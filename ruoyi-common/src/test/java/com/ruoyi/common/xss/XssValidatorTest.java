package com.ruoyi.common.xss;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class XssValidatorTest {

    private final XssValidator xssValidator = new XssValidator();
    private static final ConstraintValidatorContext VALIDATOR_CONTEXT = null;

    @Test
    @DisplayName("正常纯文本验证通过")
    void testPlainTextShouldPass() {
        // 给定
        String normalText = "这是一段正常的文本，不包含任何HTML标签";

        // 当...时
        boolean result = xssValidator.isValid(normalText, VALIDATOR_CONTEXT);

        // 那么
        assertTrue(result, "正常文本不应包含XSS风险");
    }

    @Test
    @DisplayName("合法HTML标签验证通过")
    void testValidHtmlShouldPass() {
        // 给定
        String htmlText = "这是<b>粗体</b>文本";

        // 当...时
        boolean result = xssValidator.isValid(htmlText, VALIDATOR_CONTEXT);

        // 那么
        assertTrue(result, "安全的HTML标签应该通过验证");
    }

    @Test
    @DisplayName("恶意脚本验证不通过")
    void testMaliciousScriptShouldFail() {
        // 给定
        String[] xssPayloads = {
                "<script>alert('XSS')</script>",
                "<img src=\"javascript:alert('XSS')\">",
                "<a href=\"javascript:alert('XSS')\">点击我</a>",
                "<div onload=\"alert('XSS')\">内容</div>",
                "<img src=\"x\" onerror=\"alert('XSS')\">"
        };

        // 当...时 / 那么
        for (String payload : xssPayloads) {
            assertFalse(xssValidator.isValid(payload, VALIDATOR_CONTEXT),
                    "恶意脚本应该被拦截: " + payload);
        }
    }

    @Test
    @DisplayName("包含换行符的文本验证通过")
    void testTextWithLineBreaksShouldPass() {
        // 给定
        String textWithLineBreaks = "第一行\n第二行\r\n第三行\r第四行";

        // 当...时
        boolean result = xssValidator.isValid(textWithLineBreaks, VALIDATOR_CONTEXT);

        // 那么
        assertTrue(result, "包含换行符的文本应该通过验证");
    }

    @Test
    @DisplayName("空值验证通过")
    void testEmptyOrNullShouldPass() {
        // 给定
        String emptyString = "";
        String nullString = null;

        // 当...时 / 那么
        assertTrue(xssValidator.isValid(emptyString, VALIDATOR_CONTEXT), "空字符串应该通过验证");
        assertTrue(xssValidator.isValid(nullString, VALIDATOR_CONTEXT), "null值应该通过验证");
    }

    @Test
    @DisplayName("白名单HTML标签验证通过")
    void testWhitelistedHtmlShouldPass() {
        // 给定
        String[] allowedHtmlSamples = {
                "<p>段落文本</p>",
                "<a href='https://example.com' target='_blank'>安全链接</a>",
                "<img src='data:image/png;base64,iVBORw0=' alt='测试图片'>",
                "<span style='color:red;'>红色文本</span>",
                "<div class='container'>容器</div>"
        };

        // 当...时 / 那么
        for (String html : allowedHtmlSamples) {
            assertTrue(xssValidator.isValid(html, VALIDATOR_CONTEXT),
                    "白名单中的HTML标签应该通过验证: " + html);
        }
    }
}