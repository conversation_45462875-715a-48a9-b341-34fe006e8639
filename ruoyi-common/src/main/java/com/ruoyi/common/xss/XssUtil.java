package com.ruoyi.common.xss;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * XSS清理工具类，使用jsoup实现
 *
 * <AUTHOR>
 */
public class XssUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private XssUtil() {
    }

    /**
     * 预先构建并缓存Safelist，提高性能
     */
    private static final Safelist XSS_SAFELIST = buildSafelist();

    /**
     * 换行符占位符，使用不太可能出现在正常文本中的字符序列
     */
    private static final String NEWLINE_PLACEHOLDER = "\u200B\u200C\u200D\u2060"; // 零宽字符组合
    private static final String CARRIAGE_RETURN_PLACEHOLDER = "\u200B\u200C\u200D\u2061"; // 零宽字符组合

    /**
     * 尖括号占位符，用于保护非HTML上下文中的尖括号
     */
    private static final String LESS_THAN_PLACEHOLDER = "\u200B\u200C\u200D\u2062"; // 零宽字符组合
    private static final String GREATER_THAN_PLACEHOLDER = "\u200B\u200C\u200D\u2063"; // 零宽字符组合

    /**
     * HTML标签模式 - 用于识别真正的HTML标签
     */
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile(
            "</?[a-zA-Z]\\w*(?:\\s[^>]*)?>"
    );

    /**
     * 清理XSS风险字符串
     *
     * @param html 可能含有XSS风险的字符串
     * @return 清理后的字符串
     */
    public static String clean(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }

        // 第一步：保存换行符
        String temp = html.replace("\n", NEWLINE_PLACEHOLDER)
                .replace("\r", CARRIAGE_RETURN_PLACEHOLDER);

        // 第二步：智能保护非HTML标签的尖括号
        temp = protectNonHtmlAngleBrackets(temp);

        // 第三步：执行XSS清理
        String cleaned = Jsoup.clean(temp, XSS_SAFELIST);

        // 第四步：恢复被保护的符号
        cleaned = cleaned.replace(LESS_THAN_PLACEHOLDER, "<")
                .replace(GREATER_THAN_PLACEHOLDER, ">")
                .replace(NEWLINE_PLACEHOLDER, "\n")
                .replace(CARRIAGE_RETURN_PLACEHOLDER, "\r");

        return cleaned;
    }

    /**
     * 保护非HTML标签的尖括号
     * 策略：先识别所有HTML标签，然后保护剩余的尖括号
     */
    private static String protectNonHtmlAngleBrackets(String text) {
        // 创建一个StringBuilder来构建结果
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;
        
        // 找到所有HTML标签
        Matcher htmlMatcher = HTML_TAG_PATTERN.matcher(text);
        
        while (htmlMatcher.find()) {
            // 处理HTML标签之前的文本 - 这部分的尖括号需要保护
            String beforeTag = text.substring(lastEnd, htmlMatcher.start());
            result.append(protectAngleBracketsInText(beforeTag));
            
            // 添加HTML标签本身（不保护）
            result.append(htmlMatcher.group());
            
            lastEnd = htmlMatcher.end();
        }
        
        // 处理最后一个HTML标签之后的文本
        if (lastEnd < text.length()) {
            String afterLastTag = text.substring(lastEnd);
            result.append(protectAngleBracketsInText(afterLastTag));
        }
        
        return result.toString();
    }

    /**
     * 在纯文本中保护尖括号（不包含HTML标签的文本）
     */
    private static String protectAngleBracketsInText(String text) {
        // 在纯文本中，所有的 < 和 > 都应该被保护
        return text.replace("<", LESS_THAN_PLACEHOLDER)
                  .replace(">", GREATER_THAN_PLACEHOLDER);
    }

    /**
     * 判断字符串是否含有XSS风险
     *
     * @param html 待检测的字符串
     * @return true表示安全，false表示含有XSS风险
     */
    public static boolean isValid(String html) {
        if (StringUtils.isBlank(html)) {
            return true;
        }

        // 对于验证，也需要考虑换行符和尖括号的处理
        String temp = html.replace("\n", NEWLINE_PLACEHOLDER)
                .replace("\r", CARRIAGE_RETURN_PLACEHOLDER);

        // 保护非HTML标签的尖括号
        temp = protectNonHtmlAngleBrackets(temp);

        return Jsoup.isValid(temp, XSS_SAFELIST);
    }

    /**
     * 构建一个 Xss 清理的 Safelist 规则。
     * 基于 Safelist#relaxed() 的基础上:
     * 1. 扩展支持了 style 和 class 属性
     * 2. a 标签额外支持了 target 属性
     * 3. img 标签额外支持了 data 协议，便于支持 base64
     *
     * @return Safelist
     */
    private static Safelist buildSafelist() {
        // 使用 jsoup 提供的默认的
        Safelist relaxedSafelist = Safelist.relaxed();
        // 富文本编辑时一些样式是使用 style 来进行实现的
        // 比如红色字体 style="color:red;", 所以需要给所有标签添加 style 属性
        // 注意：style 属性会有注入风险 <img STYLE="background-image:url(javascript:alert('XSS'))">
        relaxedSafelist.addAttributes(":all", "style", "class");
        // 保留 a 标签的 target 属性
        relaxedSafelist.addAttributes("a", "target");
        // 支持img 为base64
        relaxedSafelist.addProtocols("img", "src", "data");
        return relaxedSafelist;
    }
}