package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;

/**
 * 全局配置类
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "ruoyi")
public class RuoYiConfig {
    /**
     * 项目名称
     */
    private static String name;

    /**
     * 版本
     */
    private static String version;

    /**
     * 版权年份
     */
    private static String copyrightYear;

    /**
     * 实例演示开关
     */
    private static boolean demoEnabled;

    /**
     * 上传路径
     */
    private static String profile;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    public static String getName() {
        return name;
    }

    public void setName(String name) {
        RuoYiConfig.name = name;
    }

    public static String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        RuoYiConfig.version = version;
    }

    public static String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        RuoYiConfig.copyrightYear = copyrightYear;
    }

    public static boolean isDemoEnabled() {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        RuoYiConfig.demoEnabled = demoEnabled;
    }

    public void setProfile(String profile) {
        RuoYiConfig.profile = Paths.get(profile).normalize().toAbsolutePath().toString();
    }

    public static String getProfile() {
        return profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        RuoYiConfig.addressEnabled = addressEnabled;
    }

    /**
     * 获取导入上传路径
     */
    public static String getImportPath() {
        return Paths.get(getProfile(), "import").normalize().toAbsolutePath().toString();
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return Paths.get(getProfile(), "avatar").normalize().toAbsolutePath().toString();
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return Paths.get(getProfile(), "download").normalize().toAbsolutePath().toString();
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return Paths.get(getProfile(), "upload").normalize().toAbsolutePath().toString();
    }
}
