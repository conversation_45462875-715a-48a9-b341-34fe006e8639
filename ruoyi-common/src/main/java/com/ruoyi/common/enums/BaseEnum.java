package com.ruoyi.common.enums;

/**
 * 通用枚举
 *
 * <AUTHOR>
 */
public interface BaseEnum {
    /**
     * 获取枚举的编码
     *
     * @return 编码
     */
    Integer getCode();

    /**
     * 获取枚举的描述
     *
     * @return 描述
     * <AUTHOR>
     */
    String getDescription();

    /**
     * 根据编码获取枚举
     *
     * @param clazz 枚举类
     * @param code  编码
     * @return 枚举
     */
    static <E extends Enum<?> & BaseEnum> E ofCode(Class<E> clazz, Integer code) {
        E[] values = clazz.getEnumConstants();
        for (E value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
