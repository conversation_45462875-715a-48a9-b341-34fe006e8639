package com.ruoyi.common.densensitize;

import com.github.desensitization.StringDesensitization;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrValidator;

/**
 * 手机号脱敏
 *
 * <AUTHOR>
 */
public class MobileDesensitization implements StringDesensitization {
    @Override
    public String desensitize(String mobile) {
        if (StrValidator.isBlank(mobile)) {
            return StrValidator.EMPTY;
        }

        return CharSequenceUtil.hide(mobile, 3, mobile.length() - 4);
    }
}
