rem *******************************Code Start*****************************
@echo off

set "Ymd=%date:~,4%%date:~5,2%%date:~8,2%"
set "SqlFile=D:\backup\ph_%Ymd%.sql"
set "AssetsFile=D:\zyxcx_assets_%Ymd%.zip"

"C:\Program Files\MySQL\MySQL Server 5.7\bin\mysqldump.exe" --opt --port=5846 -u backup --password=6Dd7OPbA6id6Qzhz ph > %SqlFile%
7z a %AssetsFile% zyxcx_assets/**

echo open ************* > ftp.txt
echo user developer 9LMDOIiwSgFjgTFK >> ftp.txt
echo prompt off >> ftp.txt
echo put %SqlFile% >> ftp.txt
echo put %AssetsFile% >> ftp.txt
echo bye >> ftp.txt
ftp -i -n -s:ftp.txt

del ftp.txt
del %AssetsFile%

@echo on
rem *******************************Code End*****************************
