maMip:
  # 环境标识，test=测试环境，prod=正式环境
  env: "prod"
  # 合作方ID，由腾讯工程师分配
  partnerId: "50001449"
  # 合作方密钥，由腾讯工程师分配
  partnerKey: "40f8db9502fb2cdd88d1db907b3acb31"
  # 渠道号，由腾讯工程师分配
  channelNo: "AAHFetRwrS0CbiuOqb1FuEoY"
  # 城市编码，参见《对接移动医疗平台接口文档_国家局》（https://docs.qq.com/doc/DV3lxV3hSbXFudVBE）
  cityId: "650100"
  # 定点医药机构编码，参见《测试环境反馈单》
  orgCode: "H65010200028"
  # 医保支付密钥
  mipKey: "5da3959ec24dea98cf89138b12f8b316"
  # 定点医药机构小程序/H5应用ID
  orgAppId: "1GBPHSO9E00I75430B0A0000AFA54492"
  # 机构渠道编码，参见国家医保局医保移动支付反馈单
  orgChannelCode: "BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxvq4tTekl4i8uxRHhJzeiuw"
  # 公众号或小程序的appId
  appId: "wx978bb4d07d380be4"

mpMip:
  # 环境标识，test=测试环境，prod=正式环境
  env: "prod"
  # 合作方ID，由腾讯工程师分配
  partnerId: ""
  # 合作方密钥，由腾讯工程师分配
  partnerKey: ""
  # 渠道号，由腾讯工程师分配
  channelNo: ""
  # 城市编码，参见《对接移动医疗平台接口文档_国家局》（https://docs.qq.com/doc/DV3lxV3hSbXFudVBE）
  cityId: "650100"
  # 定点医药机构编码，参见《测试环境反馈单》
  orgCode: "H65010200028"
  # 医保支付密钥
  mipKey: ""
  # 定点医药机构小程序/H5应用ID
  orgAppId: ""
  # 机构渠道编码，参见国家医保局医保移动支付反馈单
  orgChannelCode: ""
  # 公众号或小程序的appId
  appId: ""