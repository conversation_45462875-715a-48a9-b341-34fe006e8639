ruoyi:
  profile: D:/zyxcx/assets

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主库数据源
      master:
        url: ******************************************************************************************************************************************************************
        driverClassName: com.mysql.cj.jdbc.Driver
        username: root
        password: 0IUt1XTNsh71wX1p
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      lis:
        # 从数据源开关/默认关闭
        enabled: true
        url: *****************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: HIS
        password: HIS
      his-order-view:
        enabled: true
        url: ********************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: dzpt
        password: dzpt
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 3000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      keep-alive: true
      pool-prepared-statements: true
  # SSL配置
  ssl:
    bundle:
      jks:
        web-server: # Bundle名称
          keystore:
            location: "D:/zyxcx/tomcat/ykdlfy.xjyqtl.cn.p12"  # 证书文件位置
            password: "xydlfyzyxcx"  # 证书密码
            type: "PKCS12"  # 证书类型
          reload-on-update: true  # 启用证书文件变更时自动重载
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false

# 开发环境配置
server:
  max-http-request-header-size: 10KB
  port: 8888    # HTTPS端口
  httpPort: 8899  # HTTP端口
  ssl:
    enabled: true
    bundle: web-server
  servlet:
    # 应用的访问路径
    context-path: /
    session:
      cookie:
        http-only: true
        secure: true
  tomcat:
    connection-timeout: 30s
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    threads:
      max: 800
      # Tomcat启动初始化的线程数，默认值25
      min-spare: 30
    basedir: D:/zyxcx/tomcat
    accesslog:
      enabled: true
      suffix: .log
      prefix: access_log
      file-date-format: .yyyy-MM-dd
      directory: logs
      pattern: "%a %t \"%r\" %s %b %D"

app:
  domain: ykdlfy.xjyqtl.cn
  baseUrl: https://ykdlfy.xjyqtl.cn:8888
  authCode: 4GCeCRCVRquc