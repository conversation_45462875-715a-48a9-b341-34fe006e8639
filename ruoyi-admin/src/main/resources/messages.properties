# Login success
user.login.success=Login successful
# Captcha error
user.jcaptcha.error=Invalid verification code
# User password error
user.password.not.match=User does not exist/Wrong password
# Role blocked
role.blocked=The role has been blocked, please contact the administrator
# User password retry count
user.password.retry.limit.count=Password input error {0} times, remaining {1} times will be locked
# User password retry limit exceeded
user.password.retry.limit.exceed=Password input error {0} times, the account has been locked, please contact the administrator
# User deleted
user.password.delete=Sorry, your account has been deleted
# User blocked
user.blocked=User has been blocked, please contact the administrator
# User does not exist
user.not.exists=User does not exist
# Login blacklist
login.blocked=Sorry, your IP has been blacklisted, please contact the administrator
# Required field
not.null=* Required field
# Length validation
length.not.valid=Length must be between {min} and {max} characters
# Username validation
user.username.not.valid=* Username must be 2-20 characters and can only contain letters, numbers, and symbols (except special characters)
# Password validation
user.password.not.valid=* Password must be 5-50 characters
# Email validation
user.email.not.valid=Invalid email format
# Mobile phone validation
user.mobile.phone.number.not.valid=Invalid mobile phone number format
# Logout success
user.logout.success=Logout successful
# Register success
user.register.success=Registration successful
# User not found
user.notfound=User not found
# Force logout
user.forcelogout=You have been forcibly logged out, please login again
# Unknown error
user.unknown.error=Unknown system error, please contact the administrator
# Upload size exceeded
upload.exceed.maxSize=The uploaded file exceeds the maximum size limit.<br/>The maximum allowed size is {0}MB
# Filename length exceeded
upload.filename.exceed.length=The filename cannot exceed {0} characters
# No permission
no.permission=You have no permission to operate this data, please contact the administrator [{0}]
# No create permission
no.create.permission=You have no permission to create this data, please contact the administrator [{0}]
# No update permission
no.update.permission=You have no permission to update this data, please contact the administrator [{0}]
# No delete permission
no.delete.permission=You have no permission to delete this data, please contact the administrator [{0}]
# No export permission
no.export.permission=You have no permission to export this data, please contact the administrator [{0}]
# No view permission
no.view.permission=You have no permission to view this data, please contact the administrator [{0}] 