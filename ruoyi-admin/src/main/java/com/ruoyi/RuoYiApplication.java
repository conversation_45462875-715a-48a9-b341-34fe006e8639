package com.ruoyi;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(
        scanBasePackages = {"com.ruoyi", "space.lzhq.ph"},
        exclude = {DataSourceAutoConfiguration.class}
)
@MapperScan(value = {"com.ruoyi.*.mapper", "space.lzhq.ph.mapper"})
public class RuoYiApplication {
    public static void main(String[] args) {
        SpringApplication.run(RuoYiApplication.class, args);
    }

}
