package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/system/dict/lite")
public class SysDictLiteController extends BaseController {

    private final ISysDictTypeService dictTypeService;
    private final ISysDictDataService dictDataService;

    public SysDictLiteController(ISysDictTypeService dictTypeService, ISysDictDataService dictDataService) {
        this.dictTypeService = dictTypeService;
        this.dictDataService = dictDataService;
    }

    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:view",
            "jubao:reporterLevel:view",
            "jubao:reportedLevel:view",
    })
    @GetMapping({
            "/type/{dictType}"
    })
    public String index(@PathVariable String dictType, ModelMap mmap) {
        mmap.put("dictType", dictTypeService.selectDictTypeByType(dictType));
        return "system/dict/lite/list";
    }

    @PostMapping("/list")
    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:list",
            "jubao:reporterLevel:list",
            "jubao:reportedLevel:list",
    })
    @ResponseBody
    public TableDataInfo list(SysDictData dictData) {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    @GetMapping("/add/{dictType}")
    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:add",
            "jubao:reporterLevel:add",
            "jubao:reportedLevel:add",
    })
    public String add(@PathVariable String dictType, ModelMap mmap) {
        mmap.put("dictType", dictType);
        return "system/dict/lite/add";
    }

    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:add",
            "jubao:reporterLevel:add",
            "jubao:reportedLevel:add",
    })
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysDictData dict) {
        List<SysDictData> list = dictDataService.selectListByType(dict.getDictType());
        int dictValue = 1;
        if (!list.isEmpty()) {
            try {
                int maxDictValue = list.stream().mapToInt(it -> Integer.parseInt(it.getDictValue())).max().getAsInt();
                dictValue = maxDictValue + 1;
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        dict.setDictValue("%06d".formatted(dictValue));
        dict.setCreateBy(getLoginName());
        return toAjax(dictDataService.insertDictData(dict));
    }

    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:edit",
            "jubao:reporterLevel:edit",
            "jubao:reportedLevel:edit",

    })
    @GetMapping("/edit/{dictCode}")
    public String edit(@PathVariable Long dictCode, ModelMap mmap) {
        mmap.put("dict", dictDataService.selectDictDataById(dictCode));
        return "system/dict/lite/edit";
    }

    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:edit",
            "jubao:reporterLevel:edit",
            "jubao:reportedLevel:edit",
    })
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysDictData dict) {
        dict.setUpdateBy(getLoginName());
        return toAjax(dictDataService.updateDictData(dict));
    }

    @Log(title = "字典数据", businessType = BusinessType.DELETE)
    @RequiresPermissions(logical = Logical.OR, value = {
            "jubao:politicalStatus:remove",
            "jubao:reporterLevel:remove",
            "jubao:reportedLevel:remove",
    })
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        dictDataService.deleteDictDataByIds(ids);
        return success();
    }

}
