package space.lzhq.ph.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import jakarta.validation.groups.Default;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 专用于测试 JSR-380 验证注解行为的测试控制器
 * <p>
 * 这个控制器仅在测试环境中使用，提供各种使用 JSR-380 验证注解的端点，
 * 用于验证参数验证逻辑的正确性。
 * <p>
 * 通过将参数验证测试集中在这些专门的测试端点上，
 * 其他业务接口的测试可以完全专注于业务逻辑，无需重复测试参数验证行为。
 * <p>
 * 测试覆盖的验证注解：
 * - 字符串验证：@NotBlank, @Size, @Email, @Pattern
 * - 数值验证：@NotNull, @Min, @Max, @DecimalMin, @Positive
 * - 日期时间验证：@Past, @Future, @PastOrPresent
 * - 集合验证：@NotEmpty, @Size
 * - 文件验证：MultipartFile 相关验证
 * - 请求体验证：@Valid, 嵌套验证
 * - 验证组：@Validated 与 groups
 * - 自定义验证：正则表达式等
 */
@RestController
@RequestMapping("/api/validation-test")
@Profile({"druid", "integration-test"})  // 只在测试环境中加载
@Validated
public class ValidationTestController {

    // ==================== 验证组定义 ====================

    /**
     * 创建操作验证组
     */
    public interface CreateGroup {
    }

    /**
     * 更新操作验证组
     */
    public interface UpdateGroup {
    }

    // ==================== 数据传输对象 ====================

    /**
     * 基础字符串验证DTO
     */
    public static class StringValidationDto {
        @NotBlank(message = "用户名不能为空")
        @Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
        private String username;

        @NotBlank(message = "邮箱不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;

        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
        private String phone;

        @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$",
                message = "身份证号格式不正确")
        private String idCard;

        // getters and setters
        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
    }

    /**
     * 数值验证DTO
     */
    public static class NumberValidationDto {
        @NotNull(message = "年龄不能为空")
        @Min(value = 0, message = "年龄不能小于0")
        @Max(value = 150, message = "年龄不能大于150")
        private Integer age;

        @DecimalMin(value = "0.01", message = "价格必须大于0.01")
        private BigDecimal price;

        @Positive(message = "数量必须为正数")
        private Integer quantity;

        @Min(value = 1, groups = CreateGroup.class, message = "创建时分类ID必须大于0")
        @NotNull(groups = UpdateGroup.class, message = "更新时分类ID不能为空")
        private Long categoryId;

        // getters and setters
        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public Long getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(Long categoryId) {
            this.categoryId = categoryId;
        }
    }

    /**
     * 日期时间验证DTO
     */
    public static class DateTimeValidationDto {
        @Past(message = "出生日期必须是过去的日期")
        private LocalDate birthDate;

        @Future(message = "预约日期必须是未来的日期")
        private LocalDateTime appointmentTime;

        @PastOrPresent(message = "创建时间必须是过去或当前时间")
        private LocalDateTime createTime;

        // getters and setters
        public LocalDate getBirthDate() {
            return birthDate;
        }

        public void setBirthDate(LocalDate birthDate) {
            this.birthDate = birthDate;
        }

        public LocalDateTime getAppointmentTime() {
            return appointmentTime;
        }

        public void setAppointmentTime(LocalDateTime appointmentTime) {
            this.appointmentTime = appointmentTime;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }
    }

    /**
     * 集合验证DTO
     */
    public static class CollectionValidationDto {
        @Size(min = 1, max = 5, message = "标签数量必须在1-5个之间")
        private List<String> tags;

        @NotEmpty(message = "权限列表不能为空")
        private List<@NotBlank(message = "权限名称不能为空") String> permissions;

        // getters and setters
        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }

        public List<String> getPermissions() {
            return permissions;
        }

        public void setPermissions(List<String> permissions) {
            this.permissions = permissions;
        }
    }

    /**
     * 嵌套验证DTO
     */
    public static class NestedValidationDto {
        @NotBlank(message = "订单号不能为空")
        private String orderNo;

        @Valid
        @NotNull(message = "用户信息不能为空")
        private StringValidationDto user;

        @Valid
        @NotNull(message = "商品信息不能为空")
        private NumberValidationDto product;

        // getters and setters
        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public StringValidationDto getUser() {
            return user;
        }

        public void setUser(StringValidationDto user) {
            this.user = user;
        }

        public NumberValidationDto getProduct() {
            return product;
        }

        public void setProduct(NumberValidationDto product) {
            this.product = product;
        }
    }

    // ==================== 基础字符串验证测试端点 ====================

    /**
     * 字符串验证测试端点 - 测试 @NotBlank, @Size, @Email, @Pattern
     */
    @PostMapping("/string-validation")
    public AjaxResult stringValidationTest(@Valid @RequestBody StringValidationDto dto) {
        return AjaxResult.success("字符串验证成功", dto);
    }

    /**
     * 单个参数字符串验证测试端点
     */
    @PostMapping("/single-string-validation")
    public AjaxResult singleStringValidationTest(
            @NotBlank(message = "名称不能为空") @RequestParam String name,
            @Email(message = "邮箱格式不正确") @RequestParam String email) {
        return AjaxResult.success("单个参数字符串验证成功");
    }

    // ==================== 数值验证测试端点 ====================

    /**
     * 数值验证测试端点 - 测试 @NotNull, @Min, @Max, @DecimalMin, @Positive
     */
    @PostMapping("/number-validation")
    public AjaxResult numberValidationTest(@Valid @RequestBody NumberValidationDto dto) {
        return AjaxResult.success("数值验证成功", dto);
    }

    /**
     * 单个参数数值验证测试端点
     */
    @PostMapping("/single-number-validation")
    public AjaxResult singleNumberValidationTest(
            @NotNull(message = "ID不能为空") @Min(value = 1, message = "ID必须大于0") @RequestParam Long id,
            @Positive(message = "页码必须为正数") @RequestParam Integer page) {
        return AjaxResult.success("单个参数数值验证成功");
    }

    // ==================== 日期时间验证测试端点 ====================

    /**
     * 日期时间验证测试端点 - 测试 @Past, @Future, @PastOrPresent
     */
    @PostMapping("/datetime-validation")
    public AjaxResult dateTimeValidationTest(@Valid @RequestBody DateTimeValidationDto dto) {
        return AjaxResult.success("日期时间验证成功", dto);
    }

    // ==================== 集合验证测试端点 ====================

    /**
     * 集合验证测试端点 - 测试 @NotEmpty, @Size
     */
    @PostMapping("/collection-validation")
    public AjaxResult collectionValidationTest(@Valid @RequestBody CollectionValidationDto dto) {
        return AjaxResult.success("集合验证成功", dto);
    }

    // ==================== 文件验证测试端点 ====================

    /**
     * 文件验证测试端点 - 测试 MultipartFile 验证
     */
    @PostMapping("/file-validation")
    public AjaxResult fileValidationTest(
            @NotNull(message = "文件不能为空") @RequestParam("file") MultipartFile file,
            @NotBlank(message = "文件描述不能为空") @RequestParam String description) {
        return AjaxResult.success("文件验证成功");
    }

    // ==================== 嵌套验证测试端点 ====================

    /**
     * 嵌套验证测试端点 - 测试 @Valid 嵌套验证
     */
    @PostMapping("/nested-validation")
    public AjaxResult nestedValidationTest(@Valid @RequestBody NestedValidationDto dto) {
        return AjaxResult.success("嵌套验证成功", dto);
    }

    // ==================== 验证组测试端点 ====================

    /**
     * 创建操作验证组测试端点
     */
    @PostMapping("/group-validation-create")
    public AjaxResult groupValidationCreateTest(@Validated(CreateGroup.class) @RequestBody NumberValidationDto dto) {
        return AjaxResult.success("创建操作验证组验证成功", dto);
    }

    /**
     * 更新操作验证组测试端点
     */
    @PutMapping("/group-validation-update")
    public AjaxResult groupValidationUpdateTest(@Validated(UpdateGroup.class) @RequestBody NumberValidationDto dto) {
        return AjaxResult.success("更新操作验证组验证成功", dto);
    }

    /**
     * 默认验证组测试端点
     */
    @PostMapping("/group-validation-default")
    public AjaxResult groupValidationDefaultTest(@Validated(Default.class) @RequestBody NumberValidationDto dto) {
        return AjaxResult.success("默认验证组验证成功", dto);
    }

    // ==================== 路径参数验证测试端点 ====================

    /**
     * 路径参数验证测试端点
     */
    @GetMapping("/path-validation/{id}")
    public AjaxResult pathValidationTest(
            @PathVariable @Min(value = 1, message = "ID必须大于0") Long id) {
        return AjaxResult.success("路径参数验证成功");
    }

    // ==================== 复合验证测试端点 ====================

    /**
     * 复合验证测试端点 - 同时测试多种验证类型
     */
    @PostMapping("/complex-validation")
    public AjaxResult complexValidationTest(
            @Valid @RequestBody NestedValidationDto requestBody,
            @NotBlank(message = "操作类型不能为空") @RequestParam(required = false) String operation,
            @Min(value = 1, message = "版本号必须大于0") @RequestParam Integer version) {
        return AjaxResult.success("复合验证成功");
    }
} 