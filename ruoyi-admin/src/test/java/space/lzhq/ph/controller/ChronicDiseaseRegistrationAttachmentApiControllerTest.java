package space.lzhq.ph.controller;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import space.lzhq.ph.BaseIntegrationTest;
import space.lzhq.ph.service.ChronicDiseaseRegistrationAttachmentService;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static space.lzhq.ph.TestHelper.*;

/**
 * 慢病申报附件API业务逻辑测试
 * <p>
 * 专注于测试慢病申报附件相关接口的业务逻辑，包括：
 * <ul>
 *   <li>/api/chronic-disease-registration-attachment/insuranceCard - 医保卡上传</li>
 *   <li>/api/chronic-disease-registration-attachment/idCard - 身份证上传（含OCR识别）</li>
 *   <li>/api/chronic-disease-registration-attachment/document - 认定资料上传</li>
 * </ul>
 * 测试内容包括文件验证、数据处理、OCR识别、错误处理等功能。
 * <p>
 * <strong>关于会话验证测试：</strong>
 * 这些接口使用了 @RequireSession 注解，其会话验证行为（如缺少Authorization头、无效Authorization头等）
 * 已在 {@link space.lzhq.ph.RequireSessionBehaviorTest} 中通过专用测试端点进行了全面测试。
 * 本测试类无需重复测试这些通用行为，专注于接口特定的业务逻辑即可。
 */
@Slf4j
@DisplayName("慢病申报附件API业务逻辑测试")
class ChronicDiseaseRegistrationAttachmentApiControllerTest extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ChronicDiseaseRegistrationAttachmentService attachmentService;

    private MockMvc mockMvc;

    // 用于跟踪测试过程中创建的附件ID，以便在测试结束后清理
    private final IdCollector attachmentIdCollector = new IdCollector("data.attachment.id");

    private IdCollector collectAttachmentId() {
        return attachmentIdCollector;
    }

    private static final String INSURANCE_CARD_ENDPOINT = "/api/chronic-disease-registration-attachment/insuranceCard";
    private static final String INSURANCE_CARD_FILENAME = "insurance-card.jpg";
    private static final String INSURANCE_CARD_UPLOAD_FAILED_MESSAGE = "医保卡上传失败，请重试";

    private static final String ID_CARD_ENDPOINT = "/api/chronic-disease-registration-attachment/idCard";
    private static final String ID_CARD_FILENAME = "id-card.jpg";
    private static final String ID_CARD_UPLOAD_FAILED_MESSAGE = "身份证上传失败，请重试";

    private static final String DOCUMENT_ENDPOINT = "/api/chronic-disease-registration-attachment/document";
    private static final String DOCUMENT_FILENAME = "document.jpg";
    private static final String DOCUMENT_UPLOAD_FAILED_MESSAGE = "认定资料上传失败，请重试";

    private static final String OCR_RESULT_PATH = "$.data.ocrResult";
    private static final String OCR_RESULT_NAME_PATH = "$.data.ocrResult.name";

    private static final String SYSTEM_ERROR_MESSAGE = "系统繁忙，请稍后重试";

    private static final String ATTACHMENT_FILENAME_PATH = "$.data.attachment.fileName";
    private static final String DATA_PATH = "$.data";
    private static final String TRACE_ID_PATH = "$.traceId";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();

        // 清空附件ID列表
        attachmentIdCollector.clear();
        log.info("开始测试，清空附件ID列表");
    }

    @AfterEach
    void tearDown() {
        List<String> createdAttachmentIds = attachmentIdCollector.getIds();
        log.info("测试结束，开始清理测试数据，共 {} 个附件需要清理", createdAttachmentIds.size());

        // 清理所有在测试过程中创建的附件
        int successCount = 0;
        for (String attachmentId : createdAttachmentIds) {
            try {
                boolean deleted = attachmentService.deleteAttachment(attachmentId);
                if (deleted) {
                    successCount++;
                    log.info("成功清理附件，ID: {}", attachmentId);
                } else {
                    log.warn("清理附件失败，ID: {}", attachmentId);
                }
            } catch (Exception e) {
                log.error("清理附件时发生异常，ID: {}", attachmentId, e);
            }
        }

        log.info("测试数据清理完成，成功清理 {}/{} 个附件", successCount, createdAttachmentIds.size());
    }


    // ==================== 正常业务流程测试 ====================

    @Test
    @DisplayName("正常上传医保卡图片 - 应该返回成功响应")
    void updateInsuranceCard_WithValidFile_ShouldReturnSuccess() throws Exception {
        // Arrange
        MockMultipartFile validImageFile = createValidJpegFile(INSURANCE_CARD_FILENAME);

        // Act & Assert
        mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                        .file(validImageFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andDo(collectAttachmentId()) // 使用 ResultHandler 提取附件ID
                .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                .andExpect(jsonPath("$.data.attachment").exists())
                .andExpect(jsonPath("$.data.attachment.id").isString())
                .andExpect(jsonPath("$.data.attachment.openId").isString())
                .andExpect(jsonPath("$.data.attachment.registrationId").isEmpty())
                .andExpect(jsonPath("$.data.attachment.type").exists())
                .andExpect(jsonPath("$.data.attachment.type.code").value("INSURANCE_CARD"))
                .andExpect(jsonPath("$.data.attachment.type.description").value("医保卡"))
                .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value(INSURANCE_CARD_FILENAME))
                .andExpect(jsonPath("$.data.attachment.fileUrl").value(allOf(startsWith("http"), containsString("://"), endsWith(".jpg"))))
                .andExpect(jsonPath("$.data.attachment.createTime").value(matchesPattern("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")));
    }

    // ==================== 文件验证测试 ====================

    @Test
    @DisplayName("上传空文件 - 应该返回错误响应")
    void updateInsuranceCard_WithEmptyFile_ShouldReturnError() throws Exception {
        // Arrange
        MockMultipartFile emptyFile = createEmptyFile("empty.jpg", MediaType.IMAGE_JPEG_VALUE);

        // Act & Assert
        mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                        .file(emptyFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, INSURANCE_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("上传不支持的文件格式 - 应该返回错误响应")
    void updateInsuranceCard_WithUnsupportedFileType_ShouldReturnError() throws Exception {
        // Arrange
        MockMultipartFile textFile = createTextFile("document.txt");

        // Act & Assert
        mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                        .file(textFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, INSURANCE_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("上传超大文件 - 应该返回错误响应")
    void updateInsuranceCard_WithOversizedFile_ShouldReturnError() throws Exception {
        // Arrange - 创建一个超大文件（假设系统限制小于这个大小）
        MockMultipartFile largeFile = createLargeFile("large-insurance-card.jpg", 15); // 15MB

        // Act & Assert
        mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                        .file(largeFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, INSURANCE_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    // ==================== 参数验证测试 ====================

    @Test
    @DisplayName("缺少文件参数 - 应该返回400错误")
    void updateInsuranceCard_WithoutFileParameter_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, SYSTEM_ERROR_MESSAGE))
                .andExpect(jsonPath(TRACE_ID_PATH).isNotEmpty());
    }

    @Test
    @DisplayName("错误的参数名称 - 应该返回400错误")
    void updateInsuranceCard_WithWrongParameterName_ShouldReturnBadRequest() throws Exception {
        // Arrange
        MockMultipartFile fileWithWrongName = createFileWithWrongParameterName("wrongName", INSURANCE_CARD_FILENAME);

        // Act & Assert
        mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                        .file(fileWithWrongName)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, SYSTEM_ERROR_MESSAGE))
                .andExpect(jsonPath(TRACE_ID_PATH).isNotEmpty());
    }

    // ==================== 并发和边界测试 ====================

    @Test
    @DisplayName("并发上传多个文件 - 应该正确处理")
    void updateInsuranceCard_ConcurrentUploads_ShouldHandleCorrectly() throws Exception {
        // Arrange
        MockMultipartFile file1 = createValidJpegFile("insurance-card-1.jpg");
        MockMultipartFile file2 = createValidJpegFile("insurance-card-2.jpg");
        CountDownLatch startLatch = new CountDownLatch(1);  // 确保任务同时开始
        CountDownLatch completionLatch = new CountDownLatch(2);  // 等待任务完成

        // 用于存储结果和异常
        AtomicReference<ResultActions> result1 = new AtomicReference<>();
        AtomicReference<ResultActions> result2 = new AtomicReference<>();
        AtomicReference<Exception> exception1 = new AtomicReference<>();
        AtomicReference<Exception> exception2 = new AtomicReference<>();

        // 用于验证并发性的时间戳
        AtomicLong startTime1 = new AtomicLong();
        AtomicLong startTime2 = new AtomicLong();

        // 使用 try-with-resources 确保 ExecutorService 正确关闭
        try (ExecutorService executor = Executors.newFixedThreadPool(2)) {
            // Act - 提交任务到线程池
            executor.submit(() -> {
                try {
                    startLatch.await();  // 等待同步开始信号
                    startTime1.set(System.nanoTime());  // 记录开始时间

                    ResultActions actions = mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                                    .file(file1)
                                    .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                                    .contentType(MediaType.MULTIPART_FORM_DATA))
                            .andDo(collectAttachmentId()); // 使用 ResultHandler 提取附件ID
                    result1.set(actions);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();  // 重新设置中断状态
                    exception1.set(e);
                } catch (Exception e) {
                    exception1.set(e);  // 显式存储异常而不包装
                } finally {
                    completionLatch.countDown();
                }
            });

            executor.submit(() -> {
                try {
                    startLatch.await();  // 等待同步开始信号
                    startTime2.set(System.nanoTime());  // 记录开始时间

                    ResultActions actions = mockMvc.perform(multipart(INSURANCE_CARD_ENDPOINT)
                                    .file(file2)
                                    .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                                    .contentType(MediaType.MULTIPART_FORM_DATA))
                            .andDo(collectAttachmentId()); // 使用 ResultHandler 提取附件ID
                    result2.set(actions);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();  // 重新设置中断状态
                    exception2.set(e);
                } catch (Exception e) {
                    exception2.set(e);  // 显式存储异常而不包装
                } finally {
                    completionLatch.countDown();
                }
            });

            // 同时启动两个任务
            startLatch.countDown();

            // 等待任务完成，设置超时防止测试挂起
            boolean completed = completionLatch.await(10, TimeUnit.SECONDS);
            assertTrue(completed, "并发上传未在预期时间内完成");

            // Assert - 检查是否有异常发生
            if (exception1.get() != null) {
                throw new AssertionError("第一个上传请求失败", exception1.get());
            }
            if (exception2.get() != null) {
                throw new AssertionError("第二个上传请求失败", exception2.get());
            }

            // 验证并发执行 - 两个请求的开始时间应该非常接近
            long timeDifferenceNanos = Math.abs(startTime1.get() - startTime2.get());
            long timeDifferenceMillis = timeDifferenceNanos / 1_000_000;
            assertTrue(timeDifferenceMillis < 100,
                    String.format("请求启动时间差过大 (%d ms)，可能未实现真正的并发执行", timeDifferenceMillis));

            // 验证两个请求都成功（附件ID已通过 ResultHandler 提取）
            result1.get()
                    .andDo(print())
                    .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                    .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value("insurance-card-1.jpg"));

            result2.get()
                    .andDo(print())
                    .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                    .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value("insurance-card-2.jpg"));

            // 确保ExecutorService正确关闭
            executor.shutdown();
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        }
    }

    // ==================== 身份证上传接口测试 ====================

    @Test
    @DisplayName("正常上传身份证图片 - 应该返回成功响应和OCR识别结果")
    void uploadIdCard_WithValidFile_ShouldReturnSuccessWithOcrResult() throws Exception {
        // Arrange
        MockMultipartFile validIdCardFile = createIdCardSampleFile(ID_CARD_FILENAME);

        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .file(validIdCardFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andDo(collectAttachmentId()) // 使用 ResultHandler 提取附件ID
                .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                // 验证附件信息
                .andExpect(jsonPath("$.data.attachment").exists())
                .andExpect(jsonPath("$.data.attachment.id").isString())
                .andExpect(jsonPath("$.data.attachment.openId").isString())
                .andExpect(jsonPath("$.data.attachment.registrationId").isEmpty())
                .andExpect(jsonPath("$.data.attachment.type").exists())
                .andExpect(jsonPath("$.data.attachment.type.code").value("ID_CARD"))
                .andExpect(jsonPath("$.data.attachment.type.description").value("身份证"))
                .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value(ID_CARD_FILENAME))
                .andExpect(jsonPath("$.data.attachment.fileUrl").value(allOf(startsWith("http"), containsString("://"), endsWith(".jpg"))))
                .andExpect(jsonPath("$.data.attachment.createTime").value(matchesPattern("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")))
                // 验证OCR识别结果
                .andExpect(jsonPath(OCR_RESULT_PATH).exists())
                .andExpect(jsonPath("$.data.ocrResult.type").value("Front"))
                .andExpect(jsonPath(OCR_RESULT_NAME_PATH).value("卢中强"))
                .andExpect(jsonPath("$.data.ocrResult.id").value("******************"))
                .andExpect(jsonPath("$.data.ocrResult.addr").value("新疆昌吉市中山路街道滨河北路129号和谐牡丹园西区15幢1单元1403室"))
                .andExpect(jsonPath("$.data.ocrResult.gender").value("男"))
                .andExpect(jsonPath("$.data.ocrResult.nationality").value("汉"))
                .andExpect(jsonPath("$.data.ocrResult.validDate").isEmpty());
    }

    @Test
    @DisplayName("上传身份证空文件 - 应该返回错误响应")
    void uploadIdCard_WithEmptyFile_ShouldReturnError() throws Exception {
        // Arrange
        MockMultipartFile emptyFile = createEmptyFile("empty.jpg", MediaType.IMAGE_JPEG_VALUE);

        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .file(emptyFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, ID_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("上传身份证不支持的文件格式 - 应该返回错误响应")
    void uploadIdCard_WithUnsupportedFileType_ShouldReturnError() throws Exception {
        // Arrange
        MockMultipartFile textFile = createTextFile("document.txt");

        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .file(textFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, ID_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("上传身份证超大文件 - 应该返回错误响应")
    void uploadIdCard_WithOversizedFile_ShouldReturnError() throws Exception {
        // Arrange - 创建一个超大文件（假设系统限制小于这个大小）
        MockMultipartFile largeFile = createLargeFile("large-id-card.jpg", 15); // 15MB

        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .file(largeFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, ID_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("身份证上传缺少文件参数 - 应该返回400错误")
    void uploadIdCard_WithoutFileParameter_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, SYSTEM_ERROR_MESSAGE))
                .andExpect(jsonPath(TRACE_ID_PATH).isNotEmpty());
    }

    @Test
    @DisplayName("身份证上传错误的参数名称 - 应该返回400错误")
    void uploadIdCard_WithWrongParameterName_ShouldReturnBadRequest() throws Exception {
        // Arrange
        MockMultipartFile fileWithWrongName = createFileWithWrongParameterName("wrongName", ID_CARD_FILENAME);

        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .file(fileWithWrongName)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, SYSTEM_ERROR_MESSAGE))
                .andExpect(jsonPath(TRACE_ID_PATH).isNotEmpty());
    }

    @Test
    @DisplayName("身份证上传并发测试 - 应该正确处理多个OCR请求")
    void uploadIdCard_ConcurrentUploads_ShouldHandleCorrectly() throws Exception {
        // Arrange
        MockMultipartFile file1 = createIdCardSampleFile("id-card-1.jpg");
        MockMultipartFile file2 = createIdCardSampleFile("id-card-2.jpg");
        CountDownLatch startLatch = new CountDownLatch(1);  // 确保任务同时开始
        CountDownLatch completionLatch = new CountDownLatch(2);  // 等待任务完成

        // 用于存储结果和异常
        AtomicReference<ResultActions> result1 = new AtomicReference<>();
        AtomicReference<ResultActions> result2 = new AtomicReference<>();
        AtomicReference<Exception> exception1 = new AtomicReference<>();
        AtomicReference<Exception> exception2 = new AtomicReference<>();

        // 用于验证并发性的时间戳
        AtomicLong startTime1 = new AtomicLong();
        AtomicLong startTime2 = new AtomicLong();

        // 使用 try-with-resources 确保 ExecutorService 正确关闭
        try (ExecutorService executor = Executors.newFixedThreadPool(2)) {
            // Act - 提交任务到线程池
            executor.submit(() -> {
                try {
                    startLatch.await();  // 等待同步开始信号
                    startTime1.set(System.nanoTime());  // 记录开始时间

                    ResultActions actions = mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                                    .file(file1)
                                    .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                                    .contentType(MediaType.MULTIPART_FORM_DATA))
                            .andDo(collectAttachmentId()); // 使用 ResultHandler 提取附件ID
                    result1.set(actions);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();  // 重新设置中断状态
                    exception1.set(e);
                } catch (Exception e) {
                    exception1.set(e);  // 显式存储异常而不包装
                } finally {
                    completionLatch.countDown();
                }
            });

            executor.submit(() -> {
                try {
                    startLatch.await();  // 等待同步开始信号
                    startTime2.set(System.nanoTime());  // 记录开始时间

                    ResultActions actions = mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                                    .file(file2)
                                    .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                                    .contentType(MediaType.MULTIPART_FORM_DATA))
                            .andDo(collectAttachmentId()); // 使用 ResultHandler 提取附件ID
                    result2.set(actions);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();  // 重新设置中断状态
                    exception2.set(e);
                } catch (Exception e) {
                    exception2.set(e);  // 显式存储异常而不包装
                } finally {
                    completionLatch.countDown();
                }
            });

            // 同时启动两个任务
            startLatch.countDown();

            // 等待任务完成，设置超时防止测试挂起
            boolean completed = completionLatch.await(15, TimeUnit.SECONDS); // OCR处理可能需要更长时间
            assertTrue(completed, "并发身份证上传未在预期时间内完成");

            // Assert - 检查是否有异常发生
            if (exception1.get() != null) {
                throw new AssertionError("第一个身份证上传请求失败", exception1.get());
            }
            if (exception2.get() != null) {
                throw new AssertionError("第二个身份证上传请求失败", exception2.get());
            }

            // 验证并发执行 - 两个请求的开始时间应该非常接近
            long timeDifferenceNanos = Math.abs(startTime1.get() - startTime2.get());
            long timeDifferenceMillis = timeDifferenceNanos / 1_000_000;
            assertTrue(timeDifferenceMillis < 100,
                    String.format("请求启动时间差过大 (%d ms)，可能未实现真正的并发执行", timeDifferenceMillis));

            // 验证两个请求都成功（附件ID已通过 ResultHandler 提取）
            result1.get()
                    .andDo(print())
                    .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                    .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value("id-card-1.jpg"))
                    .andExpect(jsonPath(OCR_RESULT_PATH).exists())
                    .andExpect(jsonPath(OCR_RESULT_NAME_PATH).isString());

            result2.get()
                    .andDo(print())
                    .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                    .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value("id-card-2.jpg"))
                    .andExpect(jsonPath(OCR_RESULT_PATH).exists())
                    .andExpect(jsonPath(OCR_RESULT_NAME_PATH).isString());

            // 确保ExecutorService正确关闭
            executor.shutdown();
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        }
    }

    @Test
    @DisplayName("身份证OCR识别失败处理 - 使用无效图片应该返回错误")
    void uploadIdCard_WithInvalidImageForOcr_ShouldReturnError() throws Exception {
        // Arrange - 使用基本的JPEG文件（无法进行OCR识别）
        MockMultipartFile invalidOcrFile = createValidJpegFile("invalid-for-ocr.jpg");

        // Act & Assert
        mockMvc.perform(multipart(ID_CARD_ENDPOINT)
                        .file(invalidOcrFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andDo(collectAttachmentId())
                .andExpect(expectErrorResponse(500, ID_CARD_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    // ==================== 认定资料上传测试 ====================

    @Test
    @DisplayName("正常上传认定资料图片 - 应该返回成功响应")
    void uploadDocument_WithValidFile_ShouldReturnSuccess() throws Exception {
        // Arrange
        MockMultipartFile validImageFile = createValidJpegFile(DOCUMENT_FILENAME);

        // Act & Assert
        mockMvc.perform(multipart(DOCUMENT_ENDPOINT)
                        .file(validImageFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andDo(collectAttachmentId()) // 使用 ResultHandler 提取附件ID
                .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                .andExpect(jsonPath("$.data.attachment").exists())
                .andExpect(jsonPath("$.data.attachment.id").isString())
                .andExpect(jsonPath("$.data.attachment.openId").isString())
                .andExpect(jsonPath("$.data.attachment.registrationId").isEmpty())
                .andExpect(jsonPath("$.data.attachment.type").exists())
                .andExpect(jsonPath("$.data.attachment.type.code").value("DOCUMENT"))
                .andExpect(jsonPath("$.data.attachment.type.description").value("认定资料"))
                .andExpect(jsonPath(ATTACHMENT_FILENAME_PATH).value(DOCUMENT_FILENAME))
                .andExpect(jsonPath("$.data.attachment.fileUrl").value(allOf(startsWith("http"), containsString("://"), endsWith(".jpg"))))
                .andExpect(jsonPath("$.data.attachment.createTime").value(matchesPattern("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")));
    }

    @Test
    @DisplayName("上传认定资料空文件 - 应该返回错误响应")
    void uploadDocument_WithEmptyFile_ShouldReturnError() throws Exception {
        // Arrange
        MockMultipartFile emptyFile = createEmptyFile("empty.jpg", MediaType.IMAGE_JPEG_VALUE);

        // Act & Assert
        mockMvc.perform(multipart(DOCUMENT_ENDPOINT)
                        .file(emptyFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, DOCUMENT_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("上传认定资料不支持的文件格式 - 应该返回错误响应")
    void uploadDocument_WithUnsupportedFileType_ShouldReturnError() throws Exception {
        // Arrange
        MockMultipartFile textFile = createTextFile("document.txt");

        // Act & Assert
        mockMvc.perform(multipart(DOCUMENT_ENDPOINT)
                        .file(textFile)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, DOCUMENT_UPLOAD_FAILED_MESSAGE))
                .andExpect(jsonPath(DATA_PATH).isEmpty());
    }

    @Test
    @DisplayName("认定资料上传缺少文件参数 - 应该返回400错误")
    void uploadDocument_WithoutFileParameter_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(multipart(DOCUMENT_ENDPOINT)
                        .header(HttpHeaders.AUTHORIZATION, VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(expectErrorResponse(500, SYSTEM_ERROR_MESSAGE))
                .andExpect(jsonPath(TRACE_ID_PATH).isNotEmpty());
    }
}
