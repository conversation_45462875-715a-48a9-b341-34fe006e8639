# Controller 层集成测试开发规范

## 🚨 重要规范

> **⚠️ 必须遵守**: 所有 Controller 层的集成测试必须放在 `ruoyi-admin/src/test/java/space/lzhq/ph/controller/` 目录下！
> 
> **❌ 禁止**: 在业务模块（如 `xydlfy-ph`）中创建 Controller 集成测试

## 📁 目录结构规范

所有 Controller 层的集成测试必须放置在 `ruoyi-admin` 模块的测试目录下：

```
ruoyi-admin/
└── src/
    └── test/
        └── java/
            └── space/
                └── lzhq/
                    └── ph/
                        └── controller/  ← Controller 集成测试专用目录
                            ├── ChronicDiseaseRegistrationApiControllerTest.java
                            ├── ChronicDiseaseRegistrationAttachmentApiControllerTest.java
                            ├── RequireSessionTestController.java
                            ├── ValidationTestController.java
                            ├── RequireSessionBehaviorTest.java
                            ├── ValidationBehaviorTest.java
                            ├── IdCollector.java
                            ├── README.md
                            └── VALIDATION_TESTING_GUIDE.md
```

## 🎯 测试分类与命名规范

### 1. 业务接口测试类
- **命名规范**: `{业务模块}ApiControllerTest.java`
- **示例**: 
  - `ChronicDiseaseRegistrationApiControllerTest.java`
  - `PatientManagementApiControllerTest.java`
  - `PaymentReconciliationApiControllerTest.java`

### 2. 专用测试控制器
- **命名规范**: `{功能}TestController.java`
- **示例**:
  - `RequireSessionTestController.java` - 会话验证测试
  - `ValidationTestController.java` - 参数验证测试

### 3. 行为测试类
- **命名规范**: `{功能}BehaviorTest.java`
- **示例**:
  - `RequireSessionBehaviorTest.java` - 会话验证行为测试
  - `ValidationBehaviorTest.java` - 参数验证行为测试

### 4. 工具类
- **命名规范**: 描述性名称
- **示例**:
  - `IdCollector.java` - ID收集工具
  - `TestDataBuilder.java` - 测试数据构建器

## 📋 开发规范

### 1. 目录位置要求
- ✅ **必须**: 所有 Controller 测试放在 `ruoyi-admin/src/test/java/space/lzhq/ph/controller/`
- ❌ **禁止**: 在业务模块（如 `xydlfy-ph`）中编写 Controller 集成测试
- ❌ **禁止**: 在其他目录创建 Controller 测试

### 2. 测试类继承要求
```java
// ✅ 正确的基类继承
public class XXXApiControllerTest extends BaseIntegrationTest {
    // 测试内容
}
```

### 3. 注解使用规范
```java
@Slf4j                                    // 日志记录
@DisplayName("XXX业务逻辑测试")              // 中文测试描述
public class XXXApiControllerTest extends BaseIntegrationTest {
    
    @Test
    @DisplayName("正常业务流程 - 应该返回成功")   // 详细的中文描述
    void methodName_WithCondition_ShouldExpectedResult() {
        // 测试实现
    }
}
```

## 🏗️ 测试架构模式

### 现代化测试架构
本项目采用**专用测试端点**模式，实现关注点分离：

1. **专用验证测试** - 统一测试所有验证逻辑
2. **业务逻辑测试** - 专注于业务功能测试
3. **零重复代码** - 避免在每个测试类中重复验证测试

详细信息参见：
- [会话验证测试架构](./README.md#controller-测试架构指南)
- [参数验证测试架构](./VALIDATION_TESTING_GUIDE.md)

## 慢病申报附件API集成测试

### 概述

本目录包含了慢病申报附件API的完整集成测试套件，专门测试以下接口：

- `POST /api/chronic-disease-registration-attachment/InsuranceCard` - 医保卡上传接口
- `POST /api/chronic-disease-registration-attachment/idCard` - 身份证上传接口（含OCR识别）

## 测试覆盖范围

### 功能测试

- ✅ **正常上传流程** - 验证有效图片文件的上传流程
- ✅ **多种图片格式** - 支持 JPEG、PNG 等格式
- ✅ **文件验证** - 验证文件格式、大小等限制
- ✅ **会话验证** - 验证Authorization头的处理
- ✅ **OCR识别** - 验证身份证OCR识别功能
- ✅ **OCR错误处理** - 验证OCR识别失败的处理

### 安全测试

- ✅ **身份验证** - 缺少或无效的Authorization头
- ✅ **输入验证** - 文件格式、大小、参数验证
- ✅ **错误处理** - 各种异常情况的处理

### 边界测试

- ✅ **空文件上传** - 处理空文件的情况
- ✅ **超大文件** - 测试文件大小限制
- ✅ **并发请求** - 测试并发上传处理

## 测试结构

```
ChronicDiseaseRegistrationAttachmentApiControllerTest
 ├── 医保卡上传测试
 │   ├── uploadInsuranceCard_WithValidFile_ShouldReturnSuccess()           # 正常上传
 │   ├── uploadInsuranceCard_WithEmptyFile_ShouldReturnError()             # 空文件
 │   ├── uploadInsuranceCard_WithUnsupportedFileType_ShouldReturnError()   # 不支持格式
 │   ├── uploadInsuranceCard_WithOversizedFile_ShouldReturnError()         # 超大文件
 │   ├── uploadInsuranceCard_WithoutFileParameter_ShouldReturnBadRequest() # 缺少文件参数
 │   ├── uploadInsuranceCard_WithWrongParameterName_ShouldReturnBadRequest() # 错误参数名
 │   └── uploadInsuranceCard_ConcurrentUploads_ShouldHandleCorrectly()     # 并发上传
 └── 身份证上传测试（含OCR）
     ├── uploadIdCard_WithValidFile_ShouldReturnSuccessWithOcrResult()    # 正常上传+OCR
     ├── uploadIdCard_WithEmptyFile_ShouldReturnError()                   # 空文件
     ├── uploadIdCard_WithUnsupportedFileType_ShouldReturnError()         # 不支持格式
     ├── uploadIdCard_WithOversizedFile_ShouldReturnError()               # 超大文件
     ├── uploadIdCard_WithoutFileParameter_ShouldReturnBadRequest()       # 缺少文件参数
     ├── uploadIdCard_WithWrongParameterName_ShouldReturnBadRequest()     # 错误参数名
     ├── uploadIdCard_ConcurrentUploads_ShouldHandleCorrectly()           # 并发OCR处理
     └── uploadIdCard_WithInvalidImageForOcr_ShouldReturnError()          # OCR识别失败
```

## 运行测试

### 前置条件

1. **Java 11+** - 确保安装了Java 11或更高版本
2. **Maven/Gradle** - 确保构建工具正确配置
3. **测试数据库** - 确保H2内存数据库依赖可用

### 运行方式

#### 使用Maven运行

```bash
# 运行所有测试
mvn test

# 只运行集成测试
mvn test -Dtest=ChronicDiseaseRegistrationAttachmentApiControllerTest

# 运行特定测试方法
mvn test -Dtest=ChronicDiseaseRegistrationAttachmentApiControllerTest#uploadInsuranceCard_WithValidFile_ShouldReturnSuccess

# 只运行身份证上传相关测试
mvn test -Dtest=ChronicDiseaseRegistrationAttachmentApiControllerTest#uploadIdCard*
```

#### 使用Gradle运行

```bash
# 运行所有测试
./gradlew test

# 只运行集成测试
./gradlew test --tests "ChronicDiseaseRegistrationAttachmentApiControllerTest"

# 运行特定测试方法
./gradlew test --tests "ChronicDiseaseRegistrationAttachmentApiControllerTest.uploadInsuranceCard_WithValidFile_ShouldReturnSuccess"

# 只运行身份证上传相关测试
./gradlew test --tests "ChronicDiseaseRegistrationAttachmentApiControllerTest.uploadIdCard*"
```

#### 在IDE中运行

在IntelliJ IDEA或Eclipse中：

1. 右键点击测试类或测试方法
2. 选择 "Run Test" 或 "Debug Test"

## 测试配置

### 环境配置

测试使用独立的配置文件 `application-test.yml`：

```yaml
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 15MB
```

### 测试工具类

`TestHelper` 类提供了以下功能：

- 创建各种格式的测试文件
- 生成有效/无效的会话ID
- 提供常用的断言辅助方法

## 测试数据

### 有效的测试数据

- **会话ID**: `TEST-3e37-471a-a148-d7a8eaf58de4`
- **图片格式**: JPEG, PNG, GIF
- **文件大小**: < 10MB
- **身份证样本**: `idcard_sample.jpeg` (可进行OCR识别)

### 无效的测试数据

- **无效会话**: `invalid-session-id`
- **不支持格式**: TXT, PDF, DOC
- **超大文件**: > 10MB
- **无效OCR图片**: 基本JPEG文件（无法识别身份证信息）

## 预期结果

### 成功响应 (200 OK)

#### 医保卡上传成功响应

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "attachment": {
      "id": "attachment-id",
      "fileName": "insurance-card.jpg",
      "fileUrl": "http://example.com/uploads/...",
      "type": {
        "code": "INSURANCE_CARD",
        "description": "医保卡"
      }
    }
  }
}
```

#### 身份证上传成功响应（含OCR结果）

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "attachment": {
      "id": "attachment-id",
      "fileName": "id-card.jpg",
      "fileUrl": "http://example.com/uploads/...",
      "type": {
        "code": "ID_CARD",
        "description": "身份证"
      }
    },
    "ocrResult": {
      "type": "Front",
      "name": "苏辉",
      "id": "511024199009308597",
      "addr": "福建省泉州市丰泽区清源街道普明社区976号",
      "gender": "男",
      "nationality": "汉",
      "validDate": null
    }
  }
}
```

### 错误响应 (400/401/500)

```json
{
  "code": 500,
  "msg": "具体的错误信息"
}
```

## 故障排除

### 常见问题

1. **测试数据库连接失败**
    - 检查H2数据库依赖是否正确配置
    - 确认测试配置文件路径正确

2. **文件上传失败**
    - 检查文件大小限制配置
    - 确认临时目录权限

3. **会话验证失败**
    - 确认会话管理服务正确配置
    - 检查测试中的会话ID是否有效

4. **OCR识别失败**
    - 确认微信OCR服务配置正确
    - 检查身份证样本文件是否存在于 `src/test/resources/idcard_sample.jpeg`
    - 验证网络连接是否正常（OCR需要调用外部服务）

### 调试技巧

1. **启用详细日志**
   ```yaml
   logging:
     level:
       space.lzhq.ph: DEBUG
       org.springframework.web: DEBUG
   ```

2. **使用断点调试**
    - 在关键方法设置断点
    - 检查请求参数和响应内容

3. **查看测试输出**
    - 使用 `.andDo(print())` 查看详细的HTTP请求/响应

## 维护指南

### 添加新测试

1. 在测试类中添加新的测试方法
2. 使用 `@Test` 和 `@DisplayName` 注解
3. 遵循 Arrange-Act-Assert 模式
4. 更新本文档的测试覆盖范围

### 更新测试数据

1. 在 `TestHelper` 类中添加新的工具方法
2. 更新测试配置文件
3. 确保向后兼容性

### 性能优化

1. 使用内存数据库进行快速测试
2. 避免在测试中使用真实的外部服务
3. 合理使用 `@Transactional` 注解进行数据回滚

## 相关文档

- [Spring Boot Testing Reference](https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-testing)
- [JUnit 5 User Guide](https://junit.org/junit5/docs/current/user-guide/)
- [MockMvc Documentation](https://docs.spring.io/spring-framework/docs/current/reference/html/testing.html#spring-mvc-test-framework)

## 贡献指南

1. 所有新测试必须包含中文注释
2. 遵循现有的命名约定
3. 确保测试的独立性和可重复性
4. 更新相关文档

# Controller 测试架构指南

## 🎯 测试架构概述

本项目采用了创新的测试架构来处理 `@RequireSession` 注解的测试问题。通过**专用测试端点**的方式，实现了会话验证逻辑与业务逻辑测试的完全分离。

## 🔧 核心组件

### 1. 专用测试控制器

**`RequireSessionTestController`** - 专门用于测试会话验证的控制器

- 🎯 **仅限测试环境**：使用 `@Profile({"test", "integration-test"})` 确保只在测试中加载
- 🔗 **多种端点**：提供各种使用 `@RequireSession` 注解的测试端点
- 📋 **全面覆盖**：涵盖不同HTTP方法、客户端类型、请求格式

### 2. 会话验证测试类

**`RequireSessionBehaviorTest`** - 专门测试所有 `@RequireSession` 注解行为

- ✅ **完整覆盖**：测试所有可能的会话验证场景
- 🎯 **专一性**：只关注会话验证，无业务逻辑干扰
- 📝 **一次性**：验证通过后，其他测试无需重复

### 3. 业务逻辑测试类

**业务接口测试类** - 专注于各自的业务逻辑

- 🚀 **轻量化**：无需关心会话验证测试
- 🎯 **专注性**：100% 专注于业务逻辑
- 🔄 **高效性**：避免重复的会话验证测试

## 📊 测试覆盖矩阵

### RequireSessionBehaviorTest 测试覆盖

| 测试维度               | 覆盖内容                   | 状态 |
|--------------------|------------------------|----|
| **HTTP方法**         | GET, POST, PUT, DELETE | ✅  |
| **请求类型**           | JSON, 文件上传, 带参数        | ✅  |
| **客户端类型**          | ANY, WEIXIN, ALIPAY    | ✅  |
| **Authorization头** | 缺失, 无效, 有效             | ✅  |
| **错误响应**           | 统一的会话无效错误              | ✅  |

### 业务逻辑测试覆盖（以慢病申报附件为例）

| 测试分类     | 测试内容          | 状态 |
|----------|---------------|----|
| **正常流程** | 文件上传成功流程      | ✅  |
| **文件验证** | 空文件、格式验证、大小限制 | ✅  |
| **参数验证** | 必需参数、参数格式     | ✅  |
| **错误处理** | 业务异常、系统异常     | ✅  |
| **并发测试** | 多用户同时上传       | ✅  |

## 🚀 新的开发模式

### ❌ 旧模式（已废弃）

```java

@Test
void businessMethod_WithoutAuthorizationHeader_ShouldReturnUnauthorized() {
    // 每个业务接口都要重复测试会话验证...
    // 大量重复代码
    // 维护困难
}

@Test
void businessMethod_WithInvalidAuthorizationHeader_ShouldReturnSessionError() {
    // 同样的重复测试...
}

@Test
void businessMethod_NormalFlow_ShouldReturnSuccess() {
    // 真正的业务逻辑测试
}
```

### ✅ 新模式（推荐）

#### 1. 会话验证测试（一次性，覆盖所有接口）

```java
// RequireSessionBehaviorTest.java
@Test
void getRequest_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() {
    // 使用专用测试端点 /api/require-session-test/basic
    // 一次测试，覆盖所有使用 @RequireSession 的接口
}
```

#### 2. 业务逻辑测试（专注，高效）

```java
// ChronicDiseaseRegistrationAttachmentApiControllerTest.java
/**
 * 业务逻辑测试 - 无需关心会话验证
 * @RequireSession 的行为已在 RequireSessionBehaviorTest 中验证
 */
@Test
void uploadInsuranceCard_WithValidFile_ShouldReturnSuccess() {
    // 直接使用有效的Authorization头
    // 专注于测试文件上传的业务逻辑
    mockMvc.perform(multipart("/api/chronic-disease-registration-attachment/InsuranceCard")
                    .file(validFile)
                    .header("Authorization", VALID_SESSION_ID))  // 简单直接
            .andExpect(jsonPath("$.data.attachment.type.code").value("INSURANCE_CARD"));
}
```

## 📝 开发指南

### 1. 新建业务接口测试

```java
/**
 * XXX业务逻辑测试
 * <p>
 * <strong>关于会话验证：</strong>
 * 本接口使用 @RequireSession 注解，相关会话验证行为已在
 * {@link RequireSessionBehaviorTest} 中通过专用测试端点验证。
 * 本测试专注于业务逻辑。
 */
@DisplayName("XXX业务逻辑测试")
public class XXXControllerTest extends BaseIntegrationTest {

    @Test
    @DisplayName("正常业务流程")
    void normalBusinessFlow_ShouldReturnSuccess() {
        // 只需要使用 VALID_SESSION_ID，专注测试业务逻辑
        mockMvc.perform(post("/api/xxx")
                        .header("Authorization", TestHelper.VALID_SESSION_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    // 专注于业务参数验证、错误处理、边界条件等
    // 完全不需要测试会话验证相关逻辑
}
```

### 2. 迁移现有测试

如果你正在更新现有的测试类：

1. **删除会话验证测试**：
   ```java
   // ❌ 删除这些测试方法
   void testWithoutAuthorizationHeader() { ... }
   void testWithInvalidAuthorizationHeader() { ... }
   ```

2. **更新类注释**：
   ```java
   /**
    * <strong>关于会话验证：</strong>
    * 已在 RequireSessionBehaviorTest 中验证，本测试专注业务逻辑。
    */
   ```

3. **保留业务逻辑测试**：
   ```java
   // ✅ 保留并完善这些测试
   void testBusinessLogic() { ... }
   void testParameterValidation() { ... }
   void testErrorHandling() { ... }
   ```

## 🛠️ 工具和配置

### TestHelper 工具类

```java
// 会话相关
TestHelper.VALID_SESSION_ID     // 有效会话ID
TestHelper.INVALID_SESSION_ID   // 无效会话ID

// 文件创建
TestHelper.

createValidJpegFile("test.jpg")
TestHelper.

createValidPngFile("test.png")
TestHelper.

createEmptyFile("empty.jpg","...")
TestHelper.

createLargeFile("large.jpg",15)  // 15MB
```

### 专用测试端点

```java
// RequireSessionTestController 提供的测试端点
GET  /api/require-session-test/basic           // 基础GET测试
POST /api/require-session-test/basic           // 基础POST测试
POST /api/require-session-test/upload          // 文件上传测试
POST /api/require-session-test/weixin          // 微信客户端测试
POST /api/require-session-test/alipay          // 支付宝客户端测试
PUT  /api/require-session-test/update          // PUT请求测试
DELETE /api/require-session-test/delete/{id}   // DELETE请求测试
```

## 🎉 优势总结

### 1. 📈 提升效率

- **无重复代码**：会话验证逻辑只需测试一次
- **专注业务**：业务测试100%专注于业务逻辑
- **快速开发**：新接口测试无需考虑会话验证

### 2. 🔧 易于维护

- **集中管理**：会话验证逻辑变更只需更新一处
- **清晰职责**：测试类职责明确，代码更清晰
- **统一标准**：所有接口遵循相同的测试模式

### 3. 🎯 完整覆盖

- **全面测试**：专用端点覆盖所有会话验证场景
- **真实环境**：测试真实的注解处理逻辑
- **零遗漏**：不会因为重复而遗漏某些测试场景

### 4. 🚀 性能优化

- **减少测试数量**：大幅减少重复的会话验证测试
- **提高执行速度**：测试执行更快更高效
- **降低资源消耗**：减少不必要的HTTP请求

## 📋 检查清单

### Controller 测试规范检查清单

在开发 Controller 集成测试时，请确保：

#### 📁 目录结构要求
- [ ] 测试类放在 `ruoyi-admin/src/test/java/space/lzhq/ph/controller/` 目录下
- [ ] 不在业务模块（如 `xydlfy-ph`）中创建 Controller 测试
- [ ] 遵循统一的命名规范

#### 🏗️ 测试架构要求  
- [ ] 继承 `BaseIntegrationTest` 基类
- [ ] 使用 `@DisplayName` 提供中文测试描述
- [ ] 使用 `@Slf4j` 进行日志记录

#### 🎯 专用测试端点架构
- [ ] `RequireSessionTestController` 使用了 `@Profile` 注解
- [ ] `RequireSessionBehaviorTest` 覆盖了所有必要的会话验证场景
- [ ] 业务接口测试类移除了所有会话验证相关测试
- [ ] 业务接口测试类添加了说明注释
- [ ] 所有测试都使用 `TestHelper.VALID_SESSION_ID`
- [ ] 更新了相关文档和注释

#### 📝 代码质量要求
- [ ] 测试方法使用英文命名，格式为 `methodName_WithCondition_ShouldExpectedResult`
- [ ] 测试描述使用中文，清晰描述测试场景
- [ ] 包含适当的数据清理逻辑
- [ ] 使用 `IdCollector` 等工具类收集测试创建的资源ID

---

通过这个创新的测试架构，我们实现了：

- 🎯 **测试专注化**：每个测试类专注于特定职责
- 🔄 **零重复代码**：会话验证逻辑统一测试
- 📈 **高维护性**：清晰的架构和文档
- ⚡ **高效执行**：减少冗余测试，提升速度

这是一个**可扩展**、**易维护**、**高效率**的现代化测试架构！🚀 