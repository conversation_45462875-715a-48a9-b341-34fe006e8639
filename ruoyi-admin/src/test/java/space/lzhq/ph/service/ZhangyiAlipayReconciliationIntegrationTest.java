package space.lzhq.ph.service;

import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import space.lzhq.ph.BaseIntegrationTest;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

class ZhangyiAlipayReconciliationIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private IZhangyiAlipayReconciliationService reconciliationService;

    @Disabled
    void testReconcileSingleDate() {
        // Given
        LocalDate testDate = LocalDate.of(2024, 1, 1);

        // When & Then
        assertDoesNotThrow(() -> {
            reconciliationService.reconcile(testDate);
            logTestExecution("掌医支付宝对账 - " + testDate);
        });
    }

    @Disabled("长时间运行的批量测试，通常只在需要时手动执行")
    void testReconcileBatch() {
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 24);

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            final LocalDate dateToProcess = currentDate;
            assertDoesNotThrow(() -> {
                reconciliationService.reconcile(dateToProcess);
                logTestExecution("批量对账 - " + dateToProcess);
            });
            currentDate = currentDate.plusDays(1);
        }
    }
}
