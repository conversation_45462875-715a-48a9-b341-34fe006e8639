package space.lzhq.ph.service;

import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import space.lzhq.ph.BaseIntegrationTest;

import java.time.LocalDate;

class GuoguangAtmReconciliationTest extends BaseIntegrationTest {

    @Autowired
    private IGuoguangAtmReconciliationService reconciliationService;

    @Disabled
    void testReconcile() {
        LocalDate startDate = LocalDate.of(2024, 12, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 24);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            reconciliationService.reconcile(date);
        }
    }
}