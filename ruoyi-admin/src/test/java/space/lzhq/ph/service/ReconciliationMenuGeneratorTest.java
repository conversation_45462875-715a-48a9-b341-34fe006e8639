package space.lzhq.ph.service;

import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.system.service.ISysMenuService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import space.lzhq.ph.BaseIntegrationTest;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class ReconciliationMenuGeneratorTest extends BaseIntegrationTest {

    @Autowired
    private ISysMenuService menuService;

    private SysMenu groupMenu;

    @BeforeEach
    public void beforeEach() {
        groupMenu = menuService.selectMenuByParentIdAndName(0L, "对账管理");
        assertNotNull(groupMenu);
    }

    @Disabled
    void generate_menu_for_zhangyi_weixin_reconciliation() {
        SysMenu menu = menuService.selectMenuByParentIdAndName(groupMenu.getMenuId(), "微信小程序对账");
        if (menu == null) {
            menu = createMenu(groupMenu.getMenuId(), "微信小程序对账", "30", "/ph/zhangyi_weixin_reconciliation", "ph:zhangyi_weixin_reconciliation:view");
        }

        createSubMenuIfNotExists(menu.getMenuId(), "对账", "1", "ph:zhangyi_weixin_reconciliation:reconcile");
        createSubMenuIfNotExists(menu.getMenuId(), "下载微信账单", "2", "ph:zhangyi_weixin_reconciliation:downloadWeixinBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载HIS账单", "3", "ph:zhangyi_weixin_reconciliation:downloadHisBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载对账明细", "4", "ph:zhangyi_weixin_reconciliation:downloadReconciliationDetails");
        createSubMenuIfNotExists(menu.getMenuId(), "下载错单明细", "5", "ph:zhangyi_weixin_reconciliation:downloadNotBalancedReconciliationDetails");
    }

    @Disabled
    void generate_menu_for_zhangyi_alipay_reconciliation() {
        SysMenu menu = menuService.selectMenuByParentIdAndName(groupMenu.getMenuId(), "支付宝小程序对账");
        if (menu == null) {
            menu = createMenu(groupMenu.getMenuId(), "支付宝小程序对账", "31", "/ph/zhangyi_alipay_reconciliation", "ph:zhangyi_alipay_reconciliation:view");
        }

        createSubMenuIfNotExists(menu.getMenuId(), "对账", "1", "ph:zhangyi_alipay_reconciliation:reconcile");
        createSubMenuIfNotExists(menu.getMenuId(), "下载支付宝账单", "2", "ph:zhangyi_alipay_reconciliation:downloadAlipayBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载HIS账单", "3", "ph:zhangyi_alipay_reconciliation:downloadHisBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载对账明细", "4", "ph:zhangyi_alipay_reconciliation:downloadReconciliationDetails");
        createSubMenuIfNotExists(menu.getMenuId(), "下载错单明细", "5", "ph:zhangyi_alipay_reconciliation:downloadNotBalancedReconciliationDetails");
    }

    @Disabled
    void generate_menu_for_guoguang_atm_reconciliation() {
        SysMenu menu = menuService.selectMenuByParentIdAndName(groupMenu.getMenuId(), "国光自助机对账");
        if (menu == null) {
            menu = createMenu(groupMenu.getMenuId(), "国光自助机对账", "32", "/ph/guoguang_atm_reconciliation", "ph:guoguang_atm_reconciliation:view");
        }

        createSubMenuIfNotExists(menu.getMenuId(), "对账", "1", "ph:guoguang_atm_reconciliation:reconcile");
        createSubMenuIfNotExists(menu.getMenuId(), "下载自助机账单", "2", "ph:guoguang_atm_reconciliation:downloadAtmBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载HIS账单", "3", "ph:guoguang_atm_reconciliation:downloadHisBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载对账明细", "4", "ph:guoguang_atm_reconciliation:downloadReconciliationDetails");
        createSubMenuIfNotExists(menu.getMenuId(), "下载错单明细", "5", "ph:guoguang_atm_reconciliation:downloadNotBalancedReconciliationDetails");
    }

    @Disabled
    void generate_menu_for_ccb_pos_reconciliation() {
        SysMenu menu = menuService.selectMenuByParentIdAndName(groupMenu.getMenuId(), "建行POS对账");
        if (menu == null) {
            menu = createMenu(groupMenu.getMenuId(), "建行POS对账", "33", "/ph/ccb_pos_reconciliation", "ph:ccb_pos_reconciliation:view");
        }

        createSubMenuIfNotExists(menu.getMenuId(), "对账", "1", "ph:ccb_pos_reconciliation:reconcile");
        createSubMenuIfNotExists(menu.getMenuId(), "下载POS账单", "2", "ph:ccb_pos_reconciliation:downloadPosBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载HIS账单", "3", "ph:ccb_pos_reconciliation:downloadHisBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载对账明细", "4", "ph:ccb_pos_reconciliation:downloadReconciliationDetails");
        createSubMenuIfNotExists(menu.getMenuId(), "下载错单明细", "5", "ph:ccb_pos_reconciliation:downloadNotBalancedReconciliationDetails");
    }

    private SysMenu createMenu(Long parentId, String menuName, String orderNum, String url, String perms) {
        SysMenu menu = new SysMenu();
        menu.setParentId(parentId);
        menu.setMenuName(menuName);
        menu.setOrderNum(orderNum);
        menu.setUrl(url);
        menu.setTarget("menuItem");
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setPerms(perms);
        menu.setIcon("#");
        menu.setCreateBy("developer");
        menu.setCreateTime(new Date());
        menu.setIsRefresh("1");
        menuService.insertMenu(menu);
        return menu;
    }

    private void createSubMenuIfNotExists(Long parentId, String menuName, String orderNum, String perms) {
        SysMenu subMenu = menuService.selectMenuByParentIdAndName(parentId, menuName);
        if (subMenu == null) {
            subMenu = new SysMenu();
            subMenu.setParentId(parentId);
            subMenu.setMenuName(menuName);
            subMenu.setOrderNum(orderNum);
            subMenu.setUrl("#");
            subMenu.setTarget("menuItem");
            subMenu.setMenuType("F");
            subMenu.setVisible("0");
            subMenu.setPerms(perms);
            subMenu.setIcon("#");
            subMenu.setCreateBy("developer");
            subMenu.setCreateTime(new Date());
            subMenu.setIsRefresh("1");
            menuService.insertMenu(subMenu);
        }
    }


    @Disabled
    void generate_menu_for_abc_pos_reconciliation() {
        SysMenu menu = menuService.selectMenuByParentIdAndName(groupMenu.getMenuId(), "农行POS对账");
        if (menu == null) {
            menu = createMenu(groupMenu.getMenuId(), "农行POS对账", "34", "/ph/abc_pos_reconciliation", "ph:abc_pos_reconciliation:view");
        }

        createSubMenuIfNotExists(menu.getMenuId(), "对账", "1", "ph:abc_pos_reconciliation:reconcile");
        createSubMenuIfNotExists(menu.getMenuId(), "下载POS账单", "2", "ph:abc_pos_reconciliation:downloadPosBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载HIS账单", "3", "ph:abc_pos_reconciliation:downloadHisBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载对账明细", "4", "ph:abc_pos_reconciliation:downloadReconciliationDetails");
        createSubMenuIfNotExists(menu.getMenuId(), "下载错单明细", "5", "ph:abc_pos_reconciliation:downloadNotBalancedReconciliationDetails");
    }

    @Disabled
    void generate_menu_for_weixin_transfer_reconciliation() {
        SysMenu menu = menuService.selectMenuByParentIdAndName(groupMenu.getMenuId(), "余额清退");
        if (menu == null) {
            menu = createMenu(groupMenu.getMenuId(), "余额清退", "35", "/ph/weixin_transfer_reconciliation", "ph:weixin_transfer_reconciliation:view");
        }

        createSubMenuIfNotExists(menu.getMenuId(), "对账", "1", "ph:weixin_transfer_reconciliation:reconcile");
        createSubMenuIfNotExists(menu.getMenuId(), "下载微信账单", "2", "ph:weixin_transfer_reconciliation:downloadWeixinBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载HIS账单", "3", "ph:weixin_transfer_reconciliation:downloadHisBill");
        createSubMenuIfNotExists(menu.getMenuId(), "下载对账明细", "4", "ph:weixin_transfer_reconciliation:downloadReconciliationDetails");
        createSubMenuIfNotExists(menu.getMenuId(), "下载错单明细", "5", "ph:weixin_transfer_reconciliation:downloadNotBalancedReconciliationDetails");
    }
}
