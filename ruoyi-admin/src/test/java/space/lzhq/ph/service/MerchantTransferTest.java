package space.lzhq.ph.service;

import com.github.binarywang.wxpay.bean.transfer.TransferBillsGetResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsRequest;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import org.junit.jupiter.api.Disabled;
import org.mospital.jackson.JacksonKit;
import org.springframework.beans.factory.annotation.Autowired;
import space.lzhq.ph.BaseIntegrationTest;

import java.util.Collections;

class MerchantTransferTest extends BaseIntegrationTest {

    @Autowired
    private WxPayService wxPayService;

    @Disabled
    void testTransfer() throws WxPayException {
        String appId = wxPayService.getConfig().getAppId();
        String outBillNo = "MZ2025011807380000";
        int amount = 100; // 单位是分
        TransferBillsRequest request = TransferBillsRequest.newBuilder()
                .appid(appId)
                .outBillNo(outBillNo)
                .transferSceneId("1009")
                .openid("omPh85eVhvt4A3NyMHJET7Bw7YuA")
                .userName("卢中强")
                .transferAmount(amount)
                .transferRemark("商家转账测试")
                .userRecvPerception("货款")
                .transferSceneReportInfos(Collections.singletonList(
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("采购商品名称")
                                .infoContent("门诊预交金清退")
                                .build()
                ))
                .build();
        TransferBillsResult result = wxPayService.getTransferService().transferBills(request);
        System.out.println(JacksonKit.INSTANCE.writeValueAsString(result));
    }

    @Disabled
    void testTransferQueryByOutBillNo() throws WxPayException {
        String outBillNo = "MZ2025011807380000";
        TransferBillsGetResult transferBillQueryResult = wxPayService.getTransferService().getBillsByOutBillNo(outBillNo);
        System.out.println(JacksonKit.INSTANCE.writeValueAsString(transferBillQueryResult));
    }

    @Disabled
    void testTransferQueryByTransferBillNo() throws WxPayException {
        String transferBillNo = "1330001213374352501180002685888281";
        TransferBillsGetResult transferBillQueryResult = wxPayService.getTransferService().getBillsByTransferBillNo(transferBillNo);
        System.out.println(JacksonKit.INSTANCE.writeValueAsString(transferBillQueryResult));
    }

}
