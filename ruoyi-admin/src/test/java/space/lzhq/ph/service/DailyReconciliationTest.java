package space.lzhq.ph.service;

import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import space.lzhq.ph.BaseIntegrationTest;

import java.time.LocalDate;

class DailyReconciliationTest extends BaseIntegrationTest {

    @Autowired
    private IZhangyiWeixinReconciliationService zhangyiWeixinReconciliationService;

    @Autowired
    private IZhangyiAlipayReconciliationService zhangyiAlipayReconciliationService;

    @Autowired
    private IGuoguangAtmReconciliationService guoguangAtmReconciliationService;

    @Autowired
    private ICcbPosReconciliationService ccbPosReconciliationService;

    @Autowired
    private IAbcPosReconciliationService abcPosReconciliationService;

    @Disabled
    void testReconcile() {
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 31);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            zhangyiWeixinReconciliationService.reconcile(date);
            System.out.println("掌医微信对账完成：" + date);

            zhangyiAlipayReconciliationService.reconcile(date);
            System.out.println("掌医支付宝对账完成：" + date);

            guoguangAtmReconciliationService.reconcile(date);
            System.out.println("国光ATM对账完成：" + date);

            ccbPosReconciliationService.reconcile(date);
            System.out.println("CCB POS对账完成：" + date);

            abcPosReconciliationService.reconcile(date);
            System.out.println("ABC POS对账完成：" + date);
        }
    }

}
