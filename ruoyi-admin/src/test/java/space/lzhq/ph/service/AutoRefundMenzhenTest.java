package space.lzhq.ph.service;

import com.ruoyi.common.constant.Constants;
import org.dromara.hutool.core.date.DatePattern;
import org.junit.jupiter.api.Disabled;
import org.mospital.bsoft.hai.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import space.lzhq.ph.BaseIntegrationTest;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.WxPayment;
import space.lzhq.ph.domain.WxRefund;
import space.lzhq.ph.pay.ccb.CcbPayServices;
import space.lzhq.ph.pay.ccb.bean.RefundParams;
import space.lzhq.ph.pay.ccb.bean.RefundResponse;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

class AutoRefundMenzhenTest extends BaseIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(AutoRefundMenzhenTest.class);

    @Autowired
    private IWxPaymentService paymentService;

    @Autowired
    private IWxRefundService refundService;

    @Disabled
    void testRefundMenzhen() {
        WxPayment queryTemplate = new WxPayment();
        LocalDateTime beginCreateTime = LocalDateTime.of(2024, 5, 1, 0, 0, 0);
        LocalDateTime endCreateTime = LocalDateTime.of(2024, 6, 1, 0, 0, 0);
        queryTemplate.setParams(Map.of(
                "beginCreateTime",
                beginCreateTime.format(DatePattern.NORM_DATETIME_FORMATTER),
                "endCreateTime",
                endCreateTime.format(DatePattern.NORM_DATETIME_FORMATTER),
                "canRefund",
                true
        ));
        queryTemplate.setWxTradeStatus(Constants.PAY_OK);
        queryTemplate.setHisTradeStatus(Constants.RECHARGE_OK);
        queryTemplate.setType(ServiceType.MZ.name());

        List<WxPayment> payments = paymentService.selectWxPaymentList(queryTemplate);
        log.info("待退款的门诊充值记录数: {}", payments.size());
        int i = 0;
        for (WxPayment payment : payments) {
            i++;
            log.info("开始退款#{}/{}: {}", i, payments.size(), payment.getZyPayNo());
            try {
                refund(payment);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            log.info("退款结束#{}/{}: {}", i, payments.size(), payment.getZyPayNo());
        }
    }

    private void refund(WxPayment payment) {
        PatientInfoResponse patientInfoResponse =
                BsoftHaiServices.INSTANCE.getPatientInfoByPatientId(payment.getPatientNo());
        if (!patientInfoResponse.isOk()) {
            log.error("查询余额失败: {}", patientInfoResponse.getMessage());
            return;
        }

        PatientInfo patientInfo = patientInfoResponse.getPatientInfo();
        BigDecimal balance = new BigDecimal(patientInfo.getCardBalance());
        if (balance.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("余额不足，无法退款");
            return;
        }

        BigDecimal unrefundAmount = PaymentKit.INSTANCE.fenToYuan(payment.getUnrefund());
        BigDecimal refundAmount = unrefundAmount.compareTo(balance) > 0 ? balance : unrefundAmount;
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("退款金额为0，无法退款");
            return;
        }
        long refundAmountCents = PaymentKit.INSTANCE.yuanToFen(refundAmount);

        String outRefundNo = PaymentKit.INSTANCE.newOrderId(ServiceType.MZ);
        MenzhenRefundForm refundForm = new MenzhenRefundForm(
                "",
                refundAmount,
                payment.getWxPayNo(),
                "20",
                outRefundNo,
                payment.getJzCardNo(),
                payment.getPatientNo(),
                "16",
                "",
                ""
        );
        MenzhenRefundResponse hisRefundResponse = BsoftHaiServices.INSTANCE.menzhenRefund(refundForm);
        String hisRefundStatus = hisRefundResponse.isOk() ? Constants.REFUND_OK + ":系统清退余额" : hisRefundResponse.getMessage();
        WxRefund refund = refundService.createAndSave(payment, refundAmount, outRefundNo, hisRefundStatus);
        if (hisRefundResponse.isOk()) {
            payment.setRemark("系统清退余额");
            paymentService.updateOnRefund(payment, refundAmountCents);
            if (refund != null) {
                RefundResponse ccbRefundResponse = CcbPayServices.INSTANCE.refund(
                        new RefundParams(
                                refund.getZyPayNo(),
                                refundAmount.doubleValue(),
                                refund.getZyRefundNo()
                        )
                );
                refundService.updateOnCcbRefund(refund, ccbRefundResponse);
            }
        }

    }

}
