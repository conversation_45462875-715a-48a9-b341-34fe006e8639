package space.lzhq.ph;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * `@RequireSession` 注解完整行为测试
 * <p>
 * 使用专门的测试端点 {@link space.lzhq.ph.controller.RequireSessionTestController}
 * 来全面测试 @RequireSession 注解的各种行为模式。
 * <p>
 * 测试覆盖：
 * 1. 基础会话验证（缺少/无效Authorization头）
 * 2. 不同HTTP方法的会话验证（GET、POST、PUT、DELETE）
 * 3. 不同客户端类型的会话验证（ANY、WEIXIN、ALIPAY）
 * 4. 不同请求类型的会话验证（JSON、文件上传、带参数等）
 * <p>
 * <strong>重要说明：</strong>
 * 通过这个测试类验证了 @RequireSession 注解的所有行为后，
 * 其他业务接口的测试类可以完全专注于业务逻辑，
 * 无需重复测试会话验证相关的通用行为。
 */
@DisplayName("@RequireSession 注解完整行为测试")
public class RequireSessionBehaviorTest extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();
    }

    // ==================== 基础会话验证测试 ====================

    @Test
    @DisplayName("GET请求 - 缺少Authorization头应返回会话无效错误")
    void getRequest_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(get("/api/require-session-test/basic"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("GET请求 - 无效Authorization头应返回会话无效错误")
    void getRequest_WithInvalidAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(get("/api/require-session-test/basic")
                        .header("Authorization", TestHelper.INVALID_SESSION_ID))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("GET请求 - 有效Authorization头应返回成功")
    void getRequest_WithValidAuthorizationHeader_ShouldReturnSuccess() throws Exception {
        mockMvc.perform(get("/api/require-session-test/basic")
                        .header("Authorization", TestHelper.VALID_SESSION_ID))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("基础会话验证成功"));
    }

    // ==================== POST请求会话验证测试 ====================

    @Test
    @DisplayName("POST请求 - 缺少Authorization头应返回会话无效错误")
    void postRequest_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(post("/api/require-session-test/basic")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("POST请求 - 无效Authorization头应返回会话无效错误")
    void postRequest_WithInvalidAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(post("/api/require-session-test/basic")
                        .header("Authorization", TestHelper.INVALID_SESSION_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("POST请求 - 有效Authorization头应返回成功")
    void postRequest_WithValidAuthorizationHeader_ShouldReturnSuccess() throws Exception {
        mockMvc.perform(post("/api/require-session-test/basic")
                        .header("Authorization", TestHelper.VALID_SESSION_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("基础会话验证成功"));
    }

    // ==================== 文件上传会话验证测试 ====================

    @Test
    @DisplayName("文件上传 - 缺少Authorization头应返回会话无效错误")
    void fileUpload_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(multipart("/api/require-session-test/upload")
                        .file(TestHelper.createValidJpegFile("test.jpg"))
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("文件上传 - 无效Authorization头应返回会话无效错误")
    void fileUpload_WithInvalidAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(multipart("/api/require-session-test/upload")
                        .file(TestHelper.createValidJpegFile("test.jpg"))
                        .header("Authorization", TestHelper.INVALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("文件上传 - 有效Authorization头应返回成功")
    void fileUpload_WithValidAuthorizationHeader_ShouldReturnSuccess() throws Exception {
        mockMvc.perform(multipart("/api/require-session-test/upload")
                        .file(TestHelper.createValidJpegFile("test.jpg"))
                        .header("Authorization", TestHelper.VALID_SESSION_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("文件上传会话验证成功"));
    }

    // ==================== 带参数请求会话验证测试 ====================

    @Test
    @DisplayName("带参数请求 - 缺少Authorization头应返回会话无效错误")
    void requestWithParams_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(post("/api/require-session-test/with-params")
                        .param("param1", "value1")
                        .param("param2", "123")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"key\": \"value\"}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    // ==================== 不同HTTP方法的会话验证测试 ====================

    @Test
    @DisplayName("PUT请求 - 缺少Authorization头应返回会话无效错误")
    void putRequest_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(put("/api/require-session-test/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("DELETE请求 - 缺少Authorization头应返回会话无效错误")
    void deleteRequest_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(delete("/api/require-session-test/delete/123"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    // ==================== 客户端类型验证测试 ====================
    // 注意：这些测试需要有效的会话数据，实际实现可能需要Mock相应的会话服务

    @Test
    @DisplayName("微信客户端验证 - 缺少Authorization头应返回会话无效错误")
    void weixinClient_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(post("/api/require-session-test/weixin")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

    @Test
    @DisplayName("支付宝客户端验证 - 缺少Authorization头应返回会话无效错误")
    void alipayClient_WithoutAuthorizationHeader_ShouldReturnSessionInvalid() throws Exception {
        mockMvc.perform(post("/api/require-session-test/alipay")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("会话无效，请重新登录"));
    }

} 