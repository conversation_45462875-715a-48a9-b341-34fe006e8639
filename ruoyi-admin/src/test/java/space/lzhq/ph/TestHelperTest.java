package space.lzhq.ph;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.web.servlet.FlashMap;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import static org.junit.jupiter.api.Assertions.*;
import static space.lzhq.ph.TestHelper.*;

/**
 * TestHelper 工具类测试
 * <p>
 * 验证 TestHelper 中的 JSON 提取工具方法是否正常工作
 */
@DisplayName("TestHelper 工具类测试")
public class TestHelperTest {

    private static final String ATTACHMENT_ID_PATH = "data.attachment.id";
    private static final String ATTACHMENT_NONEXISTENT_PATH = "data.attachment.nonExistent";

    @Test
    @DisplayName("测试提取字符串值 - 正常情况")
    void testExtractStringFromJsonPathSuccess() throws Exception {
        // Arrange
        String jsonResponse = """
                {
                    "code": 0,
                    "msg": "操作成功",
                    "data": {
                        "attachment": {
                            "id": "test-attachment-id-12345",
                            "fileName": "test.jpg",
                            "fileUrl": "https://example.com/test.jpg"
                        }
                    }
                }
                """;

        MvcResult mockResult = createMockMvcResult(jsonResponse);

        // Act & Assert
        String attachmentId = extractStringFromJsonPath(mockResult, ATTACHMENT_ID_PATH);
        assertEquals("test-attachment-id-12345", attachmentId);

        String fileName = extractStringFromJsonPath(mockResult, "data.attachment.fileName");
        assertEquals("test.jpg", fileName);

        String fileUrl = extractStringFromJsonPath(mockResult, "data.attachment.fileUrl");
        assertEquals("https://example.com/test.jpg", fileUrl);

        String msg = extractStringFromJsonPath(mockResult, "msg");
        assertEquals("操作成功", msg);
    }

    @Test
    @DisplayName("测试路径不存在的情况")
    void testExtractFromJsonPathPathNotExists() throws Exception {
        // Arrange
        String jsonResponse = """
                {
                    "code": 0,
                    "data": {
                        "attachment": {
                            "id": "test-id"
                        }
                    }
                }
                """;

        MvcResult mockResult = createMockMvcResult(jsonResponse);

        // Act & Assert
        String nonExistentValue = extractStringFromJsonPath(mockResult, ATTACHMENT_NONEXISTENT_PATH);
        assertNull(nonExistentValue);

        String deepNonExistentValue = extractStringFromJsonPath(mockResult, "data.nonExistent.field");
        assertNull(deepNonExistentValue);
    }

    @Test
    @DisplayName("测试带默认值的字符串提取")
    void testExtractStringFromJsonPathWithDefaultValue() throws Exception {
        // Arrange
        String jsonResponse = """
                {
                    "code": 0,
                    "data": {
                        "attachment": {
                            "id": "test-id"
                        }
                    }
                }
                """;

        MvcResult mockResult = createMockMvcResult(jsonResponse);

        // Act & Assert
        String existingValue = extractStringFromJsonPath(mockResult, ATTACHMENT_ID_PATH, "default");
        assertEquals("test-id", existingValue);

        String nonExistentValue = extractStringFromJsonPath(mockResult, ATTACHMENT_NONEXISTENT_PATH, "default-value");
        assertEquals("default-value", nonExistentValue);
    }

    @Test
    @DisplayName("测试检查路径是否存在")
    void testIsJsonPathExists() throws Exception {
        // Arrange
        String jsonResponse = """
                {
                    "code": 0,
                    "data": {
                        "attachment": {
                            "id": "test-id",
                            "fileName": null
                        }
                    }
                }
                """;

        MvcResult mockResult = createMockMvcResult(jsonResponse);

        // Act & Assert
        assertTrue(isJsonPathExists(mockResult, "code"));
        assertTrue(isJsonPathExists(mockResult, ATTACHMENT_ID_PATH));
        assertFalse(isJsonPathExists(mockResult, "data.attachment.fileName")); // null 值
        assertFalse(isJsonPathExists(mockResult, ATTACHMENT_NONEXISTENT_PATH));
        assertFalse(isJsonPathExists(mockResult, "nonExistent.path"));
    }

    @Test
    @DisplayName("测试无效 JSON 的情况")
    void testExtractFromJsonPathInvalidJson() throws Exception {
        // Arrange
        String invalidJson = "{ invalid json }";
        MvcResult mockResult = createMockMvcResult(invalidJson);

        // Act & Assert
        TestHelper.JsonPathExtractionException exception = assertThrows(TestHelper.JsonPathExtractionException.class, () -> extractStringFromJsonPath(mockResult, "any.path"));
        assertTrue(exception.getMessage().contains("提取 JSON 路径值失败"));
    }

    @Test
    @DisplayName("测试复杂嵌套路径")
    void testExtractFromJsonPathComplexNesting() throws Exception {
        // Arrange
        String jsonResponse = """
                {
                    "data": {
                        "user": {
                            "profile": {
                                "personal": {
                                    "name": "张三"
                                }
                            }
                        }
                    }
                }
                """;

        MvcResult mockResult = createMockMvcResult(jsonResponse);

        // Act & Assert
        String name = extractStringFromJsonPath(mockResult, "data.user.profile.personal.name");
        assertEquals("张三", name);
    }

    // ==================== ResultMatcher 工具方法测试 ====================

    @Test
    @DisplayName("expectSuccessResponse - 验证带数据的成功响应 (DataExpectation.EXISTS)")
    void expectSuccessResponse_ShouldValidateSuccessResponseWithData() throws Exception {
        // Arrange
        String successResponseJson = """
                {
                    "code": 0,
                    "msg": "操作成功",
                    "data": {
                        "id": "123",
                        "name": "test"
                    }
                }
                """;

        MvcResult result = createMockMvcResult(successResponseJson);

        // Act & Assert - 应该通过验证
        assertDoesNotThrow(() -> expectSuccessResponse(DataExpectation.EXISTS).match(result));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证无数据的成功响应 (DataExpectation.NOT_EXISTS)")
    void expectSuccessResponse_ShouldValidateSuccessResponseWithoutData() throws Exception {
        // Arrange
        String successResponseJson = """
                {
                    "code": 0,
                    "msg": "操作成功"
                }
                """;

        MvcResult result = createMockMvcResult(successResponseJson);

        // Act & Assert - 应该通过验证
        assertDoesNotThrow(() -> expectSuccessResponse(DataExpectation.NOT_EXISTS).match(result));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证忽略数据字段的成功响应 (DataExpectation.IGNORE)")
    void expectSuccessResponse_ShouldValidateSuccessResponseIgnoreData() throws Exception {
        // Arrange - 测试有数据的情况
        String successResponseWithData = """
                {
                    "code": 0,
                    "msg": "操作成功",
                    "data": {
                        "id": "123"
                    }
                }
                """;

        MvcResult resultWithData = createMockMvcResult(successResponseWithData);

        // Act & Assert - 应该通过验证
        assertDoesNotThrow(() -> expectSuccessResponse(DataExpectation.IGNORE).match(resultWithData));

        // Arrange - 测试无数据的情况
        String successResponseWithoutData = """
                {
                    "code": 0,
                    "msg": "操作成功"
                }
                """;

        MvcResult resultWithoutData = createMockMvcResult(successResponseWithoutData);

        // Act & Assert - 应该通过验证
        assertDoesNotThrow(() -> expectSuccessResponse(DataExpectation.IGNORE).match(resultWithoutData));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证错误的 code 值时应抛出异常")
    void expectSuccessResponse_ShouldThrowExceptionWhenCodeIsWrong() throws Exception {
        // Arrange
        String errorResponseJson = """
                {
                    "code": 500,
                    "msg": "操作成功",
                    "data": {
                        "id": "123"
                    }
                }
                """;

        MvcResult result = createMockMvcResult(errorResponseJson);

        // Act & Assert - 应该抛出异常
        assertThrows(AssertionError.class, () -> expectSuccessResponse(DataExpectation.EXISTS).match(result));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证错误的 msg 值时应抛出异常")
    void expectSuccessResponse_ShouldThrowExceptionWhenMsgIsWrong() throws Exception {
        // Arrange
        String errorResponseJson = """
                {
                    "code": 0,
                    "msg": "操作失败",
                    "data": {
                        "id": "123"
                    }
                }
                """;

        MvcResult result = createMockMvcResult(errorResponseJson);

        // Act & Assert - 应该抛出异常
        assertThrows(AssertionError.class, () -> expectSuccessResponse(DataExpectation.EXISTS).match(result));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证期望有数据但实际无数据时应抛出异常")
    void expectSuccessResponse_ShouldThrowExceptionWhenExpectDataButNoData() throws Exception {
        // Arrange
        String responseJson = """
                {
                    "code": 0,
                    "msg": "操作成功"
                }
                """;

        MvcResult result = createMockMvcResult(responseJson);

        // Act & Assert - 应该抛出异常
        assertThrows(AssertionError.class, () -> expectSuccessResponse(DataExpectation.EXISTS).match(result));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证期望无数据但实际有数据时应抛出异常")
    void expectSuccessResponse_ShouldThrowExceptionWhenExpectNoDataButHasData() throws Exception {
        // Arrange
        String responseJson = """
                {
                    "code": 0,
                    "msg": "操作成功",
                    "data": {
                        "id": "123"
                    }
                }
                """;

        MvcResult result = createMockMvcResult(responseJson);

        // Act & Assert - 应该抛出异常
        assertThrows(AssertionError.class, () -> expectSuccessResponse(DataExpectation.NOT_EXISTS).match(result));
    }

    @Test
    @DisplayName("expectSuccessResponse - 验证不支持的 DataExpectation 值时应抛出异常")
    void expectSuccessResponse_ShouldThrowExceptionForUnsupportedDataExpectation() throws Exception {
        // Arrange
        String responseJson = """
                {
                    "code": 0,
                    "msg": "操作成功",
                    "data": {
                        "id": "123"
                    }
                }
                """;

        MvcResult result = createMockMvcResult(responseJson);

        // 这里我们无法直接测试不支持的枚举值，因为 Java 枚举是类型安全的
        // 但我们可以测试 null 值的情况
        assertThrows(NullPointerException.class, () -> expectSuccessResponse(null).match(result));
    }

    @Test
    @DisplayName("expectErrorResponse - 应该正确验证错误响应")
    void expectErrorResponse_ShouldValidateErrorResponse() throws Exception {
        // Arrange
        String errorResponseJson = """
                {
                    "code": 500,
                    "msg": "操作失败"
                }
                """;

        MvcResult result = createMockMvcResult(errorResponseJson);

        // Act & Assert - 应该通过验证
        assertDoesNotThrow(() -> expectErrorResponse(500, "操作失败").match(result));
    }

    @Test
    @DisplayName("expectErrorResponse - 验证错误的 code 值时应抛出异常")
    void expectErrorResponse_ShouldThrowExceptionWhenCodeIsWrong() throws Exception {
        // Arrange
        String responseJson = """
                {
                    "code": 400,
                    "msg": "操作失败"
                }
                """;

        MvcResult result = createMockMvcResult(responseJson);

        // Act & Assert - 应该抛出异常
        assertThrows(AssertionError.class, () -> expectErrorResponse(500, "操作失败").match(result));
    }

    @Test
    @DisplayName("expectErrorResponse - 验证错误的 msg 值时应抛出异常")
    void expectErrorResponse_ShouldThrowExceptionWhenMsgIsWrong() throws Exception {
        // Arrange
        String responseJson = """
                {
                    "code": 500,
                    "msg": "其他错误消息"
                }
                """;

        MvcResult result = createMockMvcResult(responseJson);

        // Act & Assert - 应该抛出异常
        assertThrows(AssertionError.class, () -> expectErrorResponse(500, "操作失败").match(result));
    }

    /**
     * 创建一个模拟的 MvcResult 对象用于测试
     *
     * @param jsonResponse JSON 响应字符串
     * @return MvcResult 对象
     */
    private MvcResult createMockMvcResult(String jsonResponse) {
        try {
            MockHttpServletResponse response = new MockHttpServletResponse();
            response.setStatus(200);
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.getWriter().write(jsonResponse);
            return createMockMvcResult(response);
        } catch (Exception e) {
            throw new RuntimeException("创建模拟 MvcResult 失败", e);
        }
    }

    /**
     * 创建一个模拟的 MvcResult 对象用于测试
     *
     * @param response MockHttpServletResponse 对象
     * @return MvcResult 对象
     */
    private MvcResult createMockMvcResult(MockHttpServletResponse response) {
        return new MvcResult() {
            @Override
            public MockHttpServletRequest getRequest() {
                return new MockHttpServletRequest();
            }

            @Override
            public MockHttpServletResponse getResponse() {
                return response;
            }

            @Override
            public Object getHandler() {
                return null;
            }

            @Override
            public HandlerInterceptor[] getInterceptors() {
                return new HandlerInterceptor[0];
            }

            @Override
            public ModelAndView getModelAndView() {
                return null;
            }

            @Override
            public Exception getResolvedException() {
                return null;
            }

            @Override
            public FlashMap getFlashMap() {
                return new FlashMap();
            }

            @Override
            public Object getAsyncResult() {
                return null;
            }

            @Override
            public Object getAsyncResult(long timeToWait) {
                return null;
            }
        };
    }
} 