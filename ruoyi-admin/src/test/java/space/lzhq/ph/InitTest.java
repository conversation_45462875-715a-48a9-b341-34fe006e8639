package space.lzhq.ph;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.function.ThrowingSupplier;
import org.mospital.bsoft.BSoftService;
import org.mospital.bsoft.hai.BsoftHaiServices;
import org.mospital.bsoft.mip.BSoftMipService;
import space.lzhq.ph.pay.ccb.CcbPayServices;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class InitTest extends BaseIntegrationTest {

    /**
     * 测试服务初始化的通用方法
     *
     * @param supplier    服务初始化的函数
     * @param serviceName 服务名称，用于错误消息
     * @param <T>         服务类型
     */
    private <T> void assertServiceInitialized(String serviceName, ThrowingSupplier<T> supplier) {
        T service = assertDoesNotThrow(supplier, serviceName + " 初始化过程中出现异常");
        assertNotNull(service, serviceName + " 初始化异常");
    }

    @Test
    void shouldInitializeCcbPayServiceSuccessfully() {
        assertServiceInitialized("CcbPayServices", () -> CcbPayServices.INSTANCE.queryOrder("123456"));
    }

    @Test
    void shouldInitializeWeixinMipServiceSuccessfully() {
        assertServiceInitialized("BSoftMipService", BSoftMipService.Companion::getWEIXIN);
    }

    @Test
    void shouldInitializeHaiServiceSuccessfully() {
        assertServiceInitialized("BsoftHaiServices", () -> BsoftHaiServices.INSTANCE.getPatientInfoByJzCardNo("123456"));
    }

    @Test
    void shouldInitializeBsoftServiceSuccessfully() {
        assertServiceInitialized("BSoftService", () -> BSoftService.Companion.getPatientInfoByJzCardNo("123456"));
    }

}