---
description: commit message
globs: 
alwaysApply: false
---
# Conventional Commits 规范 AI 提示词

请根据 @Conventional Commits 1.0.0 规范，为以下代码变更生成规范化的 git 提交信息。要求如下：

1. **类型**（type）必须是规范中的类型，如：feat、fix、docs、style、refactor、test、chore、build、ci 等。
2. **可选范围**（scope）可加可不加，若加需用小括号包裹，如 feat(parser):。
3. **描述**（description）需简明扼要，说明本次更改的主要内容，首字母小写，结尾不加句号。
4. 如有**详细说明**，可在描述后空一行补充正文（body）。
5. 如有**BREAKING CHANGE**，需在类型后加 ! 或在 footer 处注明 BREAKING CHANGE: 详细说明。
6. 如有**关联 issue**，可在 footer 处注明 Refs: #123 或 Closes: #123。
7. 提交信息优先使用中文，但如果是技术术语或专有名词，请使用英文。

请严格按照如下格式输出：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**示例：**

```
feat(parser): add ability to parse arrays

support parsing of nested arrays and edge cases

Refs: #123
```

---

**请用上述格式为以下变更生成提交信息：**

【在此处补充你的代码变更内容或描述】
