# MapStruct 实现文档

## 概述

本项目使用 MapStruct 来实现实体类与 DTO 之间的映射，替代了手动编写的 `fromDomain` 方法。MapStruct 是一个代码生成器，在编译时自动生成类型安全的映射代码。

## 实现的映射器

### ChronicDiseaseRegistrationMapper

位置：`src/main/java/space/lzhq/ph/dto/mapper/ChronicDiseaseRegistrationMapper.java`

#### 功能

1. **fromPatientFormDto**: 将请求 DTO 转换为实体类
   - 自动从身份证号提取性别和出生日期
   - 使用 `@Named` 注解的自定义方法进行复杂映射

2. **toResponseDto**: 将实体类转换为响应 DTO
   - 简单的字段对字段映射
   - 自动处理所有字段的转换

#### 关键特性

- **类型安全**: 编译时检查类型匹配
- **性能优化**: 生成的代码比反射更快
- **自定义映射**: 支持复杂的字段转换逻辑
- **空值处理**: 自动处理 null 值

## 配置

### Maven 依赖

```xml
<!-- MapStruct 核心依赖 -->
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct</artifactId>
</dependency>

<!-- 注解处理器配置 -->
<annotationProcessorPaths>
    <path>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
    </path>
    <path>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok-mapstruct-binding</artifactId>
        <version>${lombok-mapstruct-binding.version}</version>
    </path>
    <path>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
    </path>
</annotationProcessorPaths>
```

### 与 Lombok 集成

项目同时使用了 Lombok 和 MapStruct，通过 `lombok-mapstruct-binding` 确保两者正确协作。

## 使用示例

### 在控制器中使用

```kotlin
@PostMapping("/patientForm")
fun patientForm(@Valid @RequestBody dto: ChronicDiseasePatientFormDto): ResponseDto<ChronicDiseaseRegistrationResponseDto?> {
    val openId = request.getClientSession()?.openId ?: return ResponseDto.error("会话无效")
    
    return try {
        val registration = registrationService.save(dto, openId).orElse(null)
            ?: return ResponseDto.error("慢病申报提交失败，请重试")
        
        // 使用 MapStruct 映射器
        val responseDto = ChronicDiseaseRegistrationMapper.INSTANCE.toResponseDto(registration)
        ResponseDto.success(responseDto)
    } catch (e: Exception) {
        logger.error("慢病申报提交失败", e)
        ResponseDto.error("慢病申报提交失败，请重试")
    }
}
```

### 在服务层中使用

```java
@Service
public class ChronicDiseaseRegistrationServiceImpl implements ChronicDiseaseRegistrationService {
    
    @Override
    public Optional<ChronicDiseaseRegistration> save(ChronicDiseasePatientFormDto dto, String openId) {
        // 使用 MapStruct 映射器转换 DTO 到实体
        ChronicDiseaseRegistration registration = ChronicDiseaseRegistrationMapper.INSTANCE.fromPatientFormDto(dto);
        
        // 设置额外字段
        registration.setOpenId(openId);
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT);
        registration.setAgentFlag(0);
        
        // 保存实体
        return Optional.of(save(registration));
    }
}
```

## 优势

1. **代码简洁**: 减少了大量样板代码
2. **类型安全**: 编译时检查，避免运行时错误
3. **性能优越**: 生成的代码比反射更快
4. **易于维护**: 当字段变更时，编译器会提示需要更新的地方
5. **可测试性**: 生成的映射器可以独立测试

## 测试

测试文件：`src/test/java/space/lzhq/ph/dto/mapper/ChronicDiseaseRegistrationMapperTest.java`

测试覆盖：
- 基本字段映射
- 自定义映射逻辑（性别和出生日期提取）
- 边界情况处理（无效身份证号）
- 空值处理

## 最佳实践

1. **使用 INSTANCE 常量**: 通过 `Mappers.getMapper()` 获取映射器实例
2. **自定义映射方法**: 使用 `@Named` 注解标记复杂的映射逻辑
3. **忽略不需要的字段**: 使用 `@Mapping(target = "field", ignore = true)`
4. **编写测试**: 为每个映射器编写单元测试
5. **文档化**: 为复杂的映射逻辑添加注释

## 注意事项

1. **编译时生成**: MapStruct 在编译时生成代码，需要重新编译才能看到变更
2. **IDE 支持**: 确保 IDE 正确配置了注解处理器
3. **循环依赖**: 避免在映射器之间创建循环依赖
4. **版本兼容**: 确保 MapStruct 版本与其他依赖兼容 