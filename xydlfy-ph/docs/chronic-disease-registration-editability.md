# 慢病申报记录可编辑性检查

本文档描述了新添加的慢病申报记录可编辑性检查功能。

## 概述

为了判断慢病申报记录是否可以被申请人或代理人编辑，我们在以下两个类中添加了新的方法：

1. `ChronicDiseaseRegistrationStatus` 枚举类：`isEditableByApplicantOrAgent()`
2. `ChronicDiseaseRegistration` 实体类：`canBeModifiedByUser()`

## 业务规则

根据业务需求，只有以下状态的慢病申报记录可以被用户编辑：

- **DRAFT**（草稿（待提交））
- **REJECTED**（审核驳回（待整改））

其他状态（SUBMITTED、APPROVED）的记录不允许用户编辑。

## API 详情

### ChronicDiseaseRegistrationStatus.isEditableByApplicantOrAgent()

**方法签名：**
```java
public boolean isEditableByApplicantOrAgent()
```

**描述：** 判断当前状态是否允许申请人或代理人编辑记录

**返回值：**
- `true` - 如果状态为 DRAFT 或 REJECTED
- `false` - 对于其他所有状态

**使用示例：**
```java
ChronicDiseaseRegistrationStatus status = ChronicDiseaseRegistrationStatus.DRAFT;
boolean editable = status.isEditableByApplicantOrAgent(); // 返回 true

status = ChronicDiseaseRegistrationStatus.SUBMITTED;
editable = status.isEditableByApplicantOrAgent(); // 返回 false
```

### ChronicDiseaseRegistration.canBeModifiedByUser()

**方法签名：**
```java
public boolean canBeModifiedByUser()
```

**描述：** 判断当前慢病申报记录是否可以被用户（申请人或代理人）修改

**返回值：**
- `true` - 如果记录的状态允许用户修改
- `false` - 如果记录的状态不允许修改或状态为 null

**使用示例：**
```java
ChronicDiseaseRegistration registration = new ChronicDiseaseRegistration();
registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT);

boolean canModify = registration.canBeModifiedByUser(); // 返回 true

registration.setStatus(ChronicDiseaseRegistrationStatus.APPROVED);
canModify = registration.canBeModifiedByUser(); // 返回 false

registration.setStatus(null);
canModify = registration.canBeModifiedByUser(); // 返回 false（安全处理 null 值）
```

## 在控制器中的使用

```java
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationUpdateDto;
import space.lzhq.ph.service.ChronicDiseaseRegistrationService;

@RestController
@RequestMapping("/api")
public class ChronicDiseaseRegistrationController {
    
    @Autowired
    private ChronicDiseaseRegistrationService registrationService;
    
    @PutMapping("/chronic-disease-registration/{id}")
    public ResponseEntity<String> updateRegistration(
            @PathVariable String id,
            @RequestBody ChronicDiseaseRegistrationUpdateDto updateDto) {
        
        // 直接调用服务层，异常处理由 @ControllerAdvice 统一处理
        registrationService.updateRegistration(id, updateDto);
        
        return ResponseEntity.ok("更新成功");
    }
}
```

## 在服务层中的使用

```java
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationUpdateDto;
import space.lzhq.ph.exception.EditNotAllowedException;

@Service
public class ChronicDiseaseRegistrationService {
    
    public void updateRegistration(String registrationId, ChronicDiseaseRegistrationUpdateDto updateDto) {
        ChronicDiseaseRegistration registration = getById(registrationId);
        
        // 验证是否可以修改，抛出域特定异常
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                "记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                "ChronicDiseaseRegistration",
                registrationId,
                registration.getStatus().getDescription()
            );
        }
        
        // 执行更新逻辑
        // ...
    }
}
```

## 状态转换说明

| 当前状态 | 可编辑 | 说明 |
|---------|--------|------|
| DRAFT（草稿） | ✅ | 用户可以修改草稿状态的记录 |
| SUBMITTED（已提交） | ❌ | 已提交的记录不允许用户修改 |
| APPROVED（审核通过） | ❌ | 已通过审核的记录不允许用户修改 |
| REJECTED（审核驳回） | ✅ | 被驳回的记录允许用户修改后重新提交 |

## 异常处理

为了提供一致的错误响应，我们使用了域特定异常和全局异常处理器：

### EditNotAllowedException

```java
import space.lzhq.ph.exception.EditNotAllowedException;

// 抛出域特定异常，包含详细的错误信息
throw new EditNotAllowedException(
    "记录当前状态不允许修改：" + registration.getStatus().getDescription(),
    "ChronicDiseaseRegistration",  // 资源类型
    registrationId,                // 资源ID
    registration.getStatus().getDescription()  // 当前状态
);
```

### 全局异常处理器

`@ControllerAdvice` 注解的 `GlobalExceptionHandler` 会自动将 `EditNotAllowedException` 映射为 HTTP 403 Forbidden 响应：

```json
{
  "timestamp": "2023-12-01T10:30:00",
  "status": 403,
  "error": "Forbidden",
  "message": "记录当前状态不允许修改：已提交（待审核）",
  "details": {
    "resourceType": "ChronicDiseaseRegistration",
    "resourceId": "abc123",
    "currentStatus": "已提交（待审核）"
  }
}
```

## 最佳实践

1. **统一使用域特定异常**
   ```java
   if (!registration.canBeModifiedByUser()) {
       throw new EditNotAllowedException(
           "记录当前状态不允许修改：" + registration.getStatus().getDescription(),
           "ChronicDiseaseRegistration",
           registrationId,
           registration.getStatus().getDescription()
       );
   }
   ```

2. **在前端界面根据状态控制编辑按钮**
   ```java
   // 在 DTO 中包含可编辑标志
   responseDto.setEditable(registration.canBeModifiedByUser());
   ```

3. **记录审计日志**
   ```java
   if (registration.canBeModifiedByUser()) {
       auditService.log("用户尝试修改记录", registrationId, userId);
       // 执行修改
   } else {
       auditService.log("用户尝试修改不可编辑记录", registrationId, userId);
       throw new EditNotAllowedException(
           "记录当前状态不允许修改：" + registration.getStatus().getDescription(),
           "ChronicDiseaseRegistration",
           registrationId,
           registration.getStatus().getDescription()
       );
   }
   ```

4. **让全局异常处理器统一处理响应格式**
   - 控制器不需要手动检查状态和返回错误响应
   - 服务层专注于业务逻辑，抛出适当的异常
   - 全局异常处理器确保一致的错误响应格式

## 注意事项

1. `canBeModifiedByUser()` 方法会安全地处理 `status` 为 `null` 的情况
2. 这些方法是纯函数，不会修改对象状态
3. 方法设计考虑了未来可能的状态扩展
4. 建议在所有修改操作前调用这些方法进行验证
5. **异常处理统一性**：
   - 服务层统一抛出 `EditNotAllowedException` 而不是 `IllegalStateException`
   - 控制器依赖全局异常处理器，不需要手动处理编辑权限检查
   - HTTP 状态码映射：编辑不允许 → 403 Forbidden，其他错误 → 500 Internal Server Error
6. **错误响应格式统一**：所有异常都通过 `GlobalExceptionHandler` 返回一致的 JSON 格式
7. **日志记录**：`EditNotAllowedException` 会被记录为 WARN 级别，其他异常记录为 ERROR 级别 