# SpringDoc 配置说明

## 概述

本文档说明了为慢病申报API添加的SpringDoc配置，包括API分组、XSS过滤排除等配置。

## 配置内容

### 1. SpringDoc API分组配置

在 `ruoyi-admin/src/main/resources/application.yml` 中添加了以下配置：

```yaml
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha
    try-it-out-enabled: true
    persist-authorization: true
    display-request-duration: true
  group-configs:
    - group: '慢病申报附件管理'
      paths-to-match: '/api/chronic-disease-registration-attachment/**'
      display-name: '慢病申报附件管理'
    - group: '慢病申报核心模块'
      paths-to-match: '/api/chronic-disease-registration/**'
      display-name: '慢病申报核心接口'
```

### 2. XSS过滤排除配置

为了确保慢病申报API能正常工作，在XSS过滤配置中添加了排除路径：

```yaml
xss:
  enabled: true
  excludes: /system/notice/*,/ph/article/*,/ph/wikiArticle/*,/api/chronic-disease-registration-attachment/*,/api/chronic-disease-registration/*
  urlPatterns: /system/*,/monitor/*,/tool/*,/ph/*,/api/jubao/*
```

## API分组说明

### 慢病申报附件管理
- **路径匹配**: `/api/chronic-disease-registration-attachment/**`
- **显示名称**: 慢病申报附件管理
- **包含接口**:
  - POST `/api/chronic-disease-registration-attachment/idCard` - 上传身份证附件
  - POST `/api/chronic-disease-registration-attachment/insuranceCard` - 上传医保卡附件

### 慢病申报核心模块
- **路径匹配**: `/api/chronic-disease-registration/**`
- **显示名称**: 慢病申报核心接口
- **包含接口**:
  - POST `/api/chronic-disease-registration/patientForm` - 提交慢病申报患者表单

## 访问方式

配置完成后，可以通过以下方式访问API文档：

1. **Swagger UI**: `http://localhost:8888/swagger-ui.html`
2. **OpenAPI JSON**: `http://localhost:8888/v3/api-docs`
3. **分组API文档**:
   - 慢病申报附件管理: `http://localhost:8888/v3/api-docs/慢病申报附件管理`
   - 慢病申报核心模块: `http://localhost:8888/v3/api-docs/慢病申报核心模块`

## 配置特性

### 1. API文档功能
- **启用状态**: 已启用API文档生成
- **文档路径**: `/v3/api-docs`
- **UI界面**: `/swagger-ui.html`

### 2. UI增强功能
- **操作排序**: 按字母顺序排序
- **标签排序**: 按字母顺序排序
- **试用功能**: 启用"Try it out"功能
- **认证持久化**: 保持认证状态
- **请求时长显示**: 显示请求执行时间

### 3. 安全配置
- **XSS过滤排除**: 排除慢病申报相关API路径
- **原因**: 这些API需要处理JSON数据和文件上传，需要排除XSS过滤

## 注意事项

1. **生产环境安全**: 在生产环境中考虑限制Swagger UI的访问
2. **认证要求**: 慢病申报核心接口需要通过Authorization请求头传入会话ID
3. **文件上传**: 附件管理接口支持multipart/form-data格式的文件上传
4. **API版本**: 使用OpenAPI 3.0规范

## 测试验证

可以通过以下方式验证配置是否正确：

1. 启动应用后访问 `http://localhost:8888/swagger-ui.html`
2. 检查是否显示两个API分组
3. 验证每个分组下的接口是否正确显示
4. 测试"Try it out"功能是否正常工作

## 相关文档

- [Swagger API 文档使用指南](./swagger-guide.md)
- [MapStruct 实现文档](./mapstruct-implementation.md) 