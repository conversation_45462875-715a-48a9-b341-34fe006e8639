# SOAP Web Service 开发指南

## 概述

本文档总结了如何使用Spring Boot和Apache CXF实现SOAP Web Service，特别是在已有WSDL文件的情况下，如何优雅地实现符合WSDL规范的服务。

## 技术栈

- Spring Boot
- Apache CXF
- JAX-WS (Jakarta XML Web Services)

## 实现步骤

### 1. 添加依赖

在`pom.xml`中添加必要的依赖：

```xml
<dependency>
    <groupId>org.apache.cxf</groupId>
    <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
    <version>${cxf.version}</version>
</dependency>
```

### 2. 定义服务接口

根据WSDL定义创建对应的接口：

```java
@WebService(targetNamespace = "http://ws.access.hai/")
public interface BSXmlWsEntryClass {
    
    @WebMethod
    String invoke(
        @WebParam(name = "service") String service,
        @WebParam(name = "urid") String urid,
        @WebParam(name = "pwd") String pwd,
        @WebParam(name = "parameter") String parameter
    );
}
```

### 3. 实现服务

创建服务实现类：

```java
@Service
@WebService(
    serviceName = "BSXmlWsEntryClassService",
    portName = "BSXmlWsEntryClassPort",
    targetNamespace = "http://ws.access.hai/",
    endpointInterface = "com.ruoyi.ph.webservice.BSXmlWsEntryClass"
)
public class BSXmlWsEntryClassImpl implements BSXmlWsEntryClass {
    
    @Override
    public String invoke(String service, String urid, String pwd, String parameter) {
        // 实现业务逻辑
    }
}
```

### 4. 配置服务发布

创建配置类发布服务：

```java
@Configuration
public class WebServiceConfig {

    @Autowired
    private Bus bus;

    @Autowired
    private BSXmlWsEntryClassImpl bsXmlWsEntryClass;

    @Bean
    public ServletRegistrationBean<CXFServlet> cxfServlet() {
        return new ServletRegistrationBean<>(new CXFServlet(), "/hai/*");
    }

    @Bean
    public Endpoint endpoint() {
        EndpointImpl endpoint = new EndpointImpl(bus, bsXmlWsEntryClass);
        endpoint.publish("/WebServiceEntry");
        return endpoint;
    }
}
```

## 服务访问

- WSDL访问地址：`http://localhost:8080/hai/WebServiceEntry?wsdl`
- 服务调用地址：`http://localhost:8080/hai/WebServiceEntry`

## 测试方法

使用curl测试服务：

```bash
curl --location 'http://localhost:8080/hai/WebServiceEntry' \
--header 'Content-Type: text/xml' \
--data '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.access.hai/">
   <soapenv:Header/>
   <soapenv:Body>
      <ws:invoke>
         <service>getPatientInfo</service>
         <urid>testUser</urid>
         <pwd>testPassword</pwd>
         <parameter>{"patientId": "12345"}</parameter>
      </ws:invoke>
   </soapenv:Body>
</soapenv:Envelope>'
```

## 最佳实践

1. **接口设计**
   - 使用`@WebService`和`@WebMethod`注解明确定义服务接口
   - 保持方法签名与WSDL一致
   - 使用`@WebParam`注解明确参数名称

2. **实现类**
   - 使用`@Service`注解将实现类注册为Spring Bean
   - 在`@WebService`注解中指定完整的服务信息
   - 实现良好的错误处理和日志记录

3. **配置**
   - 使用CXFServlet配置服务路径
   - 使用EndpointImpl发布服务
   - 保持URL路径结构清晰

4. **安全性**
   - 实现适当的认证机制
   - 验证用户凭据
   - 保护敏感信息

## 注意事项

1. 确保命名空间与原始WSDL保持一致
2. 正确处理异常并返回适当的错误信息
3. 实现合适的日志记录机制
4. 考虑服务的性能和并发处理
5. 注意参数验证和安全检查

## 参考资源

- [Apache CXF文档](https://cxf.apache.org/docs/springboot.html)
- [Spring Boot文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [JAX-WS规范](https://jakarta.ee/specifications/xml-web-services/) 