# Swagger API 文档使用指南

## 概述

本项目已集成 Swagger 3 (OpenAPI 3) 来自动生成和展示 API 文档。通过 Swagger UI，开发者可以：

- 查看所有可用的 API 接口
- 了解接口的参数、返回值和使用方法
- 直接在浏览器中测试 API 接口
- 查看详细的请求/响应示例

## 访问方式

启动应用后，可通过以下地址访问 Swagger 文档：

- **Swagger UI 界面**: `http://localhost:8888/swagger-ui.html`
- **OpenAPI JSON 文档**: `http://localhost:8888/v3/api-docs`

## 主要功能

### 1. 慢性病登记附件管理模块

位置：`/api/chronic-disease-registration-attachment`

#### 接口列表：

- **POST /api/chronic-disease-registration-attachment/idCard**
    - 功能：上传身份证附件并进行 OCR 识别
    - 参数：`file` (multipart/form-data 格式的图片文件)
    - 返回：包含附件信息和 OCR 识别结果

- **POST /api/chronic-disease-registration-attachment/insuranceCard**
    - 功能：上传医保卡附件
    - 参数：`file` (multipart/form-data 格式的图片文件)
    - 返回：包含附件信息

### 2. 慢性病申报核心模块

位置：`/api/chronic-disease-registration`

#### 接口列表：

- **POST /api/chronic-disease-registration/patientForm**
    - 功能：提交慢病申报患者表单
    - 参数：`ChronicDiseasePatientFormDto` (JSON 格式的患者申报信息)
    - 返回：包含申报记录的完整信息
    - 认证：需要通过 Authorization 请求头传入有效的会话ID

### 3. 使用示例

#### 提交慢病申报患者表单示例

```bash
curl -X POST "http://localhost:8888/api/chronic-disease-registration/patientForm" \
  -H "Authorization: 您的会话ID" \
  -H "Content-Type: application/json" \
  -d '{
    "patientIdCardAttachmentId": "123e4567-e89b-12d3-a456-426614174000",
    "patientIdCardNo": "110101199001011234",
    "patientName": "张三",
    "patientInsuranceCardAttachmentId": "456e7890-e89b-12d3-a456-426614174001",
    "patientInsuranceCardNo": "1101011990010112345",
    "insuranceType": "CITY_WORKER",
    "patientMobile": "13800138000"
  }'
```

预期返回：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "abd2a91e6ff9f835a76f801e6ba9b3aa",
    "openId": "omPh85Rdf1boaNRrC_AmUN0E62kU",
    "patientIdCardAttachmentId": "123e4567-e89b-12d3-a456-426614174000",
    "patientIdCardNo": "110101199001011234",
    "patientName": "张三",
    "patientGender": "男",
    "patientBirthDate": "1990-01-01",
    "patientInsuranceCardAttachmentId": "456e7890-e89b-12d3-a456-426614174001",
    "patientInsuranceCardNo": "1101011990010112345",
    "insuranceType": "CITY_WORKER",
    "patientMobile": "13800138000",
    "agentFlag": 0,
    "status": "DRAFT",
    "createTime": "2025-05-23 18:51:30",
    "updateTime": "2025-05-23 18:51:30"
  }
}
```

#### 上传身份证示例

```bash
curl -X POST "http://localhost:8888/api/chronic-disease-registration-attachment/idCard" \
  -H "Authorization: 您的会话ID" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/id_card.jpg"
```

预期返回：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "attachment": {
      "id": 1,
      "openId": "oxxxxxxx",
      "attachmentType": "ID_CARD",
      "fileName": "id_card.jpg",
      "fileUrl": "https://example.com/files/id_card.jpg",
      "createTime": "2023-12-01T10:30:00"
    },
    "ocrResult": {
      "name": "张三",
      "id": "110101199001011234",
      "addr": "北京市朝阳区xxx街道xxx号",
      "gender": "男",
      "nationality": "汉"
    }
  }
}
```

## 配置说明

### 1. 依赖引入

在 `pom.xml` 中已添加：

```xml

<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.8.8</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中的配置：

```yaml
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha
  group-configs:
    - group: '慢病申报附件管理'
      paths-to-match: '/api/chronic-disease-registration-attachment/**'
      display-name: '慢病申报附件管理'
    - group: '慢病申报核心模块'
      paths-to-match: '/api/chronic-disease-registration/**'
      display-name: '慢病申报核心接口'
```

### 3. XSS 过滤排除

为了确保 Swagger 相关接口能正常访问，已在 XSS 配置中排除：

> **注意**: 排除这些路径是因为 Swagger UI 和文件上传接口需要处理特殊字符和二进制数据。
> 确保这些接口有其他安全措施保护，如身份验证和授权检查。

```yaml
xss:
  excludes: /system/notice/*,/ph/article/*,/ph/wikiArticle/*,/api/chronic-disease-registration-attachment/*,/api/chronic-disease-registration/*
```

## 注解说明

### 控制器级别注解

- `@Tag`: 为整个控制器添加标签和描述
- `@RequestMapping`: 定义基础路径

### 方法级别注解

- `@Operation`: 描述 API 操作的摘要和详细信息
- `@ApiResponses`: 定义可能的响应
- `@ApiResponse`: 单个响应的详细信息

### 参数级别注解

- `@Parameter`: 描述方法参数
- `@RequestParam`: Spring 的参数绑定注解

### 响应模型注解

- `@Schema`: 定义数据模型的结构
- `@Content`: 定义响应内容类型
- `@ExampleObject`: 提供示例数据

## 最佳实践

### 1. 文档完整性

- 为每个接口添加清晰的摘要和描述
- 提供详细的参数说明
- 包含完整的响应示例
- 说明可能的错误情况

### 2. 示例数据

- 提供真实可用的示例数据
- 确保示例数据格式正确
- 包含成功和失败的示例

### 3. 分组管理

- 按功能模块对接口进行分组
- 使用有意义的分组名称
- 保持分组结构清晰

## 注意事项

1. **安全性**: 在生产环境中可能需要限制对 Swagger UI 的访问
    - 使用 Shiro 配置访问控制
    - 考虑使用环境变量控制 Swagger 启用状态
    - 在生产环境中禁用 Swagger UI 或限制访问 IP
2. **性能**: Swagger 文档生成可能会对性能产生轻微影响
    - 考虑使用缓存机制
    - 监控文档生成对响应时间的影响
3. **维护**: 确保 API 变更时及时更新文档注解
4. **版本管理**: 考虑 API 版本管理策略
    - 使用语义化版本控制
    - 为破坏性变更提供向后兼容性策略

## 扩展功能

### 自定义配置

可以通过 `OpenApiConfig` 类进行更多自定义配置：

- 修改 API 文档标题和描述
- 配置服务器信息
- 添加认证信息
- 自定义主题和样式

### 安全配置

在生产环境中，建议：

- 通过 Spring Security 控制访问权限
- 使用环境变量控制是否启用 Swagger
- 考虑使用 API Key 或其他认证方式

## 故障排除

### 常见问题

1. **Swagger UI 无法访问**: 检查端口和路径配置
2. **接口不显示**: 确认控制器包扫描路径
3. **注解不生效**: 检查依赖版本兼容性
4. **文件上传测试失败**: 确认文件大小限制配置

### 调试方法

- 查看控制台日志
- 检查 `/v3/api-docs` 端点是否正常
- 验证配置文件语法
- 确认注解使用正确 