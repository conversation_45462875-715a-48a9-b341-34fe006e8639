package space.lzhq.ph.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;

/**
 * 删除操作不允许异常
 * <p>
 * 当慢病申报记录的状态不允许进行删除操作时抛出此异常。
 * 通常在以下情况下抛出：
 * <ul>
 *   <li>申报记录状态不是DRAFT（草稿）</li>
 *   <li>申报记录已被审批通过</li>
 *   <li>申报记录处于审批流程中</li>
 * </ul>
 */
@ResponseStatus(HttpStatus.CONFLICT)
public class DeleteNotAllowedException extends RuntimeException {

    private final String resourceId;
    private final ChronicDiseaseRegistrationStatus currentStatus;

    /**
     * 创建删除不允许异常
     *
     * @param resourceId    资源ID
     * @param currentStatus 当前状态
     * @param message       异常信息
     */
    public DeleteNotAllowedException(String resourceId, ChronicDiseaseRegistrationStatus currentStatus, String message) {
        super(message);
        this.resourceId = resourceId;
        this.currentStatus = currentStatus;
    }

    /**
     * 创建删除不允许异常
     *
     * @param resourceId    资源ID
     * @param currentStatus 当前状态
     */
    public DeleteNotAllowedException(String resourceId, ChronicDiseaseRegistrationStatus currentStatus) {
        this(resourceId, currentStatus,
             String.format("申报记录 %s 当前状态为 %s，不允许删除操作", resourceId, currentStatus));
    }

    /**
     * 创建删除不允许异常
     *
     * @param message 异常信息
     */
    public DeleteNotAllowedException(String message) {
        super(message);
        this.resourceId = null;
        this.currentStatus = null;
    }

    /**
     * 获取资源ID
     *
     * @return 资源ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    public ChronicDiseaseRegistrationStatus getCurrentStatus() {
        return currentStatus;
    }
} 