package space.lzhq.ph.exception;

import lombok.Getter;

/**
 * 无效病种异常
 * 当病种ID无效或不支持指定医保类型时抛出此异常
 */
@Getter
public class InvalidDiseaseException extends RuntimeException {

    /**
     * 病种ID
     */
    private final Integer diseaseId;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public InvalidDiseaseException(String message) {
        super(message);
        this.diseaseId = null;
    }

    /**
     * 构造函数
     *
     * @param message   异常消息
     * @param diseaseId 病种ID
     */
    public InvalidDiseaseException(String message, Integer diseaseId) {
        super(message);
        this.diseaseId = diseaseId;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public InvalidDiseaseException(String message, Throwable cause) {
        super(message, cause);
        this.diseaseId = null;
    }

    /**
     * 构造函数
     *
     * @param message   异常消息
     * @param cause     异常原因
     * @param diseaseId 病种ID
     */
    public InvalidDiseaseException(String message, Throwable cause, Integer diseaseId) {
        super(message, cause);
        this.diseaseId = diseaseId;
    }
}