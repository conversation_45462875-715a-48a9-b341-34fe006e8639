package space.lzhq.ph.reconciliation.docs;

import org.springframework.stereotype.Service;
import space.lzhq.ph.reconciliation.config.MatchRule;
import space.lzhq.ph.reconciliation.config.MatchRuleConfig;

import java.util.List;
import java.util.Map;

/**
 * 匹配规则文档生成服务
 */
@Service
public class MatchRuleDocumentService {
    
    private final Map<String, MatchRuleConfig> matchRuleConfigs;
    
    public MatchRuleDocumentService(Map<String, MatchRuleConfig> matchRuleConfigs) {
        this.matchRuleConfigs = matchRuleConfigs;
    }
    
    /**
     * 生成所有渠道的匹配规则文档
     */
    public String generateMatchRuleDocument() {
        StringBuilder doc = new StringBuilder();
        doc.append("# 交易明细匹配规则文档\n\n");
        
        for (Map.Entry<String, MatchRuleConfig> entry : matchRuleConfigs.entrySet()) {
            String channelName = entry.getKey();
            MatchRuleConfig config = entry.getValue();
            
            doc.append("## ").append(channelName).append("\n\n");
            doc.append(config.getDescription()).append("\n\n");
            
            // 支付匹配规则
            doc.append("### 支付交易匹配规则\n\n");
            doc.append("| 应用方字段 | HIS系统字段 | 描述 |\n");
            doc.append("|------------|------------|------|\n");
            
            for (MatchRule rule : config.getPayRules()) {
                doc.append("| ").append(rule.getSourceField())
                   .append(" | ").append(rule.getTargetField())
                   .append(" | ").append(rule.getDescription())
                   .append(" |\n");
            }
            
            doc.append("\n");
            
            // 退款匹配规则
            doc.append("### 退款交易匹配规则\n\n");
            doc.append("| 应用方字段 | HIS系统字段 | 描述 |\n");
            doc.append("|------------|------------|------|\n");
            
            for (MatchRule rule : config.getRefundRules()) {
                doc.append("| ").append(rule.getSourceField())
                   .append(" | ").append(rule.getTargetField())
                   .append(" | ").append(rule.getDescription())
                   .append(" |\n");
            }
            
            doc.append("\n");
        }
        
        return doc.toString();
    }
    
    /**
     * 生成指定渠道的匹配规则文档
     */
    public String generateMatchRuleDocument(String channelName) {
        MatchRuleConfig config = matchRuleConfigs.get(channelName);
        if (config == null) {
            return "未找到渠道 " + channelName + " 的匹配规则配置";
        }
        
        StringBuilder doc = new StringBuilder();
        doc.append("# ").append(channelName).append(" 交易明细匹配规则\n\n");
        doc.append(config.getDescription()).append("\n\n");
        
        // 支付匹配规则
        doc.append("## 支付交易匹配规则\n\n");
        doc.append("| 应用方字段 | HIS系统字段 | 描述 |\n");
        doc.append("|------------|------------|------|\n");
        
        for (MatchRule rule : config.getPayRules()) {
            doc.append("| ").append(rule.getSourceField())
               .append(" | ").append(rule.getTargetField())
               .append(" | ").append(rule.getDescription())
               .append(" |\n");
        }
        
        doc.append("\n");
        
        // 退款匹配规则
        doc.append("## 退款交易匹配规则\n\n");
        doc.append("| 应用方字段 | HIS系统字段 | 描述 |\n");
        doc.append("|------------|------------|------|\n");
        
        for (MatchRule rule : config.getRefundRules()) {
            doc.append("| ").append(rule.getSourceField())
               .append(" | ").append(rule.getTargetField())
               .append(" | ").append(rule.getDescription())
               .append(" |\n");
        }
        
        return doc.toString();
    }
} 