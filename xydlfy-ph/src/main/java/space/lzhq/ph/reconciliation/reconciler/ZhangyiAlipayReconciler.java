package space.lzhq.ph.reconciliation.reconciler;

import org.mospital.alipay.AlipayBillItem;
import space.lzhq.ph.domain.BaseReconciliation;
import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.domain.ReconciliationDetail;
import space.lzhq.ph.domain.ZhangyiAlipayReconciliationDetail;
import space.lzhq.ph.reconciliation.matcher.impl.ZhangyiAlipayMatcher;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static space.lzhq.ph.domain.ReconciliationDetail.RESULT_AMOUNT_MISMATCH;
import static space.lzhq.ph.domain.ReconciliationDetail.RESULT_BALANCED;

public class ZhangyiAlipayReconciler {

    private final LocalDate billDate;
    private final List<AlipayBillItem> appOrders;
    private final List<HisSelfPayOrder> hisOrders;
    private final ZhangyiAlipayMatcher matcher;

    private final List<ZhangyiAlipayReconciliationDetail> reconciliationDetails = new ArrayList<>();
    private final Map<String, ZhangyiAlipayReconciliationDetail> paymentMap = new HashMap<>();
    private final Map<String, ZhangyiAlipayReconciliationDetail> refundMap = new HashMap<>();


    public ZhangyiAlipayReconciler(LocalDate billDate, List<AlipayBillItem> appOrders, List<HisSelfPayOrder> hisOrders, ZhangyiAlipayMatcher matcher) {
        this.billDate = billDate;
        this.appOrders = appOrders.stream().filter(it -> !it.getRemark().isBlank()).toList();
        this.hisOrders = hisOrders;
        this.matcher = matcher;
    }

    public BaseReconciliation.ReconciliationResult<ZhangyiAlipayReconciliationDetail> reconcile() {
        reconciliationDetails.clear();
        paymentMap.clear();
        refundMap.clear();

        BaseReconciliation.AmountSummary appSummary = processAppOrders();
        BaseReconciliation.AmountSummary hisSummary = processHisOrders();

        return new BaseReconciliation.ReconciliationResult<>(
                reconciliationDetails,
                appSummary.payAmount(),
                appSummary.refundAmount(),
                hisSummary.payAmount(),
                hisSummary.refundAmount()
        );
    }

    private BaseReconciliation.AmountSummary processAppOrders() {
        BigDecimal appPayAmount = BigDecimal.ZERO;
        BigDecimal appRefundAmount = BigDecimal.ZERO;

        for (AlipayBillItem appOrder : appOrders) {
            ZhangyiAlipayReconciliationDetail reconciliationDetail = new ZhangyiAlipayReconciliationDetail(billDate, appOrder);

            if (reconciliationDetail.isPay()) {
                appPayAmount = appPayAmount.add(reconciliationDetail.getAppTradeAmount());
                paymentMap.put(matcher.getAppPaymentMatchKey(appOrder), reconciliationDetail);
            } else {
                appRefundAmount = appRefundAmount.add(reconciliationDetail.getAppTradeAmount());
                refundMap.put(matcher.getAppRefundMatchKey(appOrder), reconciliationDetail);
            }

            reconciliationDetails.add(reconciliationDetail);
        }

        return new BaseReconciliation.AmountSummary(appPayAmount, appRefundAmount);
    }

    private BaseReconciliation.AmountSummary processHisOrders() {
        BigDecimal hisPayAmount = BigDecimal.ZERO;
        BigDecimal hisRefundAmount = BigDecimal.ZERO;

        for (HisSelfPayOrder hisOrder : hisOrders) {
            ZhangyiAlipayReconciliationDetail reconciliationDetail;
            if (hisOrder.isPayment()) {
                reconciliationDetail = paymentMap.get(matcher.getHisPaymentMatchKey(hisOrder));
            } else {
                reconciliationDetail = refundMap.get(matcher.getHisRefundMatchKey(hisOrder));
            }

            if (reconciliationDetail != null && reconciliationDetail.getResult() == ReconciliationDetail.RESULT_APP_SURPLUS) {
                updateHisInfo(reconciliationDetail, hisOrder);
            } else {
                reconciliationDetail = new ZhangyiAlipayReconciliationDetail(billDate, hisOrder);
                reconciliationDetails.add(reconciliationDetail);
            }

            if (reconciliationDetail.isPay()) {
                hisPayAmount = hisPayAmount.add(reconciliationDetail.getHisTradeAmount());
            } else {
                hisRefundAmount = hisRefundAmount.add(reconciliationDetail.getHisTradeAmount());
            }
        }

        return new BaseReconciliation.AmountSummary(hisPayAmount, hisRefundAmount);
    }

    private void updateHisInfo(ZhangyiAlipayReconciliationDetail detail, HisSelfPayOrder hisOrder) {
        detail.setHisTradeTime(hisOrder.getTradeTime());
        detail.setHisTradeAmount(hisOrder.getTradeAmount());
        detail.setPatientId(hisOrder.getPatientId());
        detail.setPatientName(hisOrder.getPatientName());
        detail.setBusinessType(hisOrder.getRemark());

        // 判断金额是否一致
        if (detail.getHisTradeAmount().compareTo(detail.getAppTradeAmount()) != 0) {
            detail.setResult(RESULT_AMOUNT_MISMATCH);
        } else {
            detail.setResult(RESULT_BALANCED);
        }
    }
}
