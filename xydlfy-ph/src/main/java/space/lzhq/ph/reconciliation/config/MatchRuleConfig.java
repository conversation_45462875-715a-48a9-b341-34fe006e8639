package space.lzhq.ph.reconciliation.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 匹配规则配置类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MatchRuleConfig {
    /** 渠道名称 */
    private String channelName;

    /** 渠道描述 */
    private String description;

    /** 支付匹配规则 */
    private List<MatchRule> payRules;

    /** 退款匹配规则 */
    private List<MatchRule> refundRules;
}