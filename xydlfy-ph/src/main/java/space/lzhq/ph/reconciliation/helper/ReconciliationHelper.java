package space.lzhq.ph.reconciliation.helper;

import org.springframework.stereotype.Component;
import space.lzhq.ph.domain.ReconciliationDetail;
import space.lzhq.ph.domain.ZhangyiWeixinReconciliationDetail;

/**
 * 匹配结果辅助类
 */
@Component
public class ReconciliationHelper {
    
    /**
     * 判断两个明细是否匹配并设置结果
     */
    public void setMatchResult(ZhangyiWeixinReconciliationDetail detail) {
        if (detail.getHisTradeAmount() == null || detail.getAppTradeAmount() == null) {
            // 一方有记录，另一方无记录
            if (detail.getHisTradeAmount() == null) {
                detail.setResult(ReconciliationDetail.RESULT_APP_SURPLUS);
            } else {
                detail.setResult(ReconciliationDetail.RESULT_HIS_SURPLUS);
            }
        } else if (detail.getHisTradeAmount().compareTo(detail.getAppTradeAmount()) != 0) {
            // 金额不一致
            detail.setResult(ReconciliationDetail.RESULT_AMOUNT_MISMATCH);
        } else {
            // 完全匹配
            detail.setResult(ReconciliationDetail.RESULT_BALANCED);
        }
    }
} 