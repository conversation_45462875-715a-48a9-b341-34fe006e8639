package space.lzhq.ph.reconciliation.matcher.impl;

import org.springframework.stereotype.Component;
import space.lzhq.ph.domain.CCBPosOrder;
import space.lzhq.ph.domain.CcbPosReconciliationDetail;
import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.reconciliation.matcher.ReconciliationMatcher;

/**
 * 建行POS对账匹配策略
 */
@Component
public class CcbPosMatcher implements ReconciliationMatcher<CCBPosOrder, CcbPosReconciliationDetail> {

    @Override
    public String getAppPaymentMatchKey(CCBPosOrder posOrder) {
        return posOrder.getSystemRefNo(); // 系统参考号作为支付匹配键
    }

    @Override
    public String getAppRefundMatchKey(CCBPosOrder posOrder) {
        return posOrder.getSystemRefNo(); // 系统参考号作为退款匹配键
    }

    @Override
    public String getHisPaymentMatchKey(HisSelfPayOrder hisOrder) {
        return hisOrder.getBankOrderNo(); // HIS系统银行订单号作为支付匹配键
    }

    @Override
    public String getHisRefundMatchKey(HisSelfPayOrder hisOrder) {
        return hisOrder.getBankOrderNo(); // HIS系统银行订单号作为退款匹配键
    }

} 