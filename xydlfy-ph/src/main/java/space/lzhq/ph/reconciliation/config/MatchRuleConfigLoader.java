package space.lzhq.ph.reconciliation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 匹配规则配置加载器
 */
@Configuration
public class MatchRuleConfigLoader {

    @Value("classpath:zhangyi-weixin-match-rules.yml")
    private Resource zhangyiWeixinRules;

    @Value("classpath:zhangyi-alipay-match-rules.yml")
    private Resource zhangyiAlipayRules;

    @Value("classpath:guoguang-atm-match-rules.yml")
    private Resource guoguangAtmRules;

    @Value("classpath:ccb-pos-match-rules.yml")
    private Resource ccbPosRules;

    @Bean
    public Map<String, MatchRuleConfig> matchRuleConfigs() throws IOException {
        Map<String, MatchRuleConfig> configs = new HashMap<>();

        // 加载掌医微信匹配规则
        loadRulesFromResource(zhangyiWeixinRules, configs);

        // 加载掌医支付宝匹配规则
        loadRulesFromResource(zhangyiAlipayRules, configs);

        // 加载国光自助机匹配规则
        loadRulesFromResource(guoguangAtmRules, configs);

        // 加载建行POS机匹配规则
        loadRulesFromResource(ccbPosRules, configs);

        return configs;
    }

    @SuppressWarnings("unchecked")
    private void loadRulesFromResource(Resource resource, Map<String, MatchRuleConfig> configs) throws IOException {
        try (InputStream inputStream = resource.getInputStream()) {
            Yaml yaml = new Yaml();
            Map<String, Map<String, Object>> yamlMap = yaml.load(inputStream);

            for (Map.Entry<String, Map<String, Object>> entry : yamlMap.entrySet()) {
                String channelName = entry.getKey();
                Map<String, Object> configMap = entry.getValue();

                MatchRuleConfig config = new MatchRuleConfig();
                config.setChannelName(channelName);
                config.setDescription((String) configMap.get("description"));

                // 解析支付规则
                config.setPayRules(parseRules((List<Map<String, String>>) configMap.get("pay_rules")));

                // 解析退款规则
                config.setRefundRules(parseRules((List<Map<String, String>>) configMap.get("refund_rules")));

                configs.put(channelName, config);
            }
        }
    }

    private List<MatchRule> parseRules(List<Map<String, String>> rulesList) {
        List<MatchRule> rules = new ArrayList<>();

        if (rulesList != null) {
            for (Map<String, String> ruleMap : rulesList) {
                MatchRule rule = new MatchRule();
                rule.setSourceField(ruleMap.get("source_field"));
                rule.setTargetField(ruleMap.get("target_field"));
                rule.setDescription(ruleMap.get("description"));

                rules.add(rule);
            }
        }

        return rules;
    }
} 