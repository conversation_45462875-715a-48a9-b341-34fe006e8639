package space.lzhq.ph.mail;

import jakarta.mail.*;
import jakarta.mail.internet.MimeUtility;
import jakarta.mail.search.SearchTerm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AttachmentDownloader extends IMAPMailReceiver<AttachmentResult> {
    private final LocalDate sentDate;
    private final String saveDirectory;

    public AttachmentDownloader(String host, int port, String username, String password, LocalDate sentDate, SearchTerm searchTerm, String saveDirectory) {
        super(host, port, username, password, searchTerm);
        this.sentDate = sentDate;
        this.saveDirectory = saveDirectory;
    }

    @Override
    protected int minMessageNumber(Folder inbox) throws MessagingException {
        if (this.sentDate == null) {
            return super.minMessageNumber(inbox);
        } else {
            long messageCount = inbox.getMessageCount();
            long daysUntilToday = sentDate.minusDays(15).until(LocalDate.now(), ChronoUnit.DAYS);
            return Math.clamp(messageCount - daysUntilToday, 1, (int) messageCount);
        }
    }

    @Override
    protected int maxMessageNumber(Folder inbox) throws MessagingException {
        if (this.sentDate == null) {
            return super.maxMessageNumber(inbox);
        } else {
            long messageCount = inbox.getMessageCount();
            long daysUntilToday = (int) sentDate.plusDays(15).until(LocalDate.now(), ChronoUnit.DAYS);
            return Math.clamp(Math.max(1, messageCount - daysUntilToday), 1, (int) messageCount);
        }
    }

    @Override
    protected AttachmentResult processEmails(Folder inbox) throws MessagingException, IOException {
        int messageCount = inbox.getMessageCount();

        log.info("开始处理邮件...");
        int minMessageNumber = minMessageNumber(inbox);
        int maxMessageNumber = maxMessageNumber(inbox);
        Message[] messages = inbox.getMessages(minMessageNumber, maxMessageNumber);

        // 预加载邮件信息以提高性能
        log.info("正在预加载邮件信息...");
        FetchProfile fp = new FetchProfile();
        fp.add(FetchProfile.Item.ENVELOPE);
        inbox.fetch(messages, fp);
        log.info("预加载完成");

        // 在客户端进行搜索
        List<Message> matchedMessages = new ArrayList<>();
        SearchTerm finalSearchTerm = this.searchTerm == null ? defaultSearchTerm() : this.searchTerm;

        for (Message message : messages) {
            try {
                if (finalSearchTerm.match(message)) {
                    matchedMessages.add(message);
                }
            } catch (Exception e) {
                log.warn("搜索邮件时出错: {}", e.getMessage());
            }
        }
        log.info("总邮件数: {}, 匹配邮件数: {}", messageCount, matchedMessages.size());

        Map<String, List<File>> attachmentsByEmail = new HashMap<>();
        List<String> errors = new ArrayList<>();

        File directory = new File(saveDirectory);
        FileUtils.forceMkdir(directory);

        for (Message message : matchedMessages) {
            try {
                List<File> attachments = downloadAttachments(message);
                if (!attachments.isEmpty()) {
                    attachmentsByEmail.put(message.getSubject(), attachments);
                }
            } catch (Exception e) {
                String error = String.format("处理邮件失败: %s, 原因: %s", message.getSubject(), e.getMessage());
                log.error(error, e);
                errors.add(error);
            }
        }

        return AttachmentResult.builder()
                .totalEmails(messageCount)
                .processedEmails(matchedMessages.size())
                .attachmentsByEmail(attachmentsByEmail)
                .errors(errors)
                .build();
    }

    /**
     * 下载邮件中的附件
     *
     * @param message 邮件消息
     * @return 下载的附件文件列表
     */
    private List<File> downloadAttachments(Message message) throws Exception {
        List<File> attachments = new ArrayList<>();

        Object content = message.getContent();
        if (content instanceof Multipart multipart) {
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                attachments.addAll(processBodyPart(bodyPart));
            }
        }

        return attachments;
    }

    /**
     * 处理邮件的每个部分
     */
    private List<File> processBodyPart(BodyPart bodyPart) throws Exception {
        List<File> attachments = new ArrayList<>();

        // 如果是 Multipart，递归处理
        if (bodyPart.getContent() instanceof Multipart multipart) {
            for (int i = 0; i < multipart.getCount(); i++) {
                attachments.addAll(processBodyPart(multipart.getBodyPart(i)));
            }
            return attachments;
        }

        // 检查是否为附件
        if (!isAttachment(bodyPart)) {
            return attachments;
        }

        // 获取并解码文件名
        String fileName = MimeUtility.decodeText(bodyPart.getFileName());
        log.debug("处理附件: {}, disposition: {}", fileName, bodyPart.getDisposition());

        // 创建保存路径
        Path savePath = createSafeSavePath(fileName);

        // 下载附件
        try (InputStream is = bodyPart.getInputStream()) {
            File attachmentFile = savePath.toFile();
            FileUtils.copyToFile(is, attachmentFile);
            log.info("成功下载附件: {}", fileName);
            attachments.add(attachmentFile);
        } catch (Exception e) {
            log.error("下载附件失败: {}", fileName, e);
            throw e;
        }

        return attachments;
    }

    /**
     * 判断邮件部分是否为附件
     *
     * @param part 邮件部分
     * @return 是否为附件
     */
    private boolean isAttachment(Part part) throws MessagingException {
        String disposition = part.getDisposition();
        String fileName = part.getFileName();

        // 以下情况视为附件：
        // 1. 显式标记为 ATTACHMENT
        // 2. 显式标记为 INLINE 且有文件名
        // 3. 没有 disposition 但有文件名（某些邮件客户端的处理方式）
        return (Part.ATTACHMENT.equalsIgnoreCase(disposition)) ||
                (Part.INLINE.equalsIgnoreCase(disposition) && StringUtils.isNotBlank(fileName)) ||
                (disposition == null && StringUtils.isNotBlank(fileName));
    }

    /**
     * 创建安全的文件保存路径
     * 处理文件名中的非法字符
     */
    private Path createSafeSavePath(String fileName) {
        // 移除文件名中的非法字符
        fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");

        Path directoryPath = Paths.get(saveDirectory);
        return directoryPath.resolve(fileName);
    }
}
