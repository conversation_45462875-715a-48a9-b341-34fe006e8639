package space.lzhq.ph.mail;

import jakarta.mail.Folder;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Store;
import jakarta.mail.search.ComparisonTerm;
import jakarta.mail.search.SearchTerm;
import jakarta.mail.search.SentDateTerm;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Properties;

@Slf4j
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class IMAPMailReceiver<T> {
    private final String host;
    private final int port;
    private final String username;
    private final String password;
    protected final SearchTerm searchTerm;

    public final T receive() throws MailProcessException {
        Properties props = new Properties();
        props.put("mail.store.protocol", "imaps");
        props.put("mail.imap.host", host);
        props.put("mail.imap.port", port);
        props.put("mail.imap.ssl.enable", true);

        Store store = null;
        Folder inbox = null;

        try {
            Session session = Session.getInstance(props);
            store = session.getStore("imaps");
            store.connect(host, username, password);

            inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);

            return processEmails(inbox);

        } catch (Exception e) {
            log.error("处理邮件时发生错误", e);
            throw new MailProcessException("处理邮件时发生错误", e);
        } finally {
            closeResources(inbox, store);
        }
    }

    protected abstract T processEmails(Folder inbox) throws MessagingException, IOException;

    protected int minMessageNumber(Folder inbox) throws MessagingException {
        int messageCount = inbox.getMessageCount();
        return Math.max(1, messageCount - 50);
    }

    protected int maxMessageNumber(Folder inbox) throws MessagingException {
        return inbox.getMessageCount();
    }

    protected SearchTerm defaultSearchTerm() {
        LocalDateTime startDate = LocalDateTime.now()
                .truncatedTo(ChronoUnit.DAYS)  // 截断到天的开始
                .minusDays(7);

        Date searchDate = Date.from(
                startDate.atZone(ZoneId.systemDefault()).toInstant()
        );

        return new SentDateTerm(ComparisonTerm.GE, searchDate);
    }

    private void closeResources(Folder inbox, Store store) {
        try {
            if (inbox != null && inbox.isOpen()) {
                inbox.close(false);
            }
            if (store != null && store.isConnected()) {
                store.close();
            }
        } catch (MessagingException e) {
            log.error("关闭资源时出错", e);
        }
    }
}
