package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import space.lzhq.ph.domain.Reservation;
import space.lzhq.ph.service.IReservationService;

import java.util.List;

/**
 * 预约记录Controller
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
@Controller
@RequestMapping("/ph/reservation")
public class ReservationAdminController extends BaseController {
    private String prefix = "ph/reservation";

    @Autowired
    private IReservationService reservationService;

    @RequiresPermissions("ph:reservation:view")
    @GetMapping()
    public String reservation() {
        return prefix + "/reservation";
    }

    /**
     * 查询预约记录列表
     */
    @RequiresPermissions("ph:reservation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Reservation reservation) {
        startPage();
        List<Reservation> list = reservationService.selectReservationList(reservation);
        return getDataTable(list);
    }

    /**
     * 导出预约记录列表
     */
    @RequiresPermissions("ph:reservation:export")
    @Log(title = "预约记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Reservation reservation) {
        List<Reservation> list = reservationService.selectReservationList(reservation);
        ExcelUtil<Reservation> util = new ExcelUtil<Reservation>(Reservation.class);
        return util.exportExcel(list, "预约记录数据");
    }


}
