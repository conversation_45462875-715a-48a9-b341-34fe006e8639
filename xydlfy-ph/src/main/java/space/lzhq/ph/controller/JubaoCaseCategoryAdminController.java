package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.JubaoCaseCategory;
import space.lzhq.ph.service.IJubaoCaseCategoryService;

import java.util.List;

/**
 * 问题类型Controller
 */
@Controller
@RequestMapping("/ph/jubaoCaseCategory")
public class JubaoCaseCategoryAdminController extends BaseController {

    private final IJubaoCaseCategoryService jubaoCaseCategoryService;

    public JubaoCaseCategoryAdminController(IJubaoCaseCategoryService jubaoCaseCategoryService) {
        this.jubaoCaseCategoryService = jubaoCaseCategoryService;
    }

    @RequiresPermissions("ph:jubaoCaseCategory:view")
    @GetMapping()
    public String jubaoCaseCategory() {
        return "ph/jubaoCaseCategory/jubaoCaseCategory";
    }

    /**
     * 查询问题类型树列表
     */
    @RequiresPermissions("ph:jubaoCaseCategory:list")
    @PostMapping("/list")
    @ResponseBody
    public List<JubaoCaseCategory> list(JubaoCaseCategory jubaoCaseCategory) {
        startOrderBy();
        return jubaoCaseCategoryService.selectJubaoCaseCategoryList(jubaoCaseCategory);
    }

    /**
     * 导出问题类型列表
     */
    @RequiresPermissions("ph:jubaoCaseCategory:export")
    @Log(title = "问题类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(JubaoCaseCategory jubaoCaseCategory) {
        List<JubaoCaseCategory> list = jubaoCaseCategoryService.selectJubaoCaseCategoryList(jubaoCaseCategory);
        ExcelUtil<JubaoCaseCategory> util = new ExcelUtil<JubaoCaseCategory>(JubaoCaseCategory.class);
        return util.exportExcel(list, "问题类型数据");
    }

    /**
     * 新增问题类型
     */
    @GetMapping(value = {"/add/{id}", "/add/"})
    public String add(@PathVariable(required = false) Long id, ModelMap mmap) {
        id = id == null ? 0 : id;
        JubaoCaseCategory parentCategory;
        if (id == 0) {
            parentCategory = new JubaoCaseCategory();
            parentCategory.setId(0L);
            parentCategory.setName("无");
        } else {
            parentCategory = jubaoCaseCategoryService.selectJubaoCaseCategoryById(id);
        }
        mmap.put("jubaoCaseCategory", parentCategory);
        return "ph/jubaoCaseCategory/add";
    }

    /**
     * 新增保存问题类型
     */
    @RequiresPermissions("ph:jubaoCaseCategory:add")
    @Log(title = "问题类型", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(JubaoCaseCategory jubaoCaseCategory) {
        return toAjax(jubaoCaseCategoryService.insertJubaoCaseCategory(jubaoCaseCategory));
    }

    /**
     * 修改问题类型
     */
    @RequiresPermissions("ph:jubaoCaseCategory:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        JubaoCaseCategory jubaoCaseCategory = jubaoCaseCategoryService.selectJubaoCaseCategoryById(id);
        mmap.put("jubaoCaseCategory", jubaoCaseCategory);
        return "ph/jubaoCaseCategory/edit";
    }

    /**
     * 修改保存问题类型
     */
    @RequiresPermissions("ph:jubaoCaseCategory:edit")
    @Log(title = "问题类型", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(JubaoCaseCategory jubaoCaseCategory) {
        return toAjax(jubaoCaseCategoryService.updateJubaoCaseCategory(jubaoCaseCategory));
    }

    /**
     * 删除
     */
    @RequiresPermissions("ph:jubaoCaseCategory:remove")
    @Log(title = "问题类型", businessType = BusinessType.DELETE)
    @GetMapping("/remove/{id}")
    @ResponseBody
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(jubaoCaseCategoryService.deleteJubaoCaseCategoryById(id));
    }

    /**
     * 选择问题类型树
     */
    @GetMapping(value = {"/selectJubaoCaseCategoryTree/{id}", "/selectJubaoCaseCategoryTree/"})
    public String selectJubaoCaseCategoryTree(@PathVariable(required = false) Long id, ModelMap mmap) {
        if (StringUtils.isNotNull(id)) {
            mmap.put("jubaoCaseCategory", jubaoCaseCategoryService.selectJubaoCaseCategoryById(id));
        }
        return "ph/jubaoCaseCategory/tree";
    }

    /**
     * 加载问题类型树列表
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Ztree> treeData() {
        return jubaoCaseCategoryService.selectJubaoCaseCategoryTree();
    }
}
