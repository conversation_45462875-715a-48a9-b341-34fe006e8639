package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.dto.request.ChronicDiseaseExpertAddDto;
import space.lzhq.ph.dto.request.ChronicDiseaseExpertEditDto;
import space.lzhq.ph.dto.request.ChronicDiseaseExpertResetPasswordDto;
import space.lzhq.ph.dto.request.ChronicDiseaseExpertSearchForm;
import space.lzhq.ph.dto.response.ChronicDiseaseExpertListResponseDto;
import space.lzhq.ph.service.ChronicDiseaseExpertService;

import java.util.List;

/**
 * 慢病专家管理Controller
 */
@Controller
@RequestMapping("/ph/chronic-disease-expert")
public class ChronicDiseaseExpertController extends BaseController {

    private static final String PREFIX = "ph/chronic-disease-expert";

    private final ChronicDiseaseExpertService expertService;

    public ChronicDiseaseExpertController(ChronicDiseaseExpertService expertService) {
        this.expertService = expertService;
    }

    /**
     * 专家管理主页面
     */
    @RequiresPermissions("ph:chronic-disease-expert:view")
    @GetMapping()
    public ModelAndView index() {
        return new ModelAndView(PREFIX + "/expert");
    }

    /**
     * 查询专家列表
     */
    @RequiresPermissions("ph:chronic-disease-expert:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ChronicDiseaseExpertSearchForm searchForm) {
        startPage();
        List<ChronicDiseaseExpertListResponseDto> list = expertService.selectExpertList(searchForm);
        return getDataTable(list);
    }

    /**
     * 导出专家列表
     */
    @RequiresPermissions("ph:chronic-disease-expert:export")
    @Log(title = "慢病专家", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ChronicDiseaseExpertSearchForm searchForm) {
        List<SysUser> list = expertService.selectExpertListForExport(searchForm);
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        return util.exportExcel(list, "慢病专家");
    }

    /**
     * 新增专家页面
     */
    @RequiresPermissions("ph:chronic-disease-expert:add")
    @GetMapping("/add")
    public ModelAndView add() {
        return new ModelAndView(PREFIX + "/add");
    }

    /**
     * 新增保存专家
     */
    @RequiresPermissions("ph:chronic-disease-expert:add")
    @Log(title = "慢病专家", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated ChronicDiseaseExpertAddDto expertDto) {
        try {
            int result = expertService.insertExpert(expertDto);
            return toAjax(result);
        } catch (Exception e) {
            return error("专家添加失败：" + e.getMessage());
        }
    }

    /**
     * 修改专家页面
     */
    @RequiresPermissions("ph:chronic-disease-expert:edit")
    @GetMapping("/edit/{userId}")
    public ModelAndView edit(@PathVariable("userId") Long userId, ModelMap map) {
        SysUser expert = expertService.selectExpertById(userId);
        map.put("expert", expert);
        return new ModelAndView(PREFIX + "/edit");
    }

    /**
     * 修改保存专家
     */
    @RequiresPermissions("ph:chronic-disease-expert:edit")
    @Log(title = "慢病专家", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated ChronicDiseaseExpertEditDto expertDto) {
        try {
            int result = expertService.updateExpert(expertDto);
            return toAjax(result);
        } catch (Exception e) {
            return error("专家修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除专家
     */
    @RequiresPermissions("ph:chronic-disease-expert:remove")
    @Log(title = "慢病专家", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        try {
            int result = expertService.deleteExpertByIds(ids);
            return toAjax(result);
        } catch (Exception e) {
            return error("专家删除失败：" + e.getMessage());
        }
    }

    /**
     * 重置密码页面
     */
    @RequiresPermissions("ph:chronic-disease-expert:resetPwd")
    @GetMapping("/resetPwd/{userId}")
    public ModelAndView resetPwd(@PathVariable("userId") Long userId, ModelMap map) {
        SysUser expert = expertService.selectExpertById(userId);
        map.put("expert", expert);
        return new ModelAndView(PREFIX + "/resetPwd");
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("ph:chronic-disease-expert:resetPwd")
    @Log(title = "慢病专家", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPwd")
    @ResponseBody
    public AjaxResult resetPwd(@Validated ChronicDiseaseExpertResetPasswordDto resetDto) {
        try {
            int result = expertService.resetExpertPassword(resetDto);
            return toAjax(result);
        } catch (Exception e) {
            return error("密码重置失败：" + e.getMessage());
        }
    }

    /**
     * 校验登录名唯一性
     */
    @PostMapping("/checkLoginNameUnique")
    @ResponseBody
    public boolean checkLoginNameUnique(@RequestParam String loginName, @RequestParam(required = false) Long userId) {
        return expertService.checkLoginNameUnique(loginName, userId);
    }

    /**
     * 校验手机号唯一性
     */
    @PostMapping("/checkPhoneUnique")
    @ResponseBody
    public boolean checkPhoneUnique(@RequestParam(required = false) String phonenumber, @RequestParam(required = false) Long userId) {
        // 如果手机号为空，则认为是唯一的（允许多个用户手机号为空）
        if (phonenumber == null || phonenumber.trim().isEmpty()) {
            return true;
        }
        return expertService.checkPhoneUnique(phonenumber, userId);
    }
}