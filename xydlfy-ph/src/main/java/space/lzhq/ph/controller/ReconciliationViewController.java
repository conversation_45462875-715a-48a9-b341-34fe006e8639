package space.lzhq.ph.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import space.lzhq.ph.reconciliation.config.MatchRuleConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 对账可视化控制器
 */
@Controller
@RequestMapping("/reconciliation/view")
public class ReconciliationViewController {
    
    private final Map<String, MatchRuleConfig> matchRuleConfigs;
    
    @Autowired
    public ReconciliationViewController(Map<String, MatchRuleConfig> matchRuleConfigs) {
        this.matchRuleConfigs = matchRuleConfigs;
    }
    
    /**
     * 匹配规则可视化页面
     */
    @GetMapping("/match-rules")
    public String viewMatchRules(Model model) {
        List<String> channels = new ArrayList<>(matchRuleConfigs.keySet());
        model.addAttribute("channels", channels);
        return "reconciliation/match-rules";
    }
} 