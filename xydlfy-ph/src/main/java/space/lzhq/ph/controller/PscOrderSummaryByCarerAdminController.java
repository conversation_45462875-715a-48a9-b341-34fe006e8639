package space.lzhq.ph.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.dromara.hutool.core.date.DatePattern;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscOrderWorkloadDepartmentSummaryView;
import space.lzhq.ph.domain.PscOrderWorkloadSummary;
import space.lzhq.ph.domain.PscOrderWorkloadSummaryView;
import space.lzhq.ph.service.IPscOrderWorkloadSummaryService;

import java.text.Collator;
import java.time.LocalDate;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ph/pscOrderSummaryByCarer")
public class PscOrderSummaryByCarerAdminController extends BaseController {

    private final IPscOrderWorkloadSummaryService service;

    public PscOrderSummaryByCarerAdminController(IPscOrderWorkloadSummaryService service) {
        this.service = service;
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @GetMapping
    public String page() {
        return "ph/pscOrderSummaryByCarer/index";
    }

    private List<PscOrderWorkloadSummaryView> buildViewsByCarer(LocalDate minDate, LocalDate maxDate) {
        List<PscOrderWorkloadSummary> orders = service.listByDateRangeAndPatientDepartment(minDate, maxDate, "*");
        Map<String, List<PscOrderWorkloadSummary>> carerOrderMap = orders.stream().collect(Collectors.groupingBy(PscOrderWorkloadSummary::getCarerEmployeeNo));
        return carerOrderMap.values().stream().map(PscOrderWorkloadSummaryView::fromOrders).sorted((o1, o2) -> {
            Collator collator = Collator.getInstance(Locale.SIMPLIFIED_CHINESE);
            return collator.compare(o1.getCarerName(), o2.getCarerName());
        }).collect(Collectors.toList());
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        List<PscOrderWorkloadSummaryView> views = buildViewsByCarer(minDate, maxDate);
        return getDataTable(views);
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        List<PscOrderWorkloadSummaryView> views = buildViewsByCarer(minDate, maxDate);
        ExcelUtil<PscOrderWorkloadSummaryView> util = new ExcelUtil<>(PscOrderWorkloadSummaryView.class);
        return util.exportFastExcel(views, "订单统计表");
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @GetMapping("/carerPage")
    public String carerPage(
            @RequestParam String carerEmployeeNo,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate,
            ModelMap modelMap
    ) {
        modelMap.put("carerEmployeeNo", carerEmployeeNo);
        modelMap.put("minDate", minDate);
        modelMap.put("maxDate", maxDate);
        return "ph/pscOrderSummaryByCarer/carer";
    }

    private List<PscOrderWorkloadDepartmentSummaryView> buildViewsByDepartment(LocalDate minDate, LocalDate maxDate, String carerEmployeeNo) {
        List<PscOrderWorkloadSummary> orders = service.listByDateRangeAndCarerEmployeeNo(minDate, maxDate, carerEmployeeNo);
        Map<String, List<PscOrderWorkloadSummary>> departmentOrderMap = orders.stream().collect(Collectors.groupingBy(PscOrderWorkloadSummary::getPatientDepartment));
        return departmentOrderMap.values().stream().map(PscOrderWorkloadDepartmentSummaryView::fromOrders).sorted((o1, o2) -> {
            if ("合计".equals(o1.getPatientDepartment())) {
                return 1;
            }
            if ("合计".equals(o2.getPatientDepartment())) {
                return -1;
            }
            return Collator.getInstance(Locale.SIMPLIFIED_CHINESE).compare(o1.getPatientDepartment(), o2.getPatientDepartment());
        }).collect(Collectors.toList());
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @PostMapping("/carerData")
    @ResponseBody
    public TableDataInfo carerData(
            @RequestParam String carerEmployeeNo,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        List<PscOrderWorkloadDepartmentSummaryView> views = buildViewsByDepartment(minDate, maxDate, carerEmployeeNo);
        return getDataTable(views);
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @PostMapping("/exportCarerData")
    @ResponseBody
    public AjaxResult export(
            @RequestParam String carerEmployeeNo,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate maxDate
    ) {
        List<PscOrderWorkloadDepartmentSummaryView> views = buildViewsByDepartment(minDate, maxDate, carerEmployeeNo);
        ExcelUtil<PscOrderWorkloadDepartmentSummaryView> util = new ExcelUtil<>(PscOrderWorkloadDepartmentSummaryView.class);
        return util.exportFastExcel(views, "工作量统计表");
    }

}
