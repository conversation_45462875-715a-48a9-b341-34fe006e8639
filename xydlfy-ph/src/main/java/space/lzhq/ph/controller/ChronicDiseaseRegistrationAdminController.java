package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationApprovalDto;
import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationSearchForm;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationAdminListResponseDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationDetailResponseDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationStatisticsDto;
import space.lzhq.ph.dto.response.TimelineEventDto;
import space.lzhq.ph.service.ChronicDiseaseRegistrationAdminService;
import space.lzhq.ph.service.ChronicDiseaseRegistrationTimelineService;

import java.util.List;

/**
 * 慢病申报管理端Controller
 */
@RestController
@RequestMapping("/ph/chronic-disease-registration")
public class ChronicDiseaseRegistrationAdminController extends BaseController {

    private static final String PREFIX = "ph/chronic-disease-registration";

    private final ChronicDiseaseRegistrationAdminService registrationService;
    private final ChronicDiseaseRegistrationTimelineService timelineService;

    public ChronicDiseaseRegistrationAdminController(
            ChronicDiseaseRegistrationAdminService registrationService,
            ChronicDiseaseRegistrationTimelineService timelineService
    ) {
        this.registrationService = registrationService;
        this.timelineService = timelineService;
    }

    /**
     * 慢病申报管理主页面
     */
    @RequiresPermissions("ph:chronic-disease-registration:view")
    @GetMapping()
    public ModelAndView chronicDiseaseRegistration() {
        return new ModelAndView(PREFIX + "/chronic-disease-registration");
    }

    /**
     * 查询慢病申报记录列表
     */
    @RequiresPermissions("ph:chronic-disease-registration:list")
    @PostMapping("/list")
    public TableDataInfo list(ChronicDiseaseRegistrationSearchForm searchForm) {
        startPage();
        List<ChronicDiseaseRegistrationAdminListResponseDto> list = registrationService.searchRegistrations(searchForm);
        return getDataTable(list);
    }

    /**
     * 获取慢病申报统计信息
     */
    @RequiresPermissions("ph:chronic-disease-registration:statistics")
    @GetMapping("/statistics")
    public AjaxResult statistics() {
        ChronicDiseaseRegistrationStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics);
    }

    /**
     * 导出慢病申报记录列表
     */
    @RequiresPermissions("ph:chronic-disease-registration:export")
    @Log(title = "慢病申报记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(ChronicDiseaseRegistrationSearchForm searchForm) {
        List<ChronicDiseaseRegistration> list = registrationService.exportRegistrations(searchForm);
        ExcelUtil<ChronicDiseaseRegistration> util = new ExcelUtil<>(ChronicDiseaseRegistration.class);
        return util.exportExcel(list, "慢病申报记录");
    }

    /**
     * 查看慢病申报记录详情
     */
    @RequiresPermissions("ph:chronic-disease-registration:query")
    @GetMapping("/detail/{id}")
    public ModelAndView detail(@PathVariable("id") String id) {
        ChronicDiseaseRegistrationDetailResponseDto detail = registrationService.getAdminDetailById(id);
        ModelAndView modelAndView = new ModelAndView(PREFIX + "/detail");
        modelAndView.addObject("registration", detail);
        return modelAndView;
    }

    /**
     * 获取慢病申报记录详情数据
     */
    @RequiresPermissions("ph:chronic-disease-registration:query")
    @GetMapping("/detail-data/{id}")
    public AjaxResult detailData(@PathVariable("id") String id) {
        ChronicDiseaseRegistrationDetailResponseDto detail = registrationService.getAdminDetailById(id);
        return success(detail);
    }

    /**
     * 审批慢病申报记录
     */
    @RequiresPermissions("ph:chronic-disease-registration:approve")
    @Log(title = "慢病申报审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{id}")
    public AjaxResult approveSave(@PathVariable("id") String id,
                                  @Validated @RequestBody ChronicDiseaseRegistrationApprovalDto approvalDto,
                                  BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            org.springframework.validation.FieldError fieldError = bindingResult.getFieldError();
            return error(fieldError != null ? fieldError.getDefaultMessage() : "表单验证失败");
        }

        String approverName = ShiroUtils.getSysUser().getUserName();
        registrationService.approveRegistration(id, approvalDto, approverName);
        return success("审批成功");
    }

    /**
     * 批量审批慢病申报记录
     */
    @RequiresPermissions("ph:chronic-disease-registration:approve")
    @Log(title = "慢病申报批量审批", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-approve")
    public AjaxResult batchApprove(@RequestParam("ids") List<String> ids,
                                   @Validated @RequestBody ChronicDiseaseRegistrationApprovalDto approvalDto,
                                   BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            org.springframework.validation.FieldError fieldError = bindingResult.getFieldError();
            return error(fieldError != null ? fieldError.getDefaultMessage() : "表单验证失败");
        }

        String approverName = ShiroUtils.getSysUser().getUserName();
        int count = registrationService.batchApproveRegistrations(ids, approvalDto, approverName);
        return success("批量审批成功，共处理 " + count + " 条记录");
    }

    /**
     * 删除慢病申报记录
     */
    @RequiresPermissions("ph:chronic-disease-registration:remove")
    @Log(title = "慢病申报记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable("id") String id) {
        String operatorName = ShiroUtils.getSysUser().getUserName();
        boolean result = registrationService.deleteRegistration(id, operatorName);
        return toAjax(result);
    }

    /**
     * 批量删除慢病申报记录
     */
    @RequiresPermissions("ph:chronic-disease-registration:remove")
    @Log(title = "慢病申报记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult batchRemove(@RequestParam("ids") String ids) {
        String operatorName = ShiroUtils.getSysUser().getUserName();
        String[] idArray = ids.split(",");
        int successCount = 0;

        for (String id : idArray) {
            try {
                if (registrationService.deleteRegistration(id.trim(), operatorName)) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("删除慢病申报记录失败，ID: {}, 错误: {}", id, e.getMessage());
            }
        }

        return success("批量删除完成，成功删除 " + successCount + " 条记录");
    }

    /**
     * 获取待审批记录数量
     */
    @RequiresPermissions("ph:chronic-disease-registration:list")
    @GetMapping("/pending-count")
    public AjaxResult getPendingCount() {
        ChronicDiseaseRegistrationStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics.getPendingApprovalCount());
    }

    /**
     * 获取状态分布统计
     */
    @RequiresPermissions("ph:chronic-disease-registration:statistics")
    @GetMapping("/status-distribution")
    public AjaxResult getStatusDistribution() {
        ChronicDiseaseRegistrationStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics.getCountByStatus());
    }

    /**
     * 获取申报趋势数据
     */
    @RequiresPermissions("ph:chronic-disease-registration:statistics")
    @GetMapping("/trend")
    public AjaxResult getTrend() {
        ChronicDiseaseRegistrationStatisticsDto statistics = registrationService.getStatistics();
        return success(statistics.getDailyTrend());
    }

    /**
     * 获取申报记录时间线
     */
    @RequiresPermissions("ph:chronic-disease-registration:query")
    @GetMapping("/timeline/{id}")
    public AjaxResult getTimeline(
            @PathVariable("id") String id,
            @RequestParam(required = false, defaultValue = "true") Boolean includeDetails
    ) {
        List<TimelineEventDto> timeline = timelineService.getRegistrationTimeline(id, includeDetails);
        return success(timeline);
    }
} 