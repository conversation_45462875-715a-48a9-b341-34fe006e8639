package space.lzhq.ph.controller;

import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.mospital.alipay.AlipayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.AlipayPayment;
import space.lzhq.ph.ext.HisExt;
import space.lzhq.ph.service.IAlipayPaymentService;

import java.util.Collections;
import java.util.List;

/**
 * 支付宝充值记录Controller
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
@Controller
@RequestMapping("/ph/alipayPayment")
public class AlipayPaymentAdminController extends BaseController {
    private String prefix = "ph/alipayPayment";

    @Autowired
    private IAlipayPaymentService alipayPaymentService;

    @RequiresPermissions("ph:alipayPayment:view")
    @GetMapping()
    public String alipayPayment() {
        return prefix + "/alipayPayment";
    }

    /**
     * 查询支付宝充值记录列表
     */
    @RequiresPermissions("ph:alipayPayment:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AlipayPayment alipayPayment) {
        startPage("id desc");
        List<AlipayPayment> list = alipayPaymentService.selectAlipayPaymentList(alipayPayment);
        return getDataTable(list);
    }

    /**
     * 导出支付宝充值记录列表
     */
    @RequiresPermissions("ph:alipayPayment:export")
    @Log(title = "支付宝充值记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AlipayPayment alipayPayment) {
        startPage("id desc");
        List<AlipayPayment> list = alipayPaymentService.selectAlipayPaymentList(alipayPayment);
        ExcelUtil<AlipayPayment> util = new ExcelUtil<AlipayPayment>(AlipayPayment.class);
        return util.exportExcel(list, "支付宝充值记录");
    }

    /**
     * 查询支付宝订单
     */
    @RequiresPermissions("ph:alipayPayment:queryAlipayOrder")
    @GetMapping("/queryAlipayOrder/{id}")
    @ResponseBody
    public AjaxResult queryAlipayOrder(@PathVariable Long id) {
        try {
            AlipayPayment payment = alipayPaymentService.selectAlipayPaymentById(id);
            AlipayTradeQueryResponse queryOrderResponse =
                    AlipayService.INSTANCE.queryOrder(
                            payment.getOutTradeNo(),
                            "",
                            Collections.singletonList("fund_bill_list")
                    );
            alipayPaymentService.updateOnPay(payment, queryOrderResponse);
            return AjaxResult.success(queryOrderResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 申请HIS充值
     */
    @RequiresPermissions("ph:alipayPayment:requestHisRecharge")
    @PostMapping("/requestHisRecharge/{id}")
    @ResponseBody
    public AjaxResult requestHisRecharge(@PathVariable Long id) {
        AlipayPayment payment = alipayPaymentService.selectAlipayPaymentById(id);
        if (!Constants.TRADE_SUCCESS.equals(payment.getTradeStatus())
                || Constants.RECHARGE_OK.equals(payment.getHisTradeStatus())
                || Constants.MANUAL_INIT != payment.getManualRechargeState()
        ) {
            return AjaxResult.error("此订单不能申请HIS充值");
        }

        payment.setManualRechargeState(Constants.MANUAL_REQUESTED);
        boolean ok = alipayPaymentService.updateAlipayPayment(payment) == 1;
        return ok ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 放行HIS充值
     */
    @RequiresPermissions("ph:alipayPayment:approveHisRecharge")
    @PostMapping("/approveHisRecharge/{id}")
    @ResponseBody
    public AjaxResult approveHisRecharge(@PathVariable Long id) {
        AlipayPayment payment = alipayPaymentService.selectAlipayPaymentById(id);
        if (!Constants.TRADE_SUCCESS.equals(payment.getTradeStatus())
                || Constants.RECHARGE_OK.equals(payment.getHisTradeStatus())
                || Constants.MANUAL_REQUESTED != payment.getManualRechargeState()
        ) {
            return AjaxResult.error("此订单不能放行HIS充值");
        }

        HisExt.INSTANCE.rechargeByAlipay(id);
        return AjaxResult.success();
    }
}
