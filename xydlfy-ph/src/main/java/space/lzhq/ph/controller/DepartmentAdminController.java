package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.Department;
import space.lzhq.ph.service.DepartmentKit;
import space.lzhq.ph.service.IDepartmentService;

import java.util.List;

/**
 * 科室Controller
 *
 * @date 2020-05-24
 */
@Controller
@RequestMapping("/ph/department")
public class DepartmentAdminController extends BaseController {
    private final String prefix = "ph/department";

    @Autowired
    private IDepartmentService departmentService;

    @RequiresPermissions("ph:department:view")
    @GetMapping()
    public String department() {
        return prefix + "/department";
    }

    /**
     * 查询科室列表
     */
    @RequiresPermissions("ph:department:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Department department) {
        startPage();
        List<Department> list = departmentService.selectDepartmentList(department);
        return getDataTable(list);
    }

    /**
     * 导出科室列表
     */
    @RequiresPermissions("ph:department:export")
    @Log(title = "科室", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Department department) {
        List<Department> list = departmentService.selectDepartmentList(department);
        ExcelUtil<Department> util = new ExcelUtil<Department>(Department.class);
        return util.exportExcel(list, "department");
    }

    /**
     * 新增科室
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存科室
     */
    @RequiresPermissions("ph:department:add")
    @Log(title = "科室", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Department department) {
        return toAjax(departmentService.insertDepartment(department));
    }

    /**
     * 修改科室
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable String id, ModelMap mmap) {
        Department department = departmentService.selectDepartmentById(id);
        mmap.put("department", department);
        return prefix + "/edit";
    }

    /**
     * 修改保存科室
     */
    @RequiresPermissions("ph:department:edit")
    @Log(title = "科室", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Department department) {
        return toAjax(departmentService.updateDepartment(department));
    }

    /**
     * 删除科室
     */
    @RequiresPermissions("ph:department:remove")
    @Log(title = "科室", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(departmentService.deleteDepartmentByIds(ids));
    }

    /**
     * 同步科室
     *
     * @return
     */
    @RequiresPermissions("ph:department:sync")
    @Log(title = "科室", businessType = BusinessType.SYNC)
    @PostMapping("/sync")
    @ResponseBody
    public AjaxResult sync() {
        DepartmentKit.INSTANCE.sync();
        return toAjax(true);
    }
}
