package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscNurse;
import space.lzhq.ph.service.IPscNurseService;

import java.util.List;

/**
 * 护士Controller
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
@Controller
@RequestMapping("/ph/pscNurse")
public class PscNurseAdminController extends BaseController {
    private String prefix = "ph/pscNurse";

    @Autowired
    private IPscNurseService pscNurseService;

    @RequiresPermissions("ph:pscNurse:view")
    @GetMapping()
    public String pscNurse() {
        return prefix + "/pscNurse";
    }

    /**
     * 查询护士列表
     */
    @RequiresPermissions("ph:pscNurse:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PscNurse pscNurse) {
        startPage();
        List<PscNurse> list = pscNurseService.selectPscNurseList(pscNurse);
        return getDataTable(list);
    }

    /**
     * 导出护士列表
     */
    @RequiresPermissions("ph:pscNurse:export")
    @Log(title = "护士", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PscNurse pscNurse) {
        List<PscNurse> list = pscNurseService.selectPscNurseList(pscNurse);
        ExcelUtil<PscNurse> util = new ExcelUtil<PscNurse>(PscNurse.class);
        return util.exportExcel(list, "PscNurse");
    }

    /**
     * 新增护士
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存护士
     */
    @RequiresPermissions("ph:pscNurse:add")
    @Log(title = "护士", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PscNurse pscNurse) {
        if (!pscNurseService.isEmployeeNoUnique(pscNurse)) {
            return error("工号已存在");
        }
        if (!pscNurseService.isMobileNoUnique(pscNurse)) {
            return error("手机号码已存在");
        }
        return toAjax(pscNurseService.insertPscNurse(pscNurse));
    }

    /**
     * 修改护士
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        PscNurse pscNurse = pscNurseService.selectPscNurseById(id);
        mmap.put("pscNurse", pscNurse);
        return prefix + "/edit";
    }

    /**
     * 修改保存护士
     */
    @RequiresPermissions("ph:pscNurse:edit")
    @Log(title = "护士", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PscNurse pscNurse) {
        if (!pscNurseService.isEmployeeNoUnique(pscNurse)) {
            return error("工号已存在");
        }
        if (!pscNurseService.isMobileNoUnique(pscNurse)) {
            return error("手机号码已存在");
        }
        return toAjax(pscNurseService.updatePscNurse(pscNurse));
    }

    /**
     * 删除护士
     */
    @RequiresPermissions("ph:pscNurse:remove")
    @Log(title = "护士", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(pscNurseService.deletePscNurseByIds(ids));
    }

    @PostMapping("/isEmployeeNoUnique")
    @ResponseBody
    public boolean isEmployeeNoUnique(PscNurse pscNurse) {
        return pscNurseService.isEmployeeNoUnique(pscNurse);
    }

    @PostMapping("/isMobileUnique")
    @ResponseBody
    public boolean isMobileUnique(PscNurse pscNurse) {
        return pscNurseService.isMobileNoUnique(pscNurse);
    }
}
