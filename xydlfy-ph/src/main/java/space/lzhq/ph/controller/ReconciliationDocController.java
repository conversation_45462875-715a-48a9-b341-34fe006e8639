package space.lzhq.ph.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import space.lzhq.ph.reconciliation.docs.MatchRuleDocumentService;

/**
 * 对账文档控制器
 */
@RestController
@RequestMapping("/reconciliation/docs")
public class ReconciliationDocController {
    
    private final MatchRuleDocumentService matchRuleDocumentService;
    
    @Autowired
    public ReconciliationDocController(MatchRuleDocumentService matchRuleDocumentService) {
        this.matchRuleDocumentService = matchRuleDocumentService;
    }
    
    /**
     * 获取所有渠道的匹配规则文档
     */
    @GetMapping(value = "/match-rules", produces = MediaType.TEXT_MARKDOWN_VALUE)
    public String getAllMatchRules() {
        return matchRuleDocumentService.generateMatchRuleDocument();
    }
    
    /**
     * 获取指定渠道的匹配规则文档
     */
    @GetMapping(value = "/match-rules/{channelName}", produces = MediaType.TEXT_MARKDOWN_VALUE)
    public String getChannelMatchRules(@PathVariable String channelName) {
        return matchRuleDocumentService.generateMatchRuleDocument(channelName);
    }
} 