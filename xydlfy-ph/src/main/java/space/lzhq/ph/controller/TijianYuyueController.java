package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import space.lzhq.ph.domain.TijianYuyue;
import space.lzhq.ph.service.ITijianYuyueService;

import java.util.List;

/**
 * 体检预约记录Controller
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Controller
@RequestMapping("/ph/tijianYuyue")
public class TijianYuyueController extends BaseController {
    private String prefix = "ph/tijianYuyue";

    @Autowired
    private ITijianYuyueService tijianYuyueService;

    @RequiresPermissions("ph:tijianYuyue:view")
    @GetMapping()
    public String tijianYuyue() {
        return prefix + "/tijianYuyue";
    }

    /**
     * 查询体检预约记录列表
     */
    @RequiresPermissions("ph:tijianYuyue:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(TijianYuyue tijianYuyue) {
        startPage();
        List<TijianYuyue> list = tijianYuyueService.selectTijianYuyueList(tijianYuyue);
        return getDataTable(list);
    }

    /**
     * 导出体检预约记录列表
     */
    @RequiresPermissions("ph:tijianYuyue:export")
    @Log(title = "体检预约记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(TijianYuyue tijianYuyue) {
        List<TijianYuyue> list = tijianYuyueService.selectTijianYuyueList(tijianYuyue);
        ExcelUtil<TijianYuyue> util = new ExcelUtil<TijianYuyue>(TijianYuyue.class);
        return util.exportExcel(list, "tijianYuyue");
    }

}
