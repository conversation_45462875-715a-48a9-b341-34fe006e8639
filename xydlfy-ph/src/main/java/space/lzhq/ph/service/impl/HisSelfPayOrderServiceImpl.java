package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.ext.LambdaQueryChainWrapperExt;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.domain.HisSelfPayOrderSearchForm;
import space.lzhq.ph.mapper.HisSelfPayOrderMapper;
import space.lzhq.ph.service.IHisSelfPayOrderService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Service
@DataSource(value = DataSourceType.HIS_ORDER_VIEW)
public class HisSelfPayOrderServiceImpl extends ServiceImpl<HisSelfPayOrderMapper, HisSelfPayOrder> implements IHisSelfPayOrderService {

    @Override
    public List<HisSelfPayOrder> listBySearchForm(HisSelfPayOrderSearchForm searchForm) {
        LocalDateTime startTradeTime = searchForm.getStartTradeDate() == null ? null : searchForm.getStartTradeDate().atStartOfDay();
        LocalDateTime endTradeTime = searchForm.getEndTradeDate() == null ? null : searchForm.getEndTradeDate().atTime(LocalTime.MAX);
        return new LambdaQueryChainWrapperExt<>(lambdaQuery())
                .geIfPresent(HisSelfPayOrder::getTradeTime, startTradeTime)
                .leIfPresent(HisSelfPayOrder::getTradeTime, endTradeTime)
                .eqIfNotEmpty(HisSelfPayOrder::getBankOrderNo, searchForm.getBankOrderNo())
                .eqIfNotEmpty(HisSelfPayOrder::getPatientId, searchForm.getPatientId())
                .wrapper()
                .list();
    }

    @Override
    public List<HisSelfPayOrder> listForZhangyiWeixin(LocalDate tradeDate) {
        LocalDateTime startTradeTime = tradeDate.atStartOfDay();
        LocalDateTime endTradeTime = tradeDate.atTime(LocalTime.MAX);
        List<HisSelfPayOrder> list1 = lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getApplication, "掌医")
                .eq(HisSelfPayOrder::getPayChannel, "建行")
                .ne(HisSelfPayOrder::getPayType, "就诊卡余额退款")
                .list();
        List<HisSelfPayOrder> list2 = lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getApplication, "HIS")
                .eq(HisSelfPayOrder::getPayChannel, "建行")
                .eq(HisSelfPayOrder::getPayType, "银医微信退款")
                .list();
        List<HisSelfPayOrder> result = new ArrayList<>(list1.size() + list2.size());
        result.addAll(list1);
        result.addAll(list2);
        return result;
    }

    @Override
    public List<HisSelfPayOrder> listForZhangyiAlipay(LocalDate tradeDate) {
        LocalDateTime startTradeTime = tradeDate.atStartOfDay();
        LocalDateTime endTradeTime = tradeDate.atTime(LocalTime.MAX);
        return lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getApplication, "掌医")
                .eq(HisSelfPayOrder::getPayChannel, "支付宝")
                .list();
    }

    @Override
    public List<HisSelfPayOrder> listForGuoguangAtm(LocalDate tradeDate) {
        LocalDateTime startTradeTime = tradeDate.atStartOfDay();
        LocalDateTime endTradeTime = tradeDate.atTime(LocalTime.MAX);
        return lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getApplication, "自助机")
                .eq(HisSelfPayOrder::getPayChannel, "建行")
                .list();
    }

    @Override
    public List<HisSelfPayOrder> listForCcbPos(LocalDate tradeDate) {
        LocalDateTime startTradeTime = tradeDate.atStartOfDay();
        LocalDateTime endTradeTime = tradeDate.atTime(LocalTime.MAX);
        return lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getApplication, "HIS")
                .eq(HisSelfPayOrder::getPayChannel, "建行")
                .ne(HisSelfPayOrder::getPayType, "银医微信退款")
                .list();
    }

    @Override
    public List<HisSelfPayOrder> listForAbcPos(LocalDate tradeDate) {
        LocalDateTime startTradeTime = tradeDate.atStartOfDay();
        LocalDateTime endTradeTime = tradeDate.atTime(LocalTime.MAX);
        return lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getPayChannel, "农行")
                .list();
    }

    @Override
    public List<HisSelfPayOrder> listForWeixinTransfer(LocalDate trade) {
        LocalDateTime startTradeTime = trade.atStartOfDay();
        LocalDateTime endTradeTime = trade.atTime(LocalTime.MAX);
        return lambdaQuery()
                .between(HisSelfPayOrder::getTradeTime, startTradeTime, endTradeTime)
                .eq(HisSelfPayOrder::getApplication, "掌医")
                .eq(HisSelfPayOrder::getPayChannel, "建行")
                .eq(HisSelfPayOrder::getPayType, "就诊卡余额退款")
                .list();
    }

    @Override
    public BigDecimal getTotalPaymentAmountByDate(LocalDate date) {
        return baseMapper.getTotalPaymentAmountByDate(date);
    }

    @Override
    public BigDecimal getTotalRefundAmountByDate(LocalDate date) {
        return baseMapper.getTotalRefundAmountByDate(date);
    }

}
