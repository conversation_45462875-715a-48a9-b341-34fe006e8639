package space.lzhq.ph.service.impl;

import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.map.Dict;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.mospital.bsoft.MenzhenSettlement;
import org.mospital.bsoft.Result;
import org.mospital.bsoft.hai.Recharge;
import org.mospital.bsoft.hai.RechargeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.AlipayPayment;
import space.lzhq.ph.mapper.AlipayPaymentMapper;
import space.lzhq.ph.service.IAlipayPaymentService;

import java.util.Date;
import java.util.List;

/**
 * 支付宝支付Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
@Service
public class AlipayPaymentServiceImpl implements IAlipayPaymentService {
    @Autowired
    private AlipayPaymentMapper alipayPaymentMapper;

    /**
     * 查询支付宝支付
     *
     * @param id 支付宝支付ID
     * @return 支付宝支付
     */
    @Override
    public AlipayPayment selectAlipayPaymentById(Long id) {
        return alipayPaymentMapper.selectAlipayPaymentById(id);
    }

    @Override
    public AlipayPayment selectAlipayPaymentByOutTradeNo(String outTradeNo) {
        return alipayPaymentMapper.selectAlipayPaymentByOutTradeNo(outTradeNo);
    }

    @Override
    public List<AlipayPayment> selectListForRefund(String jzCardNo, Date minCreateTime, Date maxCreateTime) {
        AlipayPayment payment = new AlipayPayment();
        payment.setJzCardNo(jzCardNo);
        payment.setTradeStatus(Constants.TRADE_SUCCESS);
        payment.setHisTradeStatus(Constants.RECHARGE_OK);
        payment.setParams(
                Dict.of()
                        .set("beginCreateTime", DateUtil.formatDateTime(minCreateTime))
                        .set("endCreateTime", DateUtil.formatDateTime(maxCreateTime))
        );
        return selectAlipayPaymentList(payment);
    }

    /**
     * 查询支付宝支付列表
     *
     * @param alipayPayment 支付宝支付
     * @return 支付宝支付
     */
    @Override
    public List<AlipayPayment> selectAlipayPaymentList(AlipayPayment alipayPayment) {
        return alipayPaymentMapper.selectAlipayPaymentList(alipayPayment);
    }

    /**
     * 新增支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    @Override
    public int insertAlipayPayment(AlipayPayment alipayPayment) {
        alipayPayment.setCreateTime(DateUtils.getNowDate());
        return alipayPaymentMapper.insertAlipayPayment(alipayPayment);
    }

    /**
     * 修改支付宝支付
     *
     * @param alipayPayment 支付宝支付
     * @return 结果
     */
    @Override
    public int updateAlipayPayment(AlipayPayment alipayPayment) {
        return alipayPaymentMapper.updateAlipayPayment(alipayPayment);
    }

    /**
     * 删除支付宝支付对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteAlipayPaymentByIds(String ids) {
        return alipayPaymentMapper.deleteAlipayPaymentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除支付宝支付信息
     *
     * @param id 支付宝支付ID
     * @return 结果
     */
    @Override
    public int deleteAlipayPaymentById(Long id) {
        return alipayPaymentMapper.deleteAlipayPaymentById(id);
    }

    @Override
    public boolean updateOnPay(AlipayPayment payment, AlipayTradeQueryResponse queryOrderResponse) {
        payment.setTradeNo(queryOrderResponse.getTradeNo());
        payment.setTradeStatus(queryOrderResponse.getTradeStatus());
        return updateAlipayPayment(payment) == 1;
    }

    @Override
    public boolean updateOnRecharge(AlipayPayment payment, RechargeResponse rechargeResponse) {
        if (payment.getManualRechargeState() == Constants.MANUAL_REQUESTED) {
            payment.setManualRechargeState(Constants.MANUAL_APPROVED);
        }

        if (rechargeResponse.isOk()) {
            payment.setHisTradeStatus(Constants.RECHARGE_OK);
            Recharge recharge = rechargeResponse.getRecharge();
            payment.setHisReceiptNo(recharge.getOrderNo());
            payment.setHisSuccessTime(new Date());
        } else {
            payment.setHisTradeStatus(rechargeResponse.getCode() + ":" + rechargeResponse.getMessage());
        }

        return updateAlipayPayment(payment) == 1;
    }

    @Override
    public boolean updateOnSettlement(AlipayPayment payment, Result<MenzhenSettlement> settlementResult) {
        if (payment.getManualRechargeState() == Constants.MANUAL_REQUESTED) {
            payment.setManualRechargeState(Constants.MANUAL_APPROVED);
        }

        if (settlementResult.isOk()) {
            payment.setHisTradeStatus(Constants.RECHARGE_OK);
            payment.setHisSuccessTime(new Date());
            MenzhenSettlement settlement = settlementResult.getData();
            if (settlement != null) {
                payment.setHisReceiptNo(CharSequenceUtil.emptyIfNull(settlement.getOrderNo()).toString());
            }
        } else {
            payment.setHisTradeStatus(settlementResult.getCode() + ":" + settlementResult.getMessage());
        }

        return updateAlipayPayment(payment) == 1;
    }
}
