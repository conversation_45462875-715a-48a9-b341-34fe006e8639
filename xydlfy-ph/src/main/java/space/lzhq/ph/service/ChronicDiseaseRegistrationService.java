package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.ChronicDisease;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.dto.request.ChronicDiseaseAgentUpdateDto;
import space.lzhq.ph.dto.request.ChronicDiseaseDocumentAttachmentsUpdateDto;
import space.lzhq.ph.dto.request.ChronicDiseasePatientFormDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationDetailResponseDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationListResponseDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.exception.AttachmentValidationException;
import space.lzhq.ph.exception.DataUpdateFailedException;

import java.util.List;
import java.util.Optional;

public interface ChronicDiseaseRegistrationService extends IService<ChronicDiseaseRegistration> {


    /**
     * 根据ID获取慢病申报记录详情
     * <p>
     * 此方法用于获取指定ID的慢病申报记录的详细信息。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>返回包含完整详情的响应DTO</li>
     * </ul>
     *
     * @param id     申报记录ID，不能为空
     * @param openId 用户OpenID，用于验证权限，不能为空
     * @return 申报记录详情；若记录不存在或无权限访问直接抛出相应异常
     */
    ChronicDiseaseRegistrationDetailResponseDto getDetailById(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 保存慢病申报记录
     * <p>
     * 此方法用于创建新的慢病申报记录。执行以下操作：
     * <ul>
     *   <li>验证患者身份证附件的有效性</li>
     *   <li>验证患者医保卡附件的有效性</li>
     *   <li>将DTO映射为实体对象</li>
     *   <li>设置申报记录的基本信息（用户ID、状态、时间等）</li>
     *   <li>保存申报记录到数据库</li>
     *   <li>更新相关附件的申报记录关联</li>
     * </ul>
     *
     * @param dto    患者申报表单DTO，包含患者基本信息和附件ID，不能为空且必须通过验证
     * @param openId 用户OpenID，用于标识申报用户，长度必须在16-28个字符之间
     * @return 包含保存后申报记录的Optional对象，如果保存失败则抛出异常
     * @throws AttachmentValidationException 当附件验证失败时抛出异常
     * @throws DataUpdateFailedException     当数据库保存操作失败时抛出异常
     * @throws RuntimeException              当其他数据库操作失败时抛出异常
     */
    Optional<ChronicDiseaseRegistration> save(
            @NotNull @Valid ChronicDiseasePatientFormDto dto,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 清除慢病申报记录的代办标志
     *
     * @param id     申报记录ID
     * @param openId 用户OpenID
     * @return 更新后的申报记录
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     */
    ChronicDiseaseRegistration clearAgentFlag(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 更新慢病申报记录的代办人信息
     * <p>
     * 此方法用于更新现有慢病申报记录的代办人信息。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许编辑</li>
     *   <li>验证代办人身份证附件的有效性</li>
     *   <li>更新代办人相关信息</li>
     *   <li>设置代办标志为1</li>
     * </ul>
     *
     * @param id       申报记录ID，不能为空
     * @param agentDto 代办人更新信息DTO，包含代办人的所有必要信息，不能为空且必须通过验证
     * @param openId   用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     * @throws space.lzhq.ph.exception.AttachmentValidationException       当代办人身份证附件验证失败时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    ChronicDiseaseRegistration updateAgent(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid ChronicDiseaseAgentUpdateDto agentDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 根据申报记录获取可申请的慢性病病种列表
     * <p>
     * 此方法用于获取用户可以申请的慢性病病种列表。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>从申报记录中提取医保类型</li>
     *   <li>根据医保类型查询支持的慢性病病种</li>
     * </ul>
     *
     * @param id     申报记录ID，不能为空
     * @param openId 用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 可申请的慢性病病种列表，如果没有找到则返回空列表
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     */
    List<ChronicDisease> fetchAvailableDiseases(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 更新慢病申报记录的慢病病种
     * <p>
     * 此方法用于更新现有慢病申报记录的慢病病种。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许编辑</li>
     *   <li>验证病种ID是否有效且支持当前医保类型</li>
     *   <li>更新申报记录的病种ID</li>
     * </ul>
     *
     * @param id        申报记录ID，不能为空
     * @param diseaseId 慢病病种ID，必须为正整数且不能为空
     * @param openId    用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     * @throws space.lzhq.ph.exception.InvalidDiseaseException             当病种ID无效或不支持当前医保类型时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    ChronicDiseaseRegistration updateDisease(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Positive Integer diseaseId,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 更新慢病申报记录的认定资料附件
     * <p>
     * 此方法用于更新现有慢病申报记录的认定资料附件。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许编辑</li>
     *   <li>验证所有认定资料附件的有效性</li>
     *   <li>更新申报记录的认定资料附件ID列表</li>
     *   <li>关联所有附件到当前申报记录</li>
     * </ul>
     *
     * @param id             申报记录ID，不能为空
     * @param attachmentsDto 认定资料附件更新信息DTO，包含附件ID列表，不能为空且必须通过验证
     * @param openId         用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     * @throws space.lzhq.ph.exception.AttachmentValidationException       当认定资料附件验证失败时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    ChronicDiseaseRegistration updateDocumentAttachments(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid ChronicDiseaseDocumentAttachmentsUpdateDto attachmentsDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 提交慢病申报记录
     * <p>
     * 此方法用于提交慢病申报记录，将状态从DRAFT（草稿）或REJECTED（审核驳回）更新为SUBMITTED（已提交）。
     * 执行以下验证和操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许提交（DRAFT或REJECTED状态）</li>
     *   <li>验证申报人信息是否已填写完整</li>
     *   <li>验证代办人信息是否已填写完整（如果是代办申请）</li>
     *   <li>验证是否已选择慢病病种</li>
     *   <li>验证是否已上传慢病认定资料</li>
     *   <li>更新申报状态为SUBMITTED，设置提交时间</li>
     * </ul>
     *
     * @param id     申报记录ID，不能为空，长度必须为32字符
     * @param openId 用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许提交时
     * @throws space.lzhq.ph.exception.SubmissionValidationException       当申报信息不完整无法提交时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    ChronicDiseaseRegistration submitRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

/**
     * 获取用户的慢病申报列表
     * <p>
     * 此方法用于获取指定用户的慢病申报记录列表。执行以下操作：
     * <ul>
     *   <li>根据用户OpenID查询申报记录</li>
     *   <li>可选择按状态筛选申报记录</li>
     *   <li>按创建时间倒序排列</li>
     * </ul>
     *
     * @param openId 用户OpenID，不能为空，长度必须在16-28字符之间
     * @param status 状态筛选（可选），为null时返回所有状态的记录
     * @return 申报记录列表，按创建时间倒序排列，如果没有记录则返回空列表
     * @since 1.0.0
      */
    List<ChronicDiseaseRegistrationListResponseDto> getUserRegistrationList(
            @NotBlank @Size(min = 16, max = 28) String openId,
            ChronicDiseaseRegistrationStatus status
    );

}