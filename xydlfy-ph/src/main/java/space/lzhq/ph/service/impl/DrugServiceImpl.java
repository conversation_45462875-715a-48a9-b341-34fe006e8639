package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Drug;
import space.lzhq.ph.mapper.DrugMapper;
import space.lzhq.ph.service.IDrugService;

import java.util.List;

/**
 * 药品Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Service
public class DrugServiceImpl implements IDrugService {
    @Autowired
    private DrugMapper drugMapper;

    /**
     * 查询药品
     *
     * @param id 药品ID
     * @return 药品
     */
    @Override
    public Drug selectDrugById(String id) {
        return drugMapper.selectDrugById(id);
    }

    /**
     * 查询药品列表
     *
     * @param drug 药品
     * @return 药品
     */
    @Override
    public List<Drug> selectDrugList(Drug drug) {
        return drugMapper.selectDrugList(drug);
    }

    /**
     * 新增药品
     *
     * @param drug 药品
     * @return 结果
     */
    @Override
    public int insertDrug(Drug drug) {
        return drugMapper.insertDrug(drug);
    }

    @Override
    public int insertDrugs(List<Drug> drugs) {
        int count = 0;
        List<List<Drug>> drugChunks = ListUtils.partition(drugs, 500);
        for (List<Drug> drugChunk : drugChunks) {
            count += drugMapper.insertDrugs(drugChunk);
        }
        return count;
    }

    /**
     * 修改药品
     *
     * @param drug 药品
     * @return 结果
     */
    @Override
    public int updateDrug(Drug drug) {
        return drugMapper.updateDrug(drug);
    }

    /**
     * 删除药品对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteDrugByIds(String ids) {
        return drugMapper.deleteDrugByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除药品信息
     *
     * @param id 药品ID
     * @return 结果
     */
    @Override
    public int deleteDrugById(String id) {
        return drugMapper.deleteDrugById(id);
    }

    @Override
    public void clearAll() {
        drugMapper.clearAll();
    }
}
