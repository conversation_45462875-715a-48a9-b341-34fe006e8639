package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.NurseCard;
import space.lzhq.ph.mapper.NurseCardMapper;
import space.lzhq.ph.service.INurseCardService;

import java.util.List;

/**
 * 电子陪护证Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-11
 */
@Service
public class NurseCardServiceImpl implements INurseCardService {
    @Autowired
    private NurseCardMapper nurseCardMapper;

    /**
     * 查询电子陪护证
     *
     * @param id 电子陪护证ID
     * @return 电子陪护证
     */
    @Override
    public NurseCard selectNurseCardById(Long id) {
        return nurseCardMapper.selectNurseCardById(id);
    }

    /**
     * 查询电子陪护证列表
     *
     * @param nurseCard 电子陪护证
     * @return 电子陪护证
     */
    @Override
    public List<NurseCard> selectNurseCardList(NurseCard nurseCard) {
        return nurseCardMapper.selectNurseCardList(nurseCard);
    }

    /**
     * 新增电子陪护证
     *
     * @param nurseCard 电子陪护证
     * @return 结果
     */
    @Override
    public int insertNurseCard(NurseCard nurseCard) {
        nurseCard.setCreateTime(DateUtils.getNowDate());
        return nurseCardMapper.insertNurseCard(nurseCard);
    }

    /**
     * 修改电子陪护证
     *
     * @param nurseCard 电子陪护证
     * @return 结果
     */
    @Override
    public int updateNurseCard(NurseCard nurseCard) {
        return nurseCardMapper.updateNurseCard(nurseCard);
    }

    /**
     * 删除电子陪护证对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteNurseCardByIds(String ids) {
        return nurseCardMapper.deleteNurseCardByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除电子陪护证信息
     *
     * @param id 电子陪护证ID
     * @return 结果
     */
    @Override
    public int deleteNurseCardById(Long id) {
        return nurseCardMapper.deleteNurseCardById(id);
    }
}
