package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationTimeline;
import space.lzhq.ph.dto.response.TimelineEventDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.TimelineEventType;
import space.lzhq.ph.enums.TimelineOperatorType;
import space.lzhq.ph.exception.TimelineRecordException;
import space.lzhq.ph.mapper.ChronicDiseaseRegistrationTimelineMapper;
import space.lzhq.ph.service.ChronicDiseaseRegistrationTimelineService;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 慢病申报时间线服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ChronicDiseaseRegistrationTimelineServiceImpl
        extends ServiceImpl<ChronicDiseaseRegistrationTimelineMapper, ChronicDiseaseRegistrationTimeline>
        implements ChronicDiseaseRegistrationTimelineService {

    /**
     * 获取当前代理对象，用于调用事务方法
     */
    private ChronicDiseaseRegistrationTimelineService getSelf() {
        return (ChronicDiseaseRegistrationTimelineService) AopContext.currentProxy();
    }

    @Override
    public List<TimelineEventDto> getRegistrationTimeline(String registrationId, Boolean includeDetails) {
        List<ChronicDiseaseRegistrationTimeline> timelines =
                getBaseMapper().selectTimelineByRegistrationId(registrationId, includeDetails);

        return timelines.stream()
                .map(this::convertToDto)
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTimelineEvent(String registrationId, TimelineEventType eventType,
                                    String eventDescription, TimelineOperatorType operatorType,
                                    String operatorName) {
        ChronicDiseaseRegistrationTimeline timeline = new ChronicDiseaseRegistrationTimeline(
                registrationId, eventType, eventDescription, operatorType, operatorName);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录时间线事件失败，申报ID: {}, 事件类型: {}, 错误信息: {}",
                    registrationId, eventType, e.getMessage(), e);
            throw new TimelineRecordException("记录时间线事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordStatusChangeEvent(String registrationId, TimelineEventType eventType,
                                        String eventDescription, TimelineOperatorType operatorType,
                                        String operatorName, ChronicDiseaseRegistrationStatus oldStatus,
                                        ChronicDiseaseRegistrationStatus newStatus) {
        ChronicDiseaseRegistrationTimeline timeline = new ChronicDiseaseRegistrationTimeline(
                registrationId, eventType, eventDescription, operatorType, operatorName, oldStatus, newStatus);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录状态变更事件失败，申报ID: {}, 事件类型: {}, 状态变更: {} -> {}, 错误信息: {}",
                    registrationId, eventType, oldStatus, newStatus, e.getMessage(), e);
            throw new TimelineRecordException("记录状态变更事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTimelineEventWithData(String registrationId, TimelineEventType eventType,
                                            String eventDescription, TimelineOperatorType operatorType,
                                            String operatorName, Map<String, Serializable> additionalData) {
        ChronicDiseaseRegistrationTimeline timeline = new ChronicDiseaseRegistrationTimeline(
                registrationId, eventType, eventDescription, operatorType, operatorName, additionalData);
        timeline.setCreateTime(LocalDateTime.now());

        try {
            this.save(timeline);
        } catch (Exception e) {
            log.error("记录带数据的时间线事件失败，申报ID: {}, 事件类型: {}, 附加数据: {}, 错误信息: {}",
                    registrationId, eventType, additionalData, e.getMessage(), e);
            throw new TimelineRecordException("记录时间线事件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void recordRegistrationCreated(String registrationId, String operatorName) {
        getSelf().recordTimelineEvent(registrationId, TimelineEventType.REGISTRATION_CREATED,
                "创建慢病申报记录", TimelineOperatorType.APPLICANT, operatorName);
    }

    @Override
    public void recordRegistrationSubmitted(String registrationId, String operatorName,
                                            ChronicDiseaseRegistrationStatus oldStatus) {
        getSelf().recordStatusChangeEvent(registrationId, TimelineEventType.REGISTRATION_SUBMITTED,
                "提交慢病申报", TimelineOperatorType.APPLICANT, operatorName,
                oldStatus, ChronicDiseaseRegistrationStatus.SUBMITTED);
    }

    @Override
    public void recordAgentInfoSet(String registrationId, String operatorName, String agentName) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("agentName", agentName);

        getSelf().recordTimelineEventWithData(registrationId, TimelineEventType.AGENT_INFO_SET,
                "设置代办人信息：" + agentName, TimelineOperatorType.APPLICANT,
                operatorName, data);
    }

    @Override
    public void recordAgentInfoCleared(String registrationId, String operatorName) {
        getSelf().recordTimelineEvent(registrationId, TimelineEventType.AGENT_INFO_CLEARED,
                "清除代办人信息", TimelineOperatorType.APPLICANT, operatorName);
    }

    @Override
    public void recordDiseaseSelected(String registrationId, String operatorName, String diseaseName) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("diseaseName", diseaseName);

        getSelf().recordTimelineEventWithData(registrationId, TimelineEventType.DISEASE_SELECTED,
                "选择慢病病种：" + diseaseName, TimelineOperatorType.APPLICANT,
                operatorName, data);
    }

    @Override
    public void recordDocumentsUploaded(String registrationId, String operatorName, Integer attachmentCount) {
        Map<String, Serializable> data = new HashMap<>();
        data.put("attachmentCount", attachmentCount);

        getSelf().recordTimelineEventWithData(registrationId, TimelineEventType.DOCUMENTS_UPLOADED,
                "上传认定资料，共 " + attachmentCount + " 个附件",
                TimelineOperatorType.APPLICANT, operatorName, data);
    }

    /**
     * 将时间线实体转换为DTO
     */
    private TimelineEventDto convertToDto(ChronicDiseaseRegistrationTimeline timeline) {
        TimelineEventDto dto = new TimelineEventDto();
        dto.setId(timeline.getId());
        dto.setEventType(timeline.getEventType());
        dto.setEventDescription(timeline.getEventDescription());
        dto.setOperatorType(timeline.getOperatorType());
        dto.setOperatorName(timeline.getOperatorName());
        dto.setOldStatus(timeline.getOldStatus());
        dto.setNewStatus(timeline.getNewStatus());
        dto.setEventTime(timeline.getEventTime());
        dto.setAdditionalData(timeline.getAdditionalData());

        // 设置图标和颜色
        dto.setIconAndColor();

        return dto;
    }
} 