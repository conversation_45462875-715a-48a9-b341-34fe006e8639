package space.lzhq.ph.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration;
import space.lzhq.ph.domain.OutpatientStopClinic;
import space.lzhq.ph.domain.Reservation;
import space.lzhq.ph.domain.WxSubscribeMessage;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.mapper.OutpatientStopClinicMapper;
import space.lzhq.ph.mapper.ReservationMapper;
import space.lzhq.ph.service.OutpatientStopClinicService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 门诊停诊消息服务实现
 */
@Service
@RequiredArgsConstructor
public class OutpatientStopClinicServiceImpl extends ServiceImpl<OutpatientStopClinicMapper, OutpatientStopClinic> implements OutpatientStopClinicService {

    private final ReservationMapper reservationMapper;
    private final WxSubscribeMessageConfiguration wxSubscribeMessageConfiguration;

    @Override
    public IPage<OutpatientStopClinic> getPageByPatientId(String patientId, long page, long size) {
        Page<OutpatientStopClinic> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<OutpatientStopClinic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OutpatientStopClinic::getSourcePatientId, patientId)
                .orderByDesc(OutpatientStopClinic::getCreateTime);
        return page(pageParam, queryWrapper);
    }

    @Override
    public List<OutpatientStopClinic> getListByPatientId(String patientId) {
        LambdaQueryWrapper<OutpatientStopClinic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OutpatientStopClinic::getSourcePatientId, patientId)
                .orderByDesc(OutpatientStopClinic::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public OutpatientStopClinic getOneByAppointmentId(String appointmentId) {
        LambdaQueryWrapper<OutpatientStopClinic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OutpatientStopClinic::getAppointmentId, appointmentId);
        return getOne(queryWrapper);
    }

    @Override
    public boolean existsByAppointmentId(String appointmentId) {
        LambdaQueryWrapper<OutpatientStopClinic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OutpatientStopClinic::getAppointmentId, appointmentId);
        return exists(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Long id) {
        OutpatientStopClinic stopClinic = new OutpatientStopClinic();
        stopClinic.setId(id);
        stopClinic.setIsRead(true);
        return updateById(stopClinic);
    }

    @Override
    public void sendSubscribeMessage(OutpatientStopClinic stopClinic) {
        Reservation reservation = reservationMapper.selectOneByReservationNumber(stopClinic.getAppointmentId());
        if (reservation == null) {
            stopClinic.setSubscribeMessageError("未关联微信用户，无法发送订阅消息");
            return;
        }

        stopClinic.setOpenId(reservation.getOpenid());
        try {
            WxSubscribeMessage wxSubscribeMessage = WeixinExt.INSTANCE.sendSubscribeMessage(
                    wxSubscribeMessageConfiguration.stopClinicId,
                    wxSubscribeMessageConfiguration.stopClinicPage,
                    buildMessageData(stopClinic),
                    stopClinic.getOpenId(),
                    stopClinic.getAppointmentId()
            );
            stopClinic.setSubscribeMessageSendTime(LocalDateTime.now());
            if (wxSubscribeMessage != null) {
                stopClinic.setSubscribeMessageError(wxSubscribeMessage.getError());
                stopClinic.setSubscribeMessageSendStatus(wxSubscribeMessage.getSendStatus());
            } else {
                stopClinic.setSubscribeMessageError("订阅消息未发送");
                stopClinic.setSubscribeMessageSendStatus(null);
            }
            updateById(stopClinic);
        } catch (Exception e) {
            stopClinic.setSubscribeMessageSendTime(LocalDateTime.now());
            stopClinic.setSubscribeMessageSendStatus(false);
            stopClinic.setSubscribeMessageError(e.getMessage());
            updateById(stopClinic);
        }
    }

    private List<WxMaSubscribeMessage.MsgData> buildMessageData(OutpatientStopClinic stopClinic) {
        List<WxMaSubscribeMessage.MsgData> messageDataList = new ArrayList<>();
        // 温馨提示
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing1",
                stopClinic.getMessageContent()
        ));
        // 科室
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing3",
                stopClinic.getRegisteredDeptName()
        ));
        // 医生
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing12",
                stopClinic.getRegisteredDoctorName()
        ));
        // 就诊日期
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "date5",
                stopClinic.getAppointmentDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
        ));
        // 姓名
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessagePhraseData(
                "phrase6",
                stopClinic.getName()
        ));
        return messageDataList;
    }
}