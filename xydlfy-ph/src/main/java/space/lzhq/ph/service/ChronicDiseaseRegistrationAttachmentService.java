package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType;

import java.util.Optional;

public interface ChronicDiseaseRegistrationAttachmentService extends IService<ChronicDiseaseRegistrationAttachment> {

    Optional<ChronicDiseaseRegistrationAttachment> save(
            @NotBlank @Size(min = 16, max = 28) String openId,
            @NotNull ChronicDiseaseRegistrationAttachmentType type,
            @NotBlank @Size(max = 255) String fileName,
            @NotBlank @Size(max = 255) String fileUrl
    );

    boolean updateRegistrationId(@NotBlank String attachmentId, @NotBlank String registrationId);

    /**
     * 删除附件记录及关联文件
     *
     * @param id 附件ID
     * @return 是否删除成功
     */
    boolean deleteAttachment(@NotBlank String id);
}
