package space.lzhq.ph.service.impl;

import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.dromara.hutool.core.text.StrUtil;
import org.mospital.alipay.AlipayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.AlipayRefund;
import space.lzhq.ph.mapper.AlipayRefundMapper;
import space.lzhq.ph.service.IAlipayRefundService;

import java.util.List;

/**
 * 支付宝退款记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-09-10
 */
@Service
public class AlipayRefundServiceImpl implements IAlipayRefundService {
    @Autowired
    private AlipayRefundMapper alipayRefundMapper;

    /**
     * 查询支付宝退款记录
     *
     * @param id 支付宝退款记录ID
     * @return 支付宝退款记录
     */
    @Override
    public AlipayRefund selectAlipayRefundById(Long id) {
        return alipayRefundMapper.selectAlipayRefundById(id);
    }

    /**
     * 查询支付宝退款记录列表
     *
     * @param alipayRefund 支付宝退款记录
     * @return 支付宝退款记录
     */
    @Override
    public List<AlipayRefund> selectAlipayRefundList(AlipayRefund alipayRefund) {
        return alipayRefundMapper.selectAlipayRefundList(alipayRefund);
    }

    /**
     * 新增支付宝退款记录
     *
     * @param alipayRefund 支付宝退款记录
     * @return 结果
     */
    @Override
    public int insertAlipayRefund(AlipayRefund alipayRefund) {
        alipayRefund.setCreateTime(DateUtils.getNowDate());
        return alipayRefundMapper.insertAlipayRefund(alipayRefund);
    }

    /**
     * 修改支付宝退款记录
     *
     * @param alipayRefund 支付宝退款记录
     * @return 结果
     */
    @Override
    public int updateAlipayRefund(AlipayRefund alipayRefund) {
        return alipayRefundMapper.updateAlipayRefund(alipayRefund);
    }

    /**
     * 删除支付宝退款记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteAlipayRefundByIds(String ids) {
        return alipayRefundMapper.deleteAlipayRefundByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除支付宝退款记录信息
     *
     * @param id 支付宝退款记录ID
     * @return 结果
     */
    @Override
    public int deleteAlipayRefundById(Long id) {
        return alipayRefundMapper.deleteAlipayRefundById(id);
    }

    @Override
    public boolean updateOnAlipayRefund(AlipayRefund refund, AlipayTradeRefundResponse refundResponse) {
        if (refundResponse.isSuccess() && refundResponse.getFundChange().equals("Y")) {
            refund.setAlipayTradeStatus(Constants.REFUND_SUCCESS);
            refund.setAlipayTradeTime(refundResponse.getGmtRefundPay());
            if (refund.getManualRefundState() == Constants.MANUAL_REQUESTED) {
                refund.setManualRefundState(Constants.MANUAL_APPROVED);
            }
        } else {
            refund.setAlipayTradeStatus(refundResponse.getSubCode() + "-" + refundResponse.getSubMsg());
        }
        return 1 == updateAlipayRefund(refund);
    }

    @Override
    public boolean updateOnAlipayRefund(
            AlipayRefund refund,
            AlipayTradeFastpayRefundQueryResponse queryRefundResponse
    ) {
        if (!refund.getAlipayTradeStatus().equals(Constants.REFUND_SUCCESS)) {
            if (queryRefundResponse.isSuccess()) {
                refund.setAlipayTradeStatus(queryRefundResponse.getRefundStatus());
                refund.setAlipayTradeTime(queryRefundResponse.getGmtRefundPay());
                if (refund.getManualRefundState() == Constants.MANUAL_REQUESTED) {
                    refund.setManualRefundState(Constants.MANUAL_APPROVED);
                }
                return 1 == updateAlipayRefund(refund);
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    @Override
    public void refundAlipay(AlipayRefund refund) {
        if (StrUtil.isBlank(refund.getOutRefundNo())) {
            String outRefundNo = PaymentKit.INSTANCE.newOrderId(ServiceType.Companion.fromCode(refund.getType()));
            refund.setOutRefundNo(outRefundNo);
        }

        AlipayTradeRefundResponse refundResponse = AlipayService.INSTANCE.refund(
                PaymentKit.INSTANCE.fenToYuan(refund.getAmount()),
                refund.getTradeNo(),
                refund.getOutRefundNo()
        );
        updateOnAlipayRefund(refund, refundResponse);
    }
}
