package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import space.lzhq.ph.domain.MipWxOrder;

import java.time.LocalDateTime;
import java.util.List;

public interface IMipWxOrderService extends IService<MipWxOrder> {

    MipWxOrder getOneByPayOrderId(String payOrderId);

    MipWxOrder getOneByMedTransactionId(String medTransactionId);

    WxInsurancePayOrderQueryResult refreshOrderStatus(String medTransactionId) throws WxPayException;

    List<MipWxOrder> queryUnlockableOrders(String patientIdCardNo);

    boolean canUnlock(MipWxOrder mipWxOrder);

    void updateUnlockTime(Long id, LocalDateTime unlockTime);

    boolean updateOnRefund(MipWxOrder mipWxOrder, WxInsurancePayRefundResult refundResult);

}
