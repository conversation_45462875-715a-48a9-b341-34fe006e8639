package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.WeixinTransferReconciliationDetail;

import java.time.LocalDate;
import java.util.List;

public interface IWeixinTransferReconciliationDetailService extends IService<WeixinTransferReconciliationDetail> {

    List<WeixinTransferReconciliationDetail> listByBillDate(LocalDate billDate);

    List<WeixinTransferReconciliationDetail> listNotBalanced(LocalDate billDate);

    boolean deleteByBillDate(LocalDate billDate);

}