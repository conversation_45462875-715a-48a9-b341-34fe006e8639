package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.ShiroUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.ChronicDisease;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationAttachmentMapper;
import space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationMapper;
import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationApprovalDto;
import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationSearchForm;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationAdminListResponseDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationDetailResponseDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationStatisticsDto;
import space.lzhq.ph.dto.response.attachment.ChronicDiseaseRegistrationAttachmentDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.InsuranceType;
import space.lzhq.ph.enums.TimelineEventType;
import space.lzhq.ph.enums.TimelineOperatorType;
import space.lzhq.ph.exception.*;
import space.lzhq.ph.mapper.ChronicDiseaseExpertRelationMapper;
import space.lzhq.ph.service.ChronicDiseaseExpertService;
import space.lzhq.ph.service.ChronicDiseaseRegistrationAdminService;
import space.lzhq.ph.service.ChronicDiseaseRegistrationAttachmentService;
import space.lzhq.ph.service.ChronicDiseaseRegistrationTimelineService;
import space.lzhq.ph.service.IChronicDiseaseService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 慢病申报管理端服务实现类
 */
@Slf4j
@Service
@Validated
public class ChronicDiseaseRegistrationAdminServiceImpl
        extends ServiceImpl<space.lzhq.ph.mapper.ChronicDiseaseRegistrationMapper, ChronicDiseaseRegistration>
        implements ChronicDiseaseRegistrationAdminService {

    /**
     * 慢病专家角色ID
     */
    private static final Long EXPERT_ROLE_ID = 17L;

    private final ChronicDiseaseRegistrationAttachmentService attachmentService;

    private final IChronicDiseaseService chronicDiseaseService;

    private final ChronicDiseaseRegistrationTimelineService timelineService;

    private final ChronicDiseaseExpertRelationMapper expertRelationMapper;

    private final ChronicDiseaseExpertService expertService;

    public ChronicDiseaseRegistrationAdminServiceImpl(
            ChronicDiseaseRegistrationAttachmentService attachmentService,
            IChronicDiseaseService chronicDiseaseService,
            ChronicDiseaseRegistrationTimelineService timelineService,
            ChronicDiseaseExpertRelationMapper expertRelationMapper,
            ChronicDiseaseExpertService expertService
    ) {
        this.attachmentService = attachmentService;
        this.chronicDiseaseService = chronicDiseaseService;
        this.timelineService = timelineService;
        this.expertRelationMapper = expertRelationMapper;
        this.expertService = expertService;
    }

    /**
     * 获取当前代理对象，用于调用事务方法
     */
    private ChronicDiseaseRegistrationAdminServiceImpl getSelf() {
        return (ChronicDiseaseRegistrationAdminServiceImpl) AopContext.currentProxy();
    }

    /**
     * 获取当前用户（如果是慢病专家的话）
     *
     * @return 如果当前用户是慢病专家则返回用户对象，否则返回null
     */
    private SysUser getCurrentExpertUser() {
        try {
            SysUser currentUser = ShiroUtils.getSysUser();
            if (currentUser == null || currentUser.getRoles() == null) {
                return null;
            }

            boolean isExpert = currentUser.getRoles().stream()
                    .anyMatch(role -> EXPERT_ROLE_ID.equals(role.getRoleId()));
            
            return isExpert ? currentUser : null;
        } catch (Exception e) {
            log.error("获取慢病专家用户信息时发生异常", e);
            return null;
        }
    }

    /**
     * 获取专家关联的病种ID列表
     *
     * @param expertId 专家ID（用户ID）
     * @return 病种ID列表，如果没有关联病种则返回空列表
     */
    private List<Integer> getExpertDiseaseIds(Long expertId) {
        try {
            List<Integer> diseaseIds = expertRelationMapper.selectDiseaseIdsByExpertId(expertId);
            return diseaseIds != null ? diseaseIds : Collections.emptyList();
        } catch (Exception e) {
            log.error("查询专家 {} 关联的病种时发生异常", expertId, e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChronicDiseaseRegistrationAdminListResponseDto> searchRegistrations(
            ChronicDiseaseRegistrationSearchForm searchForm) {

        // 构建查询条件
        LambdaQueryWrapper<ChronicDiseaseRegistration> queryWrapper = buildSearchQueryWrapper(searchForm);

        // 查询申报记录
        List<ChronicDiseaseRegistration> registrations = this.list(queryWrapper);

        if (registrations.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取病种信息映射
        Map<Integer, ChronicDisease> diseaseMap = buildDiseaseMap(registrations);

        // 转换为DTO并进行数据脱敏
        return registrations.stream()
                .map(registration -> {
                    ChronicDiseaseRegistrationAdminListResponseDto dto = convertToAdminListDto(registration);

                    // 设置病种信息
                    if (registration.getDiseaseId() != null && diseaseMap.containsKey(registration.getDiseaseId())) {
                        ChronicDisease disease = diseaseMap.get(registration.getDiseaseId());
                        dto.setDiseaseName(disease.getName());
                        dto.setDiseaseId(disease.getId());
                    }

                    // 设置是否可审批
                    dto.setCanApprove(registration.getStatus().canApprove());

                    // 设置是否可删除
                    dto.setCanDelete(registration.getStatus().canDelete());

                    return dto;
                })
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public ChronicDiseaseRegistrationDetailResponseDto getAdminDetailById(
            @NotBlank @Size(min = 32, max = 32) String id) {

        // 获取申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "ChronicDiseaseRegistration", id);
        }

        // 慢病专家权限检查
        SysUser currentExpertUser = getCurrentExpertUser();
        if (currentExpertUser != null) {
            List<Integer> expertDiseaseIds = getExpertDiseaseIds(currentExpertUser.getUserId());

            if (!expertDiseaseIds.contains(registration.getDiseaseId())) {
                throw new UnauthorizedResourceAccessException(
                        String.format("慢病专家 %s 无权查看病种ID为 %d 的申报记录",
                                currentExpertUser.getUserName(), registration.getDiseaseId()));
            }
        }

        // 转换为详情DTO
        ChronicDiseaseRegistrationDetailResponseDto detailDto =
                ChronicDiseaseRegistrationMapper.INSTANCE.toDetailResponseDto(registration);

        // 填充附件信息
        populateAttachments(registration, detailDto);

        // 填充病种信息（包含专家信息）
        if (registration.getDiseaseId() != null) {
            ChronicDisease disease = getDiseaseWithExperts(registration.getDiseaseId());
            if (disease != null) {
                detailDto.setDisease(disease);
            }
        }

        detailDto.setCanApprove(registration.getStatus().canApprove());

        return detailDto;
    }

    /**
     * 获取包含专家信息的病种详情
     *
     * @param diseaseId 病种ID
     * @return 包含专家信息的病种对象，如果不存在则返回null
     */
    private ChronicDisease getDiseaseWithExperts(Integer diseaseId) {
        try {
            // 获取病种基本信息
            ChronicDisease disease = chronicDiseaseService.getById(diseaseId);
            if (disease == null) {
                return null;
            }

            // 获取关联的专家ID列表
            List<Long> expertIds = chronicDiseaseService.getExpertIdsByDiseaseId(diseaseId);
            disease.setExpertIds(expertIds);

            // 批量获取专家信息并设置专家姓名列表
            if (!expertIds.isEmpty()) {
                Map<Long, SysUser> expertMap = expertService.selectExpertsByIds(expertIds);
                List<String> expertNames = expertIds.stream()
                        .map(expertMap::get)
                        .filter(Objects::nonNull)
                        .map(SysUser::getUserName)
                        .toList();
                disease.setExpertNames(expertNames);
            }

            return disease;
        } catch (Exception e) {
            log.error("获取病种 {} 详情时发生异常", diseaseId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChronicDiseaseRegistration approveRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid ChronicDiseaseRegistrationApprovalDto approvalDto,
            @NotBlank @Size(min = 1, max = 50) String approverName) {
        // 获取申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "ChronicDiseaseRegistration", id);
        }

        // 慢病专家权限检查
        SysUser currentExpertUser = getCurrentExpertUser();
        if (currentExpertUser != null) {
            List<Integer> expertDiseaseIds = getExpertDiseaseIds(currentExpertUser.getUserId());

            if (!expertDiseaseIds.contains(registration.getDiseaseId())) {
                throw new UnauthorizedResourceAccessException(
                        String.format("慢病专家 %s 无权审批病种ID为 %d 的申报记录",
                                currentExpertUser.getUserName(), registration.getDiseaseId()));
            }
        }

        // 验证是否可以审批
        if (!registration.getStatus().canApprove()) {
            throw new ApprovalNotAllowedException(
                    String.format("申报记录状态为 %s，不允许审批", registration.getStatus().getDescription()),
                    registration.getStatus()
            );
        }

        // 验证审批信息
        validateApprovalDto(approvalDto);

        // 更新申报记录
        registration.setStatus(approvalDto.getStatus());
        registration.setApproveTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 根据审批结果设置相关字段
        if (approvalDto.getStatus() == ChronicDiseaseRegistrationStatus.REJECTED) {
            registration.setRejectReason(approvalDto.getRejectReason());
            registration.setCorrectionAdvice(approvalDto.getCorrectionAdvice());
        } else if (approvalDto.getStatus() == ChronicDiseaseRegistrationStatus.APPROVED) {
            // 清空之前的驳回信息
            registration.setRejectReason(null);
            registration.setCorrectionAdvice(null);
        }

        // 保存更新
        boolean success = this.updateById(registration);
        if (!success) {
            throw new DataUpdateFailedException("审批失败，数据库更新失败");
        }

        // 记录时间线
        safeRecordTimeline(() -> {
            TimelineEventType eventType = approvalDto.getStatus() == ChronicDiseaseRegistrationStatus.APPROVED ?
                    TimelineEventType.REGISTRATION_APPROVED : TimelineEventType.REGISTRATION_REJECTED;
            String description = String.format("管理员 %s %s申报", approverName, eventType.getDescription());
            if (StringUtils.isNotBlank(approvalDto.getRejectReason())) {
                description += "，驳回原因：" + approvalDto.getRejectReason();
            }
            timelineService.recordTimelineEvent(id, eventType, description, TimelineOperatorType.REVIEWER, approverName);
        });

        return registration;
    }

    @Override
    public int batchApproveRegistrations(
            @NotNull @Size(min = 1) List<@NotBlank @Size(min = 32, max = 32) String> ids,
            @NotNull @Valid ChronicDiseaseRegistrationApprovalDto approvalDto,
            @NotBlank @Size(min = 1, max = 50) String approverName) {
        // 验证审批信息
        validateApprovalDto(approvalDto);

        // 如果是慢病专家，预先检查权限
        SysUser currentExpertUser = getCurrentExpertUser();
        if (currentExpertUser != null) {
            List<Integer> expertDiseaseIds = getExpertDiseaseIds(currentExpertUser.getUserId());

            for (String id : ids) {
                ChronicDiseaseRegistration registration = this.getById(id);
                if (registration != null && !expertDiseaseIds.contains(registration.getDiseaseId())) {
                    throw new UnauthorizedResourceAccessException(
                            String.format("慢病专家 %s 无权批量审批病种ID为 %d 的申报记录",
                                    currentExpertUser.getUserName(), registration.getDiseaseId()));
                }
            }
        }

        int successCount = 0;

        for (String id : ids) {
            try {
                getSelf().approveRegistration(id, approvalDto, approverName);
                successCount++;
            } catch (Exception e) {
                log.warn("批量审批中单个记录审批失败，ID: {}, 错误: {}", id, e.getMessage());
            }
        }
        return successCount;
    }

    @Override
    @Transactional(readOnly = true)
    public ChronicDiseaseRegistrationStatisticsDto getStatistics() {
        // 构建查询条件，包含权限过滤
        LambdaQueryWrapper<ChronicDiseaseRegistration> queryWrapper = buildSearchQueryWrapper(null);

        // 查询申报记录（已应用权限过滤）
        List<ChronicDiseaseRegistration> allRegistrations = this.list(queryWrapper);

        ChronicDiseaseRegistrationStatisticsDto statistics = new ChronicDiseaseRegistrationStatisticsDto();

        // 计算各状态统计
        Map<ChronicDiseaseRegistrationStatus, Long> statusCounts = allRegistrations.stream()
                .collect(Collectors.groupingBy(
                        ChronicDiseaseRegistration::getStatus,
                        Collectors.counting()
                ));

        statistics.setTotalCount((long) allRegistrations.size());
        statistics.setPendingApprovalCount(statusCounts.getOrDefault(ChronicDiseaseRegistrationStatus.SUBMITTED, 0L));
        statistics.setApprovedCount(statusCounts.getOrDefault(ChronicDiseaseRegistrationStatus.APPROVED, 0L));
        statistics.setRejectedCount(statusCounts.getOrDefault(ChronicDiseaseRegistrationStatus.REJECTED, 0L));

        // 计算今日统计
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        long todayCount = allRegistrations.stream()
                .filter(reg -> reg.getCreateTime().isAfter(startOfDay) && reg.getCreateTime().isBefore(endOfDay))
                .count();
        statistics.setTodayCount(todayCount);

        // 计算医保类型分布
        Map<InsuranceType, Long> insuranceTypeCounts = allRegistrations.stream()
                .filter(reg -> reg.getInsuranceType() != null)
                .collect(Collectors.groupingBy(
                        ChronicDiseaseRegistration::getInsuranceType,
                        Collectors.counting()
                ));
        statistics.setInsuranceTypeDistribution(insuranceTypeCounts);

        // 计算病种分布（需要查询病种信息）
        Map<Integer, Long> diseaseIdCounts = allRegistrations.stream()
                .filter(reg -> reg.getDiseaseId() != null)
                .collect(Collectors.groupingBy(
                        ChronicDiseaseRegistration::getDiseaseId,
                        Collectors.counting()
                ));

        if (!diseaseIdCounts.isEmpty()) {
            List<ChronicDisease> diseases = chronicDiseaseService.listByIds(diseaseIdCounts.keySet());
            Map<String, Long> diseaseDistribution = diseases.stream()
                    .collect(Collectors.toMap(
                            ChronicDisease::getName,
                            disease -> diseaseIdCounts.getOrDefault(disease.getId(), 0L)
                    ));
            statistics.setDiseaseDistribution(diseaseDistribution);
        } else {
            statistics.setDiseaseDistribution(Collections.emptyMap());
        }

        // 计算最近7天的每日统计
        List<ChronicDiseaseRegistrationStatisticsDto.DailyStatistic> dailyStatistics =
                calculateDailyStatistics(allRegistrations);
        statistics.setDailyTrend(dailyStatistics);

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChronicDiseaseRegistration> exportRegistrations(
            ChronicDiseaseRegistrationSearchForm searchForm) {
        // 构建查询条件
        LambdaQueryWrapper<ChronicDiseaseRegistration> queryWrapper = buildSearchQueryWrapper(searchForm);

        // 查询并返回原始记录（用于导出，不进行数据脱敏）
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 1, max = 50) String operatorName) {
        // 获取申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "ChronicDiseaseRegistration", id);
        }

        // 慢病专家权限检查
        SysUser currentExpertUser = getCurrentExpertUser();
        if (currentExpertUser != null) {
            List<Integer> expertDiseaseIds = getExpertDiseaseIds(currentExpertUser.getUserId());

            if (!expertDiseaseIds.contains(registration.getDiseaseId())) {
                throw new UnauthorizedResourceAccessException(
                        String.format("慢病专家 %s 无权删除病种ID为 %d 的申报记录",
                                currentExpertUser.getUserName(), registration.getDiseaseId()));
            }
        }

        // 验证是否可以删除
        if (!registration.getStatus().canDelete()) {
            throw new DeleteNotAllowedException(
                    String.format("申报记录状态为 %s，不允许删除", registration.getStatus().getDescription())
            );
        }

        // 软删除（设置删除标记）
        registration.setDeleteFlag(true);
        registration.setUpdateTime(LocalDateTime.now());

        boolean success = this.updateById(registration);
        if (!success) {
            throw new DataUpdateFailedException("删除失败，数据库更新失败");
        }

        // 记录时间线
        safeRecordTimeline(() -> {
            String description = String.format("管理员 %s 删除申报记录", operatorName);
            timelineService.recordTimelineEvent(id, TimelineEventType.REGISTRATION_DELETED, description, TimelineOperatorType.ADMIN, operatorName);
        });

        return true;
    }

    /**
     * 构建搜索查询条件
     */
    private LambdaQueryWrapper<ChronicDiseaseRegistration> buildSearchQueryWrapper(
            ChronicDiseaseRegistrationSearchForm searchForm) {

        LambdaQueryWrapper<ChronicDiseaseRegistration> queryWrapper = Wrappers.lambdaQuery();

        // 按提交时间降序排列，NULL值排在最后
        queryWrapper.last("ORDER BY IFNULL(submit_time, '1970-01-01 00:00:00') DESC");

        if (searchForm != null) {
            // 优先处理有索引的提交时间条件，让数据库优化器优先使用索引
            if (searchForm.getSubmitDateStart() != null) {
                queryWrapper.ge(ChronicDiseaseRegistration::getSubmitTime, searchForm.getSubmitDateStart().atStartOfDay());
            }
            if (searchForm.getSubmitDateEnd() != null) {
                queryWrapper.le(ChronicDiseaseRegistration::getSubmitTime, searchForm.getSubmitDateEnd().atTime(LocalTime.MAX));
            }

            // 申报状态（如果有状态索引也应该优先处理）
            if (searchForm.getStatus() != null) {
                queryWrapper.eq(ChronicDiseaseRegistration::getStatus, searchForm.getStatus());
            }

            // 患者姓名
            if (StringUtils.isNotBlank(searchForm.getPatientName())) {
                queryWrapper.like(ChronicDiseaseRegistration::getPatientName, searchForm.getPatientName().trim());
            }

            // 身份证号
            if (StringUtils.isNotBlank(searchForm.getPatientIdCardNo())) {
                queryWrapper.like(ChronicDiseaseRegistration::getPatientIdCardNo, searchForm.getPatientIdCardNo().trim());
            }

            // 手机号
            if (StringUtils.isNotBlank(searchForm.getPatientMobile())) {
                queryWrapper.like(ChronicDiseaseRegistration::getPatientMobile, searchForm.getPatientMobile().trim());
            }
        }

        // 慢病专家权限过滤（无索引的条件放在后面）
        SysUser currentExpertUser = getCurrentExpertUser();
        if (currentExpertUser != null) {
            List<Integer> expertDiseaseIds = getExpertDiseaseIds(currentExpertUser.getUserId());
            
            if (expertDiseaseIds.isEmpty()) {
                // 如果专家没有关联任何病种，返回空结果
                queryWrapper.eq(ChronicDiseaseRegistration::getId, "IMPOSSIBLE_ID");
                return queryWrapper;
            } else {
                // 只查询专家关联的病种的申报记录
                queryWrapper.in(ChronicDiseaseRegistration::getDiseaseId, expertDiseaseIds);
            }
        }

        // 默认查询未删除的记录（无索引条件放在最后）
        queryWrapper.eq(ChronicDiseaseRegistration::getDeleteFlag, 0);

        return queryWrapper;
    }

    /**
     * 构建病种信息映射
     */
    private Map<Integer, ChronicDisease> buildDiseaseMap(List<ChronicDiseaseRegistration> registrations) {
        Set<Integer> diseaseIds = registrations.stream()
                .map(ChronicDiseaseRegistration::getDiseaseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (diseaseIds.isEmpty()) {
            return Collections.emptyMap();
        }

        List<ChronicDisease> diseases = chronicDiseaseService.listByIds(diseaseIds);
        return diseases.stream()
                .collect(Collectors.toMap(ChronicDisease::getId, Function.identity()));
    }

    /**
     * 转换为管理端列表DTO并进行数据脱敏
     */
    private ChronicDiseaseRegistrationAdminListResponseDto convertToAdminListDto(
            ChronicDiseaseRegistration registration) {

        ChronicDiseaseRegistrationAdminListResponseDto dto = new ChronicDiseaseRegistrationAdminListResponseDto();

        // 基本信息
        dto.setId(registration.getId());
        dto.setPatientName(registration.getPatientName());
        dto.setPatientIdCardNo(registration.getPatientIdCardNo());
        dto.setPatientMobile(registration.getPatientMobile());
        dto.setInsuranceType(registration.getInsuranceType());
        dto.setPatientInsuranceCardNo(registration.getPatientInsuranceCardNo());
        dto.setStatus(registration.getStatus());
        dto.setCreateTime(registration.getCreateTime());
        dto.setSubmitTime(registration.getSubmitTime());
        dto.setUpdateTime(registration.getUpdateTime());
        dto.setApproveTime(registration.getApproveTime());

        // 代办信息
        dto.setAgentFlag(registration.getAgentFlag());
        if (Objects.equals(registration.getAgentFlag(), 1)) {
            dto.setAgentName(registration.getAgentName());
            dto.setAgentIdCardNo(registration.getAgentIdCardNo());
            dto.setAgentMobile(registration.getAgentMobile());
            dto.setAgentRelation(registration.getAgentRelation());
        }

        // 驳回信息
        dto.setRejectReason(registration.getRejectReason());
        dto.setCorrectionAdvice(registration.getCorrectionAdvice());

        return dto;
    }

    /**
     * 验证审批DTO
     */
    private void validateApprovalDto(ChronicDiseaseRegistrationApprovalDto approvalDto) {
        if (approvalDto.getStatus() == ChronicDiseaseRegistrationStatus.REJECTED && StringUtils.isBlank(approvalDto.getRejectReason())) {
            throw new InvalidApprovalException("驳回申报时必须提供驳回原因");
        }
    }

    /**
     * 填充附件信息到DTO
     */
    private void populateAttachments(ChronicDiseaseRegistration registration,
                                     ChronicDiseaseRegistrationDetailResponseDto detailDto) {
        // 收集所有附件ID
        List<String> allAttachmentIds = collectAllAttachmentIds(registration);

        if (allAttachmentIds.isEmpty()) {
            return;
        }

        // 批量查询附件并创建映射
        Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap =
                batchQueryAndMapAttachments(allAttachmentIds);

        // 设置各类附件到DTO
        setAttachmentsToDto(registration, detailDto, attachmentMap);
    }

    /**
     * 收集申报记录中的所有附件ID
     */
    private List<String> collectAllAttachmentIds(ChronicDiseaseRegistration registration) {
        List<String> allAttachmentIds = new ArrayList<>();

        // 收集单个附件ID
        if (registration.getPatientIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getPatientIdCardAttachmentId());
        }
        if (registration.getPatientInsuranceCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getPatientInsuranceCardAttachmentId());
        }
        if (registration.getAgentIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getAgentIdCardAttachmentId());
        }

        // 收集认定资料附件ID列表
        if (registration.getDocumentAttachmentIds() != null && !registration.getDocumentAttachmentIds().isEmpty()) {
            allAttachmentIds.addAll(registration.getDocumentAttachmentIds());
        }

        return allAttachmentIds.stream()
                .distinct()
                .toList();
    }

    /**
     * 批量查询附件并创建ID到附件对象的映射
     */
    private Map<String, ChronicDiseaseRegistrationAttachment> batchQueryAndMapAttachments(List<String> attachmentIds) {
        List<ChronicDiseaseRegistrationAttachment> allAttachments = attachmentService.listByIds(attachmentIds);
        return allAttachments.stream()
                .collect(Collectors.toMap(ChronicDiseaseRegistrationAttachment::getId, Function.identity()));
    }

    /**
     * 设置附件信息到DTO
     */
    private void setAttachmentsToDto(ChronicDiseaseRegistration registration,
                                     ChronicDiseaseRegistrationDetailResponseDto detailDto,
                                     Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap) {

        // 设置患者身份证附件
        if (registration.getPatientIdCardAttachmentId() != null) {
            ChronicDiseaseRegistrationAttachment attachment =
                    attachmentMap.get(registration.getPatientIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setPatientIdCardAttachment(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }

        // 设置患者医保卡附件
        if (registration.getPatientInsuranceCardAttachmentId() != null) {
            ChronicDiseaseRegistrationAttachment attachment =
                    attachmentMap.get(registration.getPatientInsuranceCardAttachmentId());
            if (attachment != null) {
                detailDto.setPatientInsuranceCardAttachment(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }

        // 设置代办人身份证附件
        if (registration.getAgentIdCardAttachmentId() != null) {
            ChronicDiseaseRegistrationAttachment attachment =
                    attachmentMap.get(registration.getAgentIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setAgentIdCardAttachment(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }

        // 设置认定资料附件
        if (registration.getDocumentAttachmentIds() != null && !registration.getDocumentAttachmentIds().isEmpty()) {
            List<ChronicDiseaseRegistrationAttachmentDto> documentAttachments =
                    registration.getDocumentAttachmentIds().stream()
                            .map(attachmentMap::get)
                            .filter(Objects::nonNull)
                            .map(space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationAttachmentMapper.INSTANCE::toDto)
                            .toList();
            detailDto.setDocumentAttachments(documentAttachments);
        }
    }

    /**
     * 计算最近N天的每日统计
     */
    private List<ChronicDiseaseRegistrationStatisticsDto.DailyStatistic> calculateDailyStatistics(
            List<ChronicDiseaseRegistration> allRegistrations
    ) {

        List<ChronicDiseaseRegistrationStatisticsDto.DailyStatistic> dailyStatistics = new ArrayList<>();

        for (int i = 7 - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

            long dayCount = allRegistrations.stream()
                    .filter(reg -> reg.getCreateTime().isAfter(startOfDay) && reg.getCreateTime().isBefore(endOfDay))
                    .count();

            ChronicDiseaseRegistrationStatisticsDto.DailyStatistic dayStat =
                    new ChronicDiseaseRegistrationStatisticsDto.DailyStatistic();
            dayStat.setDate(date);
            dayStat.setCount(dayCount);

            dailyStatistics.add(dayStat);
        }

        return dailyStatistics;
    }

    /**
     * 安全地记录时间线事件，不影响主业务逻辑
     */
    private void safeRecordTimeline(Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.warn("记录时间线事件失败，但不影响主业务逻辑: {}", e.getMessage());
        }
    }
}