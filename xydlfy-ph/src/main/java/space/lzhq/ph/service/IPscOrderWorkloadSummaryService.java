package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.PscOrderWorkloadSummary;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPscOrderWorkloadSummaryService extends IService<PscOrderWorkloadSummary> {

    List<PscOrderWorkloadSummary> listByDateRangeAndPatientDepartment(LocalDate minDate, LocalDate maxDate, String patientDepartment);

    List<PscOrderWorkloadSummary> listByDateRangeAndCarerEmployeeNo(LocalDate minDate, LocalDate maxDate, String carerEmployeeNo);

}
