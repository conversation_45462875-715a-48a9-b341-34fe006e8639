package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationTimeline;
import space.lzhq.ph.dto.response.TimelineEventDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.TimelineEventType;
import space.lzhq.ph.enums.TimelineOperatorType;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 慢病申报时间线服务接口
 *
 * <AUTHOR>
 */
public interface ChronicDiseaseRegistrationTimelineService extends IService<ChronicDiseaseRegistrationTimeline> {

    /**
     * 获取申报记录的时间线
     *
     * @param registrationId 申报记录ID
     * @param includeDetails 是否包含详细操作，false时只返回重要事件
     * @return 时间线事件列表
     */
    List<TimelineEventDto> getRegistrationTimeline(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            Boolean includeDetails
    );

    /**
     * 记录时间线事件（基础版本）
     *
     * @param registrationId   申报记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     */
    void recordTimelineEvent(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotNull TimelineEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName
    );

    /**
     * 记录状态变更时间线事件
     *
     * @param registrationId   申报记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     * @param oldStatus        变更前状态
     * @param newStatus        变更后状态
     */
    void recordStatusChangeEvent(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotNull TimelineEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName,
            ChronicDiseaseRegistrationStatus oldStatus,
            @NotNull ChronicDiseaseRegistrationStatus newStatus
    );

    /**
     * 记录包含额外数据的时间线事件
     *
     * @param registrationId   申报记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     * @param additionalData   额外数据
     */
    void recordTimelineEventWithData(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotNull TimelineEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName,
            Map<String, Serializable> additionalData
    );

    /**
     * 记录申报记录创建事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     */
    void recordRegistrationCreated(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName
    );

    /**
     * 记录申报记录提交事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     * @param oldStatus      变更前状态
     */
    void recordRegistrationSubmitted(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName,
            ChronicDiseaseRegistrationStatus oldStatus
    );

    /**
     * 记录代办人信息设置事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     * @param agentName      代办人姓名
     */
void recordAgentInfoSet(
         @NotBlank @Size(min = 32, max = 32) String registrationId,
         @NotBlank String operatorName,
        @NotBlank String agentName
 );

    /**
     * 记录代办人信息清除事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     */
    void recordAgentInfoCleared(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName
    );

    /**
     * 记录病种选择事件
     *
     * @param registrationId 申报记录ID
     * @param operatorName   操作者姓名
     * @param diseaseName    病种名称
     */
    void recordDiseaseSelected(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName,
            @NotBlank String diseaseName
    );

    /**
     * 记录认定资料上传事件
     *
     * @param registrationId  申报记录ID
     * @param operatorName    操作者姓名
     * @param attachmentCount 附件数量
     */
    void recordDocumentsUploaded(
            @NotBlank @Size(min = 32, max = 32) String registrationId,
            @NotBlank String operatorName,
            Integer attachmentCount
    );
} 