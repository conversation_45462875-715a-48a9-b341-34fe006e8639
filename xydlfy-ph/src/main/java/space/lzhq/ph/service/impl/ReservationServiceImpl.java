package space.lzhq.ph.service.impl;

import com.alipay.api.response.AlipayCommerceAppAuthUploadResponse;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.text.Convert;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.json.JSONUtil;
import org.mospital.alipay.AlipayService;
import org.mospital.alipay.AlipaySetting;
import org.mospital.alipay.HospitalOrderStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Reservation;
import space.lzhq.ph.domain.Session;
import space.lzhq.ph.mapper.ReservationMapper;
import space.lzhq.ph.service.IReservationService;
import space.lzhq.ph.service.ISessionService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static space.lzhq.ph.domain.Reservation.RESERVATION_STATUS_CANCELED;

/**
 * 预约记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
@Service
public class ReservationServiceImpl extends ServiceImpl<ReservationMapper, Reservation> implements IReservationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReservationServiceImpl.class);

    @Autowired
    private ReservationMapper reservationMapper;

    @Autowired
    private ISessionService sessionService;

    /**
     * 查询预约记录
     *
     * @param id 预约记录主键
     * @return 预约记录
     */
    @Override
    public Reservation selectReservationById(Long id) {
        return reservationMapper.selectReservationById(id);
    }

    @Override
    public Reservation selectOneByReservationNumber(String reservationNumber) {
        return reservationMapper.selectOneByReservationNumber(reservationNumber);
    }

    @Override
    public void pushAlipayHospitalOrder(Reservation reservation,
                                        HospitalOrderStatus hospitalOrderStatus,
                                        Date createTime,
                                        Date updateTime,
                                        Long reservationId
    ) {
        if (RESERVATION_STATUS_CANCELED.equals(reservation.getStatus()) && hospitalOrderStatus != HospitalOrderStatus.MERCHANT_CLOSED) {
            LOGGER.warn("订单已取消，不再推送支付宝挂号单智能消息：" + hospitalOrderStatus.getDescription());
            return;
        }

        if (!reservation.getOpenid().startsWith(Constants.ALIPAY_USER_ID_PREFIX)) {
            LOGGER.warn("只能向支付宝用户推送挂号单");
            return;
        }

        Session session = sessionService.selectSessionByOpenId(reservation.getOpenid());
        if (session == null) {
            LOGGER.warn("患者已解绑");
            return;
        }
        if (StrUtil.isBlank(session.getAhoat())) {
            LOGGER.warn("患者未授权推送智能消息");
            return;
        }

        AlipayCommerceAppAuthUploadResponse commerceAppAuthUploadResponse = AlipayService.INSTANCE.pushHospitalOrder(
                session.getAhoat(),
                reservation.getOpenid(),
                reservation.getReservationNumber() + "#" + reservation.getReservationType(),
                createTime,
                updateTime,
                BigDecimal.ZERO,
                "",
                hospitalOrderStatus,
                reservation.getDepartmentCode() + "#" + reservation.getDoctorCode() + "#" + reservation.getReservationType(),
                StrUtil.defaultIfBlank(reservation.getDepartmentName(), ""),
                StrUtil.defaultIfBlank(reservation.getDepartmentCode(), ""),
                StrUtil.defaultIfBlank(reservation.getDepartmentLocation(), "暂未提供"),
                "",
                StrUtil.defaultIfBlank(reservation.getDoctorName(), ""),
                "",
                StrUtil.defaultIfBlank(reservation.getDoctorCode(), ""),
                "",
                reservation.getName(),
                reservation.getJzTime(),
                "",
                "",
                "",
                "",
                reservation.getVisitNumber(),
                "alipays://platformapi/startapp?appId=" + AlipaySetting.INSTANCE.getMaAppId() + "&page=pages_yuyue" +
                        "/YuyueDetail/YuyueDetail&query=id%3D" +
                        (reservationId == null ? reservation.getId() : reservationId)
        );
        if (commerceAppAuthUploadResponse.isSuccess()) {
            if (StrUtil.isBlank(reservation.getAlipayHospitalOrderId())) {
                String alipayHospitalOrderId = JSONUtil.parseObj(
                        commerceAppAuthUploadResponse.getData().getResponse()
                ).getStr("order_id");
                reservation.setAlipayHospitalOrderId(alipayHospitalOrderId);
                lambdaUpdate()
                        .set(Reservation::getAlipayHospitalOrderId, alipayHospitalOrderId)
                        .eq(Reservation::getId, reservation.getId())
                        .update();
            }
        } else {
            LOGGER.warn("推送支付宝挂号单智能消息失败：" + commerceAppAuthUploadResponse.getSubCode() + "-" + commerceAppAuthUploadResponse.getSubMsg());
        }
    }

    /**
     * 查询预约记录列表
     *
     * @param reservation 预约记录
     * @return 预约记录
     */
    @Override
    public List<Reservation> selectReservationList(Reservation reservation) {
        return reservationMapper.selectReservationList(reservation);
    }

    /**
     * 新增预约记录
     *
     * @param reservation 预约记录
     * @return 结果
     */
    @Override
    public int insertReservation(Reservation reservation) {
        return reservationMapper.insertReservation(reservation);
    }

    /**
     * 修改预约记录
     *
     * @param reservation 预约记录
     * @return 结果
     */
    @Override
    public int updateReservation(Reservation reservation) {
        return reservationMapper.updateReservation(reservation);
    }

    /**
     * 批量删除预约记录
     *
     * @param ids 需要删除的预约记录主键
     * @return 结果
     */
    @Override
    public int deleteReservationByIds(String ids) {
        return reservationMapper.deleteReservationByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除预约记录信息
     *
     * @param id 预约记录主键
     * @return 结果
     */
    @Override
    public int deleteReservationById(Long id) {
        return reservationMapper.deleteReservationById(id);
    }
}
