package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.ReconciliationSearchForm;
import space.lzhq.ph.domain.WeixinTransferReconciliation;
import space.lzhq.ph.domain.WeixinTransferReconciliationChartData;

import java.time.LocalDate;
import java.util.List;

public interface IWeixinTransferReconciliationService extends IService<WeixinTransferReconciliation> {

    WeixinTransferReconciliation getOrCreate(LocalDate date);

    List<WeixinTransferReconciliation> listBySearchForm(ReconciliationSearchForm searchForm);

    boolean reconcile(LocalDate billDate);
    
    /**
     * 获取对账图表数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 图表数据
     */
    WeixinTransferReconciliationChartData getChartData(LocalDate startDate, LocalDate endDate);
}