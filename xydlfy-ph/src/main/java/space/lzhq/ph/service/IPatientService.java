package space.lzhq.ph.service;

import space.lzhq.ph.common.ClientType;
import space.lzhq.ph.domain.Patient;
import space.lzhq.ph.domain.PatientDailyIncrement;

import java.util.List;

/**
 * 就诊人Service接口
 *
 * <AUTHOR>
 * @date 2020-05-25
 */
public interface IPatientService {
    /**
     * 查询就诊人
     *
     * @param id 就诊人ID
     * @return 就诊人
     */
    Patient selectPatientById(Long id);

    /**
     * 查询就诊人列表
     *
     * @param patient 就诊人
     * @return 就诊人集合
     */
    List<Patient> selectPatientList(Patient patient);

    /**
     * 统计就诊人日增量
     *
     * @param patientDailyIncrement 就诊人日增量
     * @return 就诊人日增量
     */
    List<PatientDailyIncrement> statisticsByDay(PatientDailyIncrement patientDailyIncrement);

    /**
     * 查询就诊人列表
     *
     * @param openId 微信openId
     * @return 就诊人列表
     */
    List<Patient> selectPatientListByOpenId(String openId);

    /**
     * 查询就诊人列表
     *
     * @param idCardNo 身份证号
     * @return 就诊人列表
     */
    List<Patient> selectPatientListByIdCard(String idCardNo);

    /**
     * 查询就诊人列表
     *
     * @param patientNo 患者ID
     * @return 就诊人列表
     */
    List<Patient> selectPatientListByPatientNo(String patientNo);

    /**
     * 查询就诊人
     *
     * @param jzCardNo   就诊卡号
     * @param clientType 客户端类型
     * @return 就诊人
     */
    Patient selectPatientByJzCardNoAndClientType(String jzCardNo, ClientType clientType);

    /**
     * 查询就诊人
     *
     * @param openId 微信openId
     * @return 默认就诊人
     */
    Patient selectActivePatientByOpenId(String openId);

    /**
     * 查询就诊人
     *
     * @param idCardNo 身份证号
     * @return 就诊人
     */
    Patient selectPatientByIdCard(String idCardNo);

    /**
     * 查询住院患者
     *
     * @return 住院患者
     */
    List<Patient> selectZhuyuanPatient();

    /**
     * 查询未出院的住院患者
     *
     * @return 住院患者
     */
    List<Patient> selectZaiyuanPatient();

    /**
     * 新增就诊人
     *
     * @param patient 就诊人
     * @return 结果
     */
    int insertPatient(Patient patient);

    /**
     * 修改就诊人
     *
     * @param patient 就诊人
     * @return 结果
     */
    int updatePatient(Patient patient);

    /**
     * 批量删除就诊人
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePatientByIds(String ids);

    /**
     * 删除就诊人信息
     *
     * @param id 就诊人ID
     * @return 结果
     */
    int deletePatientById(Long id);

    void syncZhuyuanPatient(Patient patient);

}