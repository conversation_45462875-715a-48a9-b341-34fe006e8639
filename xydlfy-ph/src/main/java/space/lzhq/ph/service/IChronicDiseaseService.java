package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotNull;
import space.lzhq.ph.domain.ChronicDisease;
import space.lzhq.ph.domain.ChronicDiseaseSearchForm;
import space.lzhq.ph.dto.response.ChronicDiseaseExpertSelectionDto;
import space.lzhq.ph.enums.InsuranceType;

import java.util.List;

/**
 * 慢性病及医保支持情况Service接口
 */
public interface IChronicDiseaseService extends IService<ChronicDisease> {

    List<ChronicDisease> search(ChronicDiseaseSearchForm searchForm);

    /**
     * 根据医保类型查询支持的慢性病病种列表
     *
     * @param insuranceType 医保类型，不能为空
     * @return 支持指定医保类型的慢性病病种列表
     */
    List<ChronicDisease> findByInsuranceType(@NotNull InsuranceType insuranceType);

    /**
     * 检查指定病种是否支持给定的医保类型
     *
     * @param disease       慢性病病种，不能为空
     * @param insuranceType 医保类型，不能为空
     * @return 如果病种支持该医保类型返回true，否则返回false
     */
    boolean isDiseaseSupported(@NotNull ChronicDisease disease, @NotNull InsuranceType insuranceType);

    /**
     * 获取所有可选择的慢病专家列表
     *
     * @return 专家选择DTO列表
     */
    List<ChronicDiseaseExpertSelectionDto> getAllExpertsForSelection();

    /**
     * 保存病种专家关联关系
     *
     * @param diseaseId 病种ID
     * @param expertIds 专家ID列表
     * @return 是否保存成功
     */
    boolean saveDiseaseExpertRelation(Integer diseaseId, List<Long> expertIds);

    /**
     * 根据病种ID查询关联的专家ID列表
     *
     * @param diseaseId 病种ID
     * @return 专家ID列表
     */
    List<Long> getExpertIdsByDiseaseId(Integer diseaseId);

    /**
     * 查询病种列表并填充专家信息
     *
     * @param searchForm 搜索条件
     * @return 包含专家信息的病种列表
     */
    List<ChronicDisease> searchWithExperts(ChronicDiseaseSearchForm searchForm);

    /**
     * 保存慢病病种及其专家关联关系（事务性操作）
     *
     * @param chronicDisease 慢病病种信息
     * @return 是否保存成功
     */
    boolean saveChronicDiseaseWithExperts(ChronicDisease chronicDisease);

    /**
     * 更新慢病病种及其专家关联关系（事务性操作）
     *
     * @param chronicDisease 慢病病种信息
     * @return 是否更新成功
     */
    boolean updateChronicDiseaseWithExperts(ChronicDisease chronicDisease);

    /**
     * 删除慢病病种及其专家关联关系（事务性操作）
     *
     * @param diseaseIds 病种ID列表
     * @return 是否删除成功
     */
    boolean removeChronicDiseaseWithExperts(List<Integer> diseaseIds);

}