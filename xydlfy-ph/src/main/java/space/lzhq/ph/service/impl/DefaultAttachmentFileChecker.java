package space.lzhq.ph.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import space.lzhq.ph.ext.FileUploader;
import space.lzhq.ph.service.AttachmentFileChecker;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件检查器默认实现
 * <p>
 * 使用 FileUploader 来检查文件是否存在
 */
@Slf4j
@Service
public class DefaultAttachmentFileChecker implements AttachmentFileChecker {

    @Override
    public boolean isFileExists(String fileUrl) {
        try {
            if (fileUrl == null || fileUrl.trim().isEmpty()) {
                return false;
            }
            // FileUploader already encapsulates security checks; no need for Path munging here
            File file = FileUploader.INSTANCE.getFile(fileUrl);
            return file.exists() && file.isFile();
        } catch (Exception e) {
            log.warn("检查文件存在性时发生异常，文件URL: {}", fileUrl, e);
            return false;
        }
    }
} 