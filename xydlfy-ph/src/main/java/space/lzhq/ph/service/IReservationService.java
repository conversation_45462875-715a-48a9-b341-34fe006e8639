package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.mospital.alipay.HospitalOrderStatus;
import space.lzhq.ph.domain.Reservation;

import java.util.Date;
import java.util.List;

/**
 * 预约记录Service接口
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
public interface IReservationService extends IService<Reservation> {
    /**
     * 查询预约记录
     *
     * @param id 预约记录主键
     * @return 预约记录
     */
    Reservation selectReservationById(Long id);

    Reservation selectOneByReservationNumber(@Param("reservationNumber") String reservationNumber);

    void pushAlipayHospitalOrder(Reservation reservation,
                                 HospitalOrderStatus hospitalOrderStatus,
                                 Date createTime,
                                 Date updateTime,
                                 Long reservationId
    );

    /**
     * 查询预约记录列表
     *
     * @param reservation 预约记录
     * @return 预约记录集合
     */
    List<Reservation> selectReservationList(Reservation reservation);

    /**
     * 新增预约记录
     *
     * @param reservation 预约记录
     * @return 结果
     */
    int insertReservation(Reservation reservation);

    /**
     * 修改预约记录
     *
     * @param reservation 预约记录
     * @return 结果
     */
    int updateReservation(Reservation reservation);

    /**
     * 批量删除预约记录
     *
     * @param ids 需要删除的预约记录主键集合
     * @return 结果
     */
    int deleteReservationByIds(String ids);

    /**
     * 删除预约记录信息
     *
     * @param id 预约记录主键
     * @return 结果
     */
    int deleteReservationById(Long id);
}
