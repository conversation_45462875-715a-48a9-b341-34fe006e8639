package space.lzhq.ph.service;

import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryResult;
import space.lzhq.ph.domain.WxPayment;
import space.lzhq.ph.domain.WxRefund;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款记录Service接口
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
public interface IWxRefundService {
    /**
     * 查询退款记录
     *
     * @param id 退款记录ID
     * @return 退款记录
     */
    WxRefund selectWxRefundById(Long id);

    WxRefund selectWxRefundByZyRefundNo(String zyRefundNo);

    /**
     * 查询退款记录列表
     *
     * @param wxRefund 退款记录
     * @return 退款记录集合
     */
    List<WxRefund> selectWxRefundList(WxRefund wxRefund);

    Long sumAmount(WxRefund wxRefund);

    /**
     * 新增退款记录
     *
     * @param wxRefund 退款记录
     * @return 结果
     */
    int insertWxRefund(WxRefund wxRefund);

    /**
     * 修改退款记录
     *
     * @param wxRefund 退款记录
     * @return 结果
     */
    int updateWxRefund(WxRefund wxRefund);

    /**
     * 批量删除退款记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxRefundByIds(String ids);

    /**
     * 删除退款记录信息
     *
     * @param id 退款记录ID
     * @return 结果
     */
    int deleteWxRefundById(Long id);

    WxRefund createAndSave(WxPayment wxPayment, BigDecimal refundAmount, String outRefundNo, String hisTradeStatus);

    /**
     * 建行退款时，更新退款记录
     *
     * @param wxRefund       退款记录
     * @param refundResponse 建行退款结果
     */
    boolean updateOnCcbRefund(WxRefund wxRefund, space.lzhq.ph.pay.ccb.bean.RefundResponse refundResponse);

    /**
     * 微信退款时，更新退款记录
     *
     * @param wxRefund     退款记录
     * @param refundRecord 微信退款结果
     */
    boolean updateOnWXRefund(WxRefund wxRefund, WxPayRefundQueryResult.RefundRecord refundRecord);
}
