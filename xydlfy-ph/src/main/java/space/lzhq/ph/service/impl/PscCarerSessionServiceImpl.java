package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscCarerSession;
import space.lzhq.ph.mapper.PscCarerSessionMapper;
import space.lzhq.ph.service.IPscCarerSessionService;

import java.util.List;

/**
 * 陪检会话Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-12
 */
@Service
public class PscCarerSessionServiceImpl implements IPscCarerSessionService {
    @Autowired
    private PscCarerSessionMapper pscCarerSessionMapper;

    /**
     * 查询陪检会话
     *
     * @param id 陪检会话ID
     * @return 陪检会话
     */
    @Override
    public PscCarerSession selectPscCarerSessionById(Long id) {
        return pscCarerSessionMapper.selectPscCarerSessionById(id);
    }

    @Override
    public PscCarerSession selectPscCarerSessionByEmployeeNoAndClientType(String employeeNo, Integer clientType) {
        return pscCarerSessionMapper.selectPscCarerSessionByEmployeeNoAndClientType(employeeNo, clientType);
    }

    @Override
    public PscCarerSession selectPscCarerSessionByOpenId(String openId) {
        return pscCarerSessionMapper.selectPscCarerSessionByOpenId(openId);
    }

    /**
     * 查询陪检会话列表
     *
     * @param pscCarerSession 陪检会话
     * @return 陪检会话
     */
    @Override
    public List<PscCarerSession> selectPscCarerSessionList(PscCarerSession pscCarerSession) {
        return pscCarerSessionMapper.selectPscCarerSessionList(pscCarerSession);
    }

    @Override
    public boolean existsByEmployeeNoAndClientType(String employeeNo, Integer clientType) {
        return pscCarerSessionMapper.existsByEmployeeNoAndClientType(employeeNo, clientType);
    }

    /**
     * 新增陪检会话
     *
     * @param pscCarerSession 陪检会话
     * @return 结果
     */
    @Override
    public int insertPscCarerSession(PscCarerSession pscCarerSession) {
        return pscCarerSessionMapper.insertPscCarerSession(pscCarerSession);
    }

    /**
     * 修改陪检会话
     *
     * @param pscCarerSession 陪检会话
     * @return 结果
     */
    @Override
    public int updatePscCarerSession(PscCarerSession pscCarerSession) {
        return pscCarerSessionMapper.updatePscCarerSession(pscCarerSession);
    }

    /**
     * 删除陪检会话对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePscCarerSessionByIds(String ids) {
        return pscCarerSessionMapper.deletePscCarerSessionByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除陪检会话信息
     *
     * @param id 陪检会话ID
     * @return 结果
     */
    @Override
    public int deletePscCarerSessionById(Long id) {
        return pscCarerSessionMapper.deletePscCarerSessionById(id);
    }

    @Override
    public int deletePscCarerSessionByOpenId(String openId) {
        return pscCarerSessionMapper.deletePscCarerSessionByOpenId(openId);
    }
}
