package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.GuoguangAtmReconciliation;
import space.lzhq.ph.domain.ReconciliationSearchForm;

import java.time.LocalDate;
import java.util.List;

public interface IGuoguangAtmReconciliationService extends IService<GuoguangAtmReconciliation> {

    GuoguangAtmReconciliation getOrCreate(LocalDate date);

    List<GuoguangAtmReconciliation> listBySearchForm(ReconciliationSearchForm searchForm);

    boolean reconcile(LocalDate billDate);
}