package space.lzhq.ph.service;

import com.ruoyi.common.core.domain.Ztree;
import space.lzhq.ph.domain.JubaoCaseCategory;

import java.util.List;

/**
 * 问题类型Service接口
 */
public interface IJubaoCaseCategoryService {
    /**
     * 查询问题类型
     *
     * @param id 问题类型主键
     * @return 问题类型
     */
    JubaoCaseCategory selectJubaoCaseCategoryById(Long id);

    /**
     * 查询问题类型列表
     *
     * @param jubaoCaseCategory 问题类型
     * @return 问题类型集合
     */
    List<JubaoCaseCategory> selectJubaoCaseCategoryList(JubaoCaseCategory jubaoCaseCategory);

    /**
     * 新增问题类型
     *
     * @param jubaoCaseCategory 问题类型
     * @return 结果
     */
    int insertJubaoCaseCategory(JubaoCaseCategory jubaoCaseCategory);

    /**
     * 修改问题类型
     *
     * @param jubaoCaseCategory 问题类型
     * @return 结果
     */
    int updateJubaoCaseCategory(JubaoCaseCategory jubaoCaseCategory);

    /**
     * 批量删除问题类型
     *
     * @param ids 需要删除的问题类型主键集合
     * @return 结果
     */
    int deleteJubaoCaseCategoryByIds(String ids);

    /**
     * 删除问题类型信息
     *
     * @param id 问题类型主键
     * @return 结果
     */
    int deleteJubaoCaseCategoryById(Long id);

    /**
     * 查询问题类型树列表
     *
     * @return 所有问题类型信息
     */
    List<Ztree> selectJubaoCaseCategoryTree();
}
