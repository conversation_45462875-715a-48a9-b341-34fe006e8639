package space.lzhq.ph.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import space.lzhq.ph.dto.request.*;
import space.lzhq.ph.dto.response.ChronicDiseaseExpertListResponseDto;

import java.util.List;
import java.util.Map;

/**
 * 慢病专家管理Service接口
 */
public interface ChronicDiseaseExpertService {

    /**
     * 查询专家列表
     *
     * @param searchForm 搜索条件
     * @return 专家列表
     */
    List<ChronicDiseaseExpertListResponseDto> selectExpertList(ChronicDiseaseExpertSearchForm searchForm);

    /**
     * 查询专家列表（用于导出）
     *
     * @param searchForm 搜索条件
     * @return 专家列表
     */
    List<SysUser> selectExpertListForExport(ChronicDiseaseExpertSearchForm searchForm);

    /**
     * 根据用户ID查询专家信息
     *
     * @param userId 用户ID
     * @return 专家信息
     */
    SysUser selectExpertById(Long userId);

    /**
     * 根据用户ID列表批量查询专家信息
     *
     * @param userIds 用户ID列表
     * @return 专家ID到专家信息的映射
     */
    Map<Long, SysUser> selectExpertsByIds(List<Long> userIds);

    /**
     * 新增专家
     *
     * @param expertDto 专家信息
     * @return 结果
     */
    int insertExpert(ChronicDiseaseExpertAddDto expertDto);

    /**
     * 修改专家信息
     *
     * @param expertDto 专家信息
     * @return 结果
     */
    int updateExpert(ChronicDiseaseExpertEditDto expertDto);

    /**
     * 删除专家
     *
     * @param ids 需要删除的专家ID列表
     * @return 结果
     */
    int deleteExpertByIds(String ids);

    /**
     * 重置专家密码
     *
     * @param resetDto 重置密码信息
     * @return 结果
     */
    int resetExpertPassword(ChronicDiseaseExpertResetPasswordDto resetDto);

    /**
     * 校验登录名唯一性
     *
     * @param loginName 登录名
     * @param userId    用户ID（编辑时传入）
     * @return 是否唯一
     */
    boolean checkLoginNameUnique(String loginName, Long userId);

    /**
     * 校验手机号唯一性
     *
     * @param phonenumber 手机号
     * @param userId      用户ID（编辑时传入）
     * @return 是否唯一
     */
    boolean checkPhoneUnique(String phonenumber, Long userId);
} 