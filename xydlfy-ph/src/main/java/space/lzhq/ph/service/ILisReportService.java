package space.lzhq.ph.service;

import space.lzhq.ph.domain.LisReport;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ILisReportService {

    /**
     * 查询报告列表
     *
     * @param lisReport 查询对象
     * @return 报告列表
     */
    List<LisReport> selectLisReportList(LisReport lisReport);

    /**
     * 查询报告
     *
     * @param reportId 报告编号
     * @return 报告
     */
    LisReport selectLisReportById(String reportId);
}
