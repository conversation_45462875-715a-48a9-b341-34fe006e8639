package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.request.WxPayApplyFundFlowBillV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayFundFlowBaseResult;
import com.github.binarywang.wxpay.bean.result.WxPayFundFlowResult;
import com.ruoyi.common.ext.LambdaQueryChainWrapperExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.*;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.mapper.WeixinTransferReconciliationMapper;
import space.lzhq.ph.service.IDailyReconciliationService;
import space.lzhq.ph.service.IHisSelfPayOrderService;
import space.lzhq.ph.service.IWeixinTransferReconciliationDetailService;
import space.lzhq.ph.service.IWeixinTransferReconciliationService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static space.lzhq.ph.domain.ReconciliationDetail.*;

@Service
public class WeixinTransferReconciliationServiceImpl
        extends ServiceImpl<WeixinTransferReconciliationMapper, WeixinTransferReconciliation>
        implements IWeixinTransferReconciliationService {

    private final IHisSelfPayOrderService hisSelfPayOrderService;
    private final IWeixinTransferReconciliationDetailService reconciliationDetailService;
    private final IDailyReconciliationService dailyReconciliationService;

    @Autowired
    public WeixinTransferReconciliationServiceImpl(IHisSelfPayOrderService hisSelfPayOrderService, IWeixinTransferReconciliationDetailService reconciliationDetailService, IDailyReconciliationService dailyReconciliationService) {
        this.hisSelfPayOrderService = hisSelfPayOrderService;
        this.reconciliationDetailService = reconciliationDetailService;
        this.dailyReconciliationService = dailyReconciliationService;
    }

    @Override
    public WeixinTransferReconciliation getOrCreate(LocalDate date) {
        return lambdaQuery()
                .eq(WeixinTransferReconciliation::getDate, date)
                .oneOpt()
                .orElseGet(() -> {
                    WeixinTransferReconciliation weixinTransferReconciliation = new WeixinTransferReconciliation();
                    weixinTransferReconciliation.setDate(date);
                    return save(weixinTransferReconciliation) ? weixinTransferReconciliation : null;
                });
    }

    @Override
    public List<WeixinTransferReconciliation> listBySearchForm(ReconciliationSearchForm searchForm) {
        return new LambdaQueryChainWrapperExt<>(lambdaQuery())
                .geIfPresent(WeixinTransferReconciliation::getDate, searchForm.getStartDate())
                .leIfPresent(WeixinTransferReconciliation::getDate, searchForm.getEndDate())
                .eqIfPresent(WeixinTransferReconciliation::getTotalBalanced, searchForm.getTotalBalanced())
                .wrapper()
                .list();
    }

    @Override
    public boolean reconcile(LocalDate billDate) {
        List<HisSelfPayOrder> hisOrders = getHisOrders(billDate);
        if (hisOrders == null) {
            return false;
        }

        WxPayFundFlowResult weixinBill = WeixinExt.INSTANCE.getOrDownloadFundBill(billDate, WxPayApplyFundFlowBillV3Request.AccountType.OPERATION);
        if (weixinBill == null) {
            weixinBill = new WxPayFundFlowResult();
            weixinBill.setWxPayFundFlowBaseResultList(Collections.emptyList());
        }


        BaseReconciliation.ReconciliationResult<WeixinTransferReconciliationDetail> result = processReconciliationDetails(billDate, weixinBill, hisOrders);

        reconciliationDetailService.deleteByBillDate(billDate);
        reconciliationDetailService.saveBatch(result.reconciliationDetails(), 200);

        return updateReconciliationSummary(billDate, result);
    }

    private List<HisSelfPayOrder> getHisOrders(LocalDate billDate) {
        return hisSelfPayOrderService.listForWeixinTransfer(billDate);
    }

    private BaseReconciliation.ReconciliationResult<WeixinTransferReconciliationDetail> processReconciliationDetails(
            LocalDate billDate, WxPayFundFlowResult weixinBill, List<HisSelfPayOrder> hisOrders
    ) {
        List<WeixinTransferReconciliationDetail> reconciliationDetails = new ArrayList<>();
        Map<String, WeixinTransferReconciliationDetail> refundMap = new HashMap<>();

        BaseReconciliation.AmountSummary appSummary = processWeixinBill(billDate, weixinBill, reconciliationDetails, refundMap);

        BaseReconciliation.AmountSummary hisSummary = processHisOrders(billDate, hisOrders, reconciliationDetails, refundMap);

        return new BaseReconciliation.ReconciliationResult<>(
                reconciliationDetails,
                appSummary.payAmount(),
                appSummary.refundAmount(),
                hisSummary.payAmount(),
                hisSummary.refundAmount()
        );
    }

    private BaseReconciliation.AmountSummary processWeixinBill(
            LocalDate billDate, WxPayFundFlowResult weixinBill,
            List<WeixinTransferReconciliationDetail> reconciliationDetails,
            Map<String, WeixinTransferReconciliationDetail> refundMap) {
        BigDecimal appPayAmount = BigDecimal.ZERO;
        BigDecimal appRefundAmount = BigDecimal.ZERO;

        DateTimeFormatter billingTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (WxPayFundFlowBaseResult billDetail : weixinBill.getWxPayFundFlowBaseResultList()) {
            if (!billDetail.getBizVoucherId().startsWith(ServiceType.TF.name())) {
                continue;
            }

            BigDecimal appTradeAmount;
            if ("支出".equals(billDetail.getFinancialType())) {
                appTradeAmount = new BigDecimal(billDetail.getFinancialFee());
            } else if ("收入".equals(billDetail.getFinancialType())) {
                appTradeAmount = new BigDecimal(billDetail.getFinancialFee()).negate();
            } else {
                continue;
            }

            WeixinTransferReconciliationDetail reconciliationDetail = new WeixinTransferReconciliationDetail(billDate);
            reconciliationDetail.setAppTradeTime(LocalDateTime.parse(billDetail.getBillingTime(), billingTimeFormatter));
            reconciliationDetail.setBankOrderNo(billDetail.getBizTransactionId());
            reconciliationDetail.setAppOrderNo(billDetail.getBizVoucherId());
            reconciliationDetail.setAppTradeAmount(appTradeAmount);
            reconciliationDetail.setTradeType(TRADE_TYPE_REFUND);
            reconciliationDetail.setResult(RESULT_APP_SURPLUS);
            refundMap.put(reconciliationDetail.getAppOrderNo(), reconciliationDetail);

            appRefundAmount = appRefundAmount.add(reconciliationDetail.getAppTradeAmount());
            reconciliationDetails.add(reconciliationDetail);
        }

        return new BaseReconciliation.AmountSummary(appPayAmount, appRefundAmount);
    }

    private BaseReconciliation.AmountSummary processHisOrders(
            LocalDate billDate, List<HisSelfPayOrder> hisOrders,
            List<WeixinTransferReconciliationDetail> reconciliationDetails,
            Map<String, WeixinTransferReconciliationDetail> refundMap) {
        BigDecimal hisPayAmount = BigDecimal.ZERO;
        BigDecimal hisRefundAmount = BigDecimal.ZERO;

        for (HisSelfPayOrder hisOrder : hisOrders) {
            WeixinTransferReconciliationDetail reconciliationDetail = refundMap.get(hisOrder.getBankOrderNo());
            if (reconciliationDetail != null) {
                reconciliationDetail.setHisTradeTime(hisOrder.getTradeTime());
                reconciliationDetail.setHisTradeAmount(hisOrder.getTradeAmount());
                reconciliationDetail.setPatientId(hisOrder.getPatientId());
                reconciliationDetail.setPatientName(hisOrder.getPatientName());
                reconciliationDetail.setBusinessType(hisOrder.getRemark());
                if (reconciliationDetail.getHisTradeAmount().compareTo(reconciliationDetail.getAppTradeAmount()) != 0) {
                    reconciliationDetail.setResult(RESULT_AMOUNT_MISMATCH);
                } else {
                    reconciliationDetail.setResult(RESULT_BALANCED);
                }
            } else {
                reconciliationDetail = new WeixinTransferReconciliationDetail(billDate);
                reconciliationDetail.setTradeType(TRADE_TYPE_REFUND);
                reconciliationDetail.setHisTradeTime(hisOrder.getTradeTime());
                reconciliationDetail.setHisTradeAmount(hisOrder.getTradeAmount());
                reconciliationDetail.setAppOrderNo(hisOrder.getBankOrderNo());
                reconciliationDetail.setPatientId(hisOrder.getPatientId());
                reconciliationDetail.setPatientName(hisOrder.getPatientName());
                reconciliationDetail.setBusinessType(hisOrder.getRemark());
                reconciliationDetail.setResult(RESULT_HIS_SURPLUS);
                reconciliationDetails.add(reconciliationDetail);
            }
            hisRefundAmount = hisRefundAmount.add(reconciliationDetail.getHisTradeAmount());
        }

        return new BaseReconciliation.AmountSummary(hisPayAmount, hisRefundAmount);
    }

    private boolean updateReconciliationSummary(LocalDate billDate, BaseReconciliation.ReconciliationResult<WeixinTransferReconciliationDetail> result) {
        WeixinTransferReconciliation reconciliation = getOrCreate(billDate);
        if (reconciliation == null) {
            return false;
        }

        reconciliation.syncAmount(result.appPayAmount(), result.appRefundAmount(), result.hisPayAmount(), result.hisRefundAmount());
        boolean ok = updateById(reconciliation);
        if (ok) {
            dailyReconciliationService.sync(reconciliation);
        }

        return ok;
    }

    @Override
    public WeixinTransferReconciliationChartData getChartData(LocalDate startDate, LocalDate endDate) {
        // 确保开始日期不晚于结束日期
        LocalDate start = startDate;
        LocalDate end = endDate;
        if (start.isAfter(end)) {
            LocalDate temp = start;
            start = end;
            end = temp;
        }

        // 查询指定日期范围的对账数据
        List<WeixinTransferReconciliation> reconciliations = lambdaQuery()
                .ge(WeixinTransferReconciliation::getDate, start)
                .le(WeixinTransferReconciliation::getDate, end)
                .orderByAsc(WeixinTransferReconciliation::getDate)
                .list();

        // 初始化结果数据
        WeixinTransferReconciliationChartData chartData = new WeixinTransferReconciliationChartData();
        List<String> dates = new ArrayList<>();
        List<BigDecimal> weixinAmounts = new ArrayList<>();
        List<BigDecimal> hisAmounts = new ArrayList<>();
        List<BigDecimal> diffAmounts = new ArrayList<>();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 按日期顺序填充数据
        for (WeixinTransferReconciliation item : reconciliations) {
            dates.add(item.getDate().format(dateFormatter));

            // 微信金额（使用绝对值，因为是退款）
            BigDecimal weixinAmount = item.getAppRefundAmount() != null ? item.getAppRefundAmount().abs() : BigDecimal.ZERO;
            weixinAmounts.add(weixinAmount);

            // HIS金额（使用绝对值，因为是退款）
            BigDecimal hisAmount = item.getHisRefundAmount() != null ? item.getHisRefundAmount().abs() : BigDecimal.ZERO;
            hisAmounts.add(hisAmount);

            // 计算差额
            BigDecimal diffAmount = item.getDiffRefundAmount() != null ? item.getDiffRefundAmount() : BigDecimal.ZERO;
            diffAmounts.add(diffAmount);
        }

        // 设置图表数据
        chartData.setDates(dates);
        chartData.setWeixinAmounts(weixinAmounts);
        chartData.setHisAmounts(hisAmounts);
        chartData.setDiffAmounts(diffAmounts);

        return chartData;
    }
}