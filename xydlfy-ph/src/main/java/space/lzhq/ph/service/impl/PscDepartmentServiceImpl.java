package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscDepartment;
import space.lzhq.ph.mapper.PscDepartmentMapper;
import space.lzhq.ph.service.IPscDepartmentService;

import java.text.Collator;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Service
public class PscDepartmentServiceImpl extends ServiceImpl<PscDepartmentMapper, PscDepartment> implements IPscDepartmentService {

    @Override
    public PscDepartment getOneByName(String name) {
        return new LambdaQueryChainWrapper<>(getBaseMapper()).eq(PscDepartment::getName, name).one();
    }

    public void saveIfNotExist(String name) {
        synchronized (name.intern()) {
            PscDepartment department = getOneByName(name);
            if (department == null) {
                department = new PscDepartment();
                department.setName(name);
                save(department);
            }
        }
    }

    public List<String> getAllNamesOrderByPinyin() {
        return list().stream()
                .map(PscDepartment::getName)
                .distinct()
                .sorted((name1, name2) -> Collator.getInstance(Locale.SIMPLIFIED_CHINESE).compare(name1, name2))
                .collect(Collectors.toList());
    }

}
