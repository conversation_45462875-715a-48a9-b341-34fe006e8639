package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType;
import space.lzhq.ph.mapper.ChronicDiseaseRegistrationAttachmentMapper;
import space.lzhq.ph.service.AttachmentFileDeleter;
import space.lzhq.ph.service.ChronicDiseaseRegistrationAttachmentService;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
@Validated
public class ChronicDiseaseRegistrationAttachmentServiceImpl
        extends ServiceImpl<ChronicDiseaseRegistrationAttachmentMapper, ChronicDiseaseRegistrationAttachment>
        implements ChronicDiseaseRegistrationAttachmentService {

    @Autowired
    private AttachmentFileDeleter attachmentFileDeleter;

    @Override
    public Optional<ChronicDiseaseRegistrationAttachment> save(
            String openId,
            ChronicDiseaseRegistrationAttachmentType type,
            String fileName,
            String fileUrl
    ) {
        ChronicDiseaseRegistrationAttachment attachment = ChronicDiseaseRegistrationAttachment.builder()
                .openId(openId)
                .type(type)
                .fileName(fileName)
                .fileUrl(fileUrl)
                .createTime(LocalDateTime.now())
                .build();

        if (this.save(attachment)) {
            return Optional.of(attachment);
        } else {
            log.error("Failed to save ChronicDiseaseRegistrationAttachment for openId: {}", openId);
            return Optional.empty();
        }
    }

    @Override
    public boolean updateRegistrationId(String attachmentId, String registrationId) {
        if (StringUtils.isBlank(attachmentId) || StringUtils.isBlank(registrationId)) {
            log.warn("updateRegistrationId skipped – blank args attachmentId={}, registrationId={}",
                    attachmentId, registrationId);
            return false;
        }
        return this.lambdaUpdate()
                .set(ChronicDiseaseRegistrationAttachment::getRegistrationId, registrationId)
                .eq(ChronicDiseaseRegistrationAttachment::getId, attachmentId)
                .isNull(ChronicDiseaseRegistrationAttachment::getRegistrationId)
                .update();
    }

    @Override
    public boolean deleteAttachment(String id) {
        try {
            // 先查询附件记录
            ChronicDiseaseRegistrationAttachment attachment = this.getById(id);
            if (attachment == null) {
                log.warn("附件记录不存在，ID: {}", id);
                return false;
            }

            // 删除数据库记录
            boolean dbDeleteSuccess = this.removeById(id);
            if (!dbDeleteSuccess) {
                log.error("删除附件数据库记录失败，ID: {}", id);
                return false;
            }

            log.info("成功删除附件数据库记录，ID: {}", id);

            // 删除关联的文件
            deleteAssociatedFile(id, attachment);

            return true;

        } catch (Exception e) {
            log.error("删除附件时发生异常，ID: {}", id, e);
            return false;
        }
    }

    private void deleteAssociatedFile(String id, ChronicDiseaseRegistrationAttachment attachment) {
        try {
            String fileUrl = attachment.getFileUrl();
            if (fileUrl != null && !fileUrl.trim().isEmpty()) {
                boolean fileDeleteSuccess = attachmentFileDeleter.deleteFile(fileUrl);
                if (fileDeleteSuccess) {
                    log.info("成功删除附件文件，URL: {}", fileUrl);
                } else {
                    log.warn("删除附件文件失败，但数据库记录已删除，URL: {}", fileUrl);
                }
            } else {
                log.warn("附件记录中文件URL为空，跳过文件删除，ID: {}", id);
            }
        } catch (Exception e) {
            log.error("删除附件文件时发生异常，但数据库记录已删除，ID: {}, URL: {}",
                    id, attachment.getFileUrl(), e);
        }
    }

}
