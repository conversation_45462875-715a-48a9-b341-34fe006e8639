package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.ZhangyiAlipayReconciliationDetail;
import space.lzhq.ph.mapper.ZhangyiAlipayReconciliationDetailMapper;
import space.lzhq.ph.service.IZhangyiAlipayReconciliationDetailService;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ZhangyiAlipayReconciliationDetailServiceImpl
        extends ServiceImpl<ZhangyiAlipayReconciliationDetailMapper, ZhangyiAlipayReconciliationDetail>
        implements IZhangyiAlipayReconciliationDetailService {

    @Override
    public List<ZhangyiAlipayReconciliationDetail> listByBillDate(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaQuery().likeRight(ZhangyiAlipayReconciliationDetail::getId, billDateStr).list();
    }

    @Override
    public List<ZhangyiAlipayReconciliationDetail> listNotBalanced(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaQuery().likeRight(ZhangyiAlipayReconciliationDetail::getId, billDateStr)
                .ne(ZhangyiAlipayReconciliationDetail::getResult, ZhangyiAlipayReconciliationDetail.RESULT_BALANCED)
                .list();
    }

    @Override
    public boolean deleteByBillDate(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaUpdate().likeRight(ZhangyiAlipayReconciliationDetail::getId, billDateStr).remove();
    }
} 