package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.dromara.hutool.core.text.StrUtil;
import org.mospital.alipay.AlipayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.AlipayUserInfo;
import space.lzhq.ph.domain.Session;
import space.lzhq.ph.domain.UserInfo;
import space.lzhq.ph.mapper.AlipayUserInfoMapper;
import space.lzhq.ph.service.IAlipayUserInfoService;

import java.util.List;

@Service
public class AlipayUserInfoServiceImpl implements IAlipayUserInfoService {
    @Autowired
    private AlipayUserInfoMapper alipayUserInfoMapper;

    /**
     * 查询支付宝用户信息
     *
     * @param id 支付宝用户信息ID
     * @return 支付宝用户信息
     */
    @Override
    public AlipayUserInfo selectAlipayUserInfoById(String id) {
        return alipayUserInfoMapper.selectAlipayUserInfoById(id);
    }

    /**
     * 查询支付宝用户信息列表
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 支付宝用户信息
     */
    @Override
    public List<AlipayUserInfo> selectAlipayUserInfoList(AlipayUserInfo alipayUserInfo) {
        return alipayUserInfoMapper.selectAlipayUserInfoList(alipayUserInfo);
    }

    /**
     * 新增支付宝用户信息
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 结果
     */
    @Override
    public int insertAlipayUserInfo(AlipayUserInfo alipayUserInfo) {
        return alipayUserInfoMapper.insertAlipayUserInfo(alipayUserInfo);
    }

    /**
     * 修改支付宝用户信息
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 结果
     */
    @Override
    public int updateAlipayUserInfo(AlipayUserInfo alipayUserInfo) {
        return alipayUserInfoMapper.updateAlipayUserInfo(alipayUserInfo);
    }

    /**
     * 删除支付宝用户信息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteAlipayUserInfoByIds(String ids) {
        return alipayUserInfoMapper.deleteAlipayUserInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除支付宝用户信息信息
     *
     * @param id 支付宝用户信息ID
     * @return 结果
     */
    @Override
    public int deleteAlipayUserInfoById(String id) {
        return alipayUserInfoMapper.deleteAlipayUserInfoById(id);
    }

    @Override
    public UserInfo fillUserInfo(Session session, UserInfo userInfo) {
        if (!session.isAlipaySession()) {
            return userInfo;
        }

        if (!StrUtil.contains(userInfo.getIdCardNo() + userInfo.getName() + userInfo.getMobile(), "*")) {
            return userInfo;
        }

        AlipayUserInfo alipayUserInfo = alipayUserInfoMapper.selectAlipayUserInfoById(session.getOpenId());
        if (alipayUserInfo == null) {
            return userInfo;
        }

        if (StrUtil.equals(userInfo.getName(), AlipayService.INSTANCE.hideName(alipayUserInfo.getUserName()))) {
            userInfo.setName(alipayUserInfo.getUserName());
        }

        if (StrUtil.equals(userInfo.getMobile(), AlipayService.INSTANCE.hideMobile(alipayUserInfo.getMobile()))) {
            userInfo.setMobile(alipayUserInfo.getMobile());
        }

        if (StrUtil.equals(userInfo.getIdCardNo(), AlipayService.INSTANCE.hideIdCardNo(alipayUserInfo.getCertNo()))) {
            userInfo.setIdCardNo(alipayUserInfo.getCertNo());
        }

        return userInfo;
    }
}
