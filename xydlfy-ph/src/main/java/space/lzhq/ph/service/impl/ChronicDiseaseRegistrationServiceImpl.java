package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.ChronicDisease;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationAttachmentMapper;
import space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationMapper;
import space.lzhq.ph.dto.request.ChronicDiseaseAgentUpdateDto;
import space.lzhq.ph.dto.request.ChronicDiseaseDocumentAttachmentsUpdateDto;
import space.lzhq.ph.dto.request.ChronicDiseasePatientFormDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationDetailResponseDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationListResponseDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.InsuranceType;
import space.lzhq.ph.exception.*;
import space.lzhq.ph.service.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Validated
public class ChronicDiseaseRegistrationServiceImpl
        extends ServiceImpl<space.lzhq.ph.mapper.ChronicDiseaseRegistrationMapper, ChronicDiseaseRegistration>
        implements ChronicDiseaseRegistrationService {

    private final ChronicDiseaseRegistrationAttachmentService attachmentService;
    private final AttachmentFileChecker attachmentFileChecker;
    private final IChronicDiseaseService chronicDiseaseService;
    private final ChronicDiseaseRegistrationTimelineService timelineService;

    public ChronicDiseaseRegistrationServiceImpl(
            ChronicDiseaseRegistrationAttachmentService attachmentService,
            AttachmentFileChecker attachmentFileChecker,
            IChronicDiseaseService chronicDiseaseService,
            ChronicDiseaseRegistrationTimelineService timelineService) {
        this.attachmentService = attachmentService;
        this.attachmentFileChecker = attachmentFileChecker;
        this.chronicDiseaseService = chronicDiseaseService;
        this.timelineService = timelineService;
    }

    @Override
    public ChronicDiseaseRegistrationDetailResponseDto getDetailById(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 验证权限并获取申报记录
        ChronicDiseaseRegistration registration = validateAndGetRegistration(id, openId);

        // 2. 转换为详情DTO
        ChronicDiseaseRegistrationDetailResponseDto detailDto =
                ChronicDiseaseRegistrationMapper.INSTANCE.toDetailResponseDto(registration);

        // 3. 批量查询并设置附件信息
        populateAttachments(registration, detailDto);

        // 4. 填充病种信息
        if (registration.getDiseaseId() != null) {
            ChronicDisease disease = chronicDiseaseService.getById(registration.getDiseaseId());
            if (disease != null) {
                detailDto.setDisease(disease);
            }
        }

        return detailDto;
    }

    /**
     * 安全地记录时间线事件，不影响主业务逻辑
     *
     * @param action 记录时间线的具体操作
     */
    private void safeRecordTimeline(Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.warn("记录时间线事件失败，但不影响主业务逻辑: {}", e.getMessage());
        }
    }

    /**
     * 根据申报记录确定操作者姓名
     *
     * @param registration 申报记录
     * @return 操作者姓名（代办人或患者本人）
     */
    private String getOperatorName(ChronicDiseaseRegistration registration) {
        // 如果是代办模式且代办人姓名不为空，返回代办人姓名
        if (Objects.equals(registration.getAgentFlag(), 1) && StringUtils.isNotBlank(registration.getAgentName())) {
            return registration.getAgentName();
        }
        // 否则返回患者本人姓名
        return registration.getPatientName();
    }

    /**
     * 验证权限并获取申报记录
     *
     * @param id     申报记录ID
     * @param openId 用户OpenID
     * @return 申报记录，如果不存在或权限不符则返回null
     */
    private ChronicDiseaseRegistration validateAndGetRegistration(String id, String openId) {
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "ChronicDiseaseRegistration",
                    id
            );
        }

        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "ChronicDiseaseRegistration",
                    id,
                    openId
            );
        }
        return registration;
    }

    /**
     * 批量查询并填充附件信息到DTO
     *
     * @param registration 申报记录
     * @param detailDto    详情DTO
     */
    private void populateAttachments(ChronicDiseaseRegistration registration,
                                     ChronicDiseaseRegistrationDetailResponseDto detailDto) {
        // 收集所有附件ID
        List<String> allAttachmentIds = collectAllAttachmentIds(registration);

        if (allAttachmentIds.isEmpty()) {
            return;
        }

        // 批量查询附件并创建映射
        Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap =
                batchQueryAndMapAttachments(allAttachmentIds);

        // 设置各类附件到DTO
        setAttachmentsToDto(registration, detailDto, attachmentMap);
    }

    /**
     * 收集申报记录中的所有附件ID
     *
     * @param registration 申报记录
     * @return 附件ID列表
     */
    private List<String> collectAllAttachmentIds(ChronicDiseaseRegistration registration) {
        List<String> allAttachmentIds = new ArrayList<>();

        // 收集单个附件ID
        if (registration.getPatientIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getPatientIdCardAttachmentId());
        }
        if (registration.getPatientInsuranceCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getPatientInsuranceCardAttachmentId());
        }
        if (registration.getAgentIdCardAttachmentId() != null) {
            allAttachmentIds.add(registration.getAgentIdCardAttachmentId());
        }

        // 收集认定资料附件ID列表
        if (registration.getDocumentAttachmentIds() != null && !registration.getDocumentAttachmentIds().isEmpty()) {
            allAttachmentIds.addAll(registration.getDocumentAttachmentIds());
        }

        return allAttachmentIds.stream()
                .distinct()
                .toList();
    }

    /**
     * 批量查询附件并创建ID到附件对象的映射
     *
     * @param attachmentIds 附件ID列表
     * @return 附件ID到附件对象的映射
     */
    private Map<String, ChronicDiseaseRegistrationAttachment> batchQueryAndMapAttachments(List<String> attachmentIds) {
        List<ChronicDiseaseRegistrationAttachment> allAttachments = attachmentService.listByIds(attachmentIds);
        return allAttachments.stream()
                .collect(Collectors.toMap(ChronicDiseaseRegistrationAttachment::getId, Function.identity()));
    }

    /**
     * 将附件信息设置到详情DTO中
     *
     * @param registration  申报记录
     * @param detailDto     详情DTO
     * @param attachmentMap 附件映射
     */
    private void setAttachmentsToDto(ChronicDiseaseRegistration registration,
                                     ChronicDiseaseRegistrationDetailResponseDto detailDto,
                                     Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap) {
        // 设置患者身份证附件
        setPatientIdCardAttachment(registration, detailDto, attachmentMap);

        // 设置患者医保卡附件
        setPatientInsuranceCardAttachment(registration, detailDto, attachmentMap);

        // 设置代办人身份证附件
        setAgentIdCardAttachment(registration, detailDto, attachmentMap);

        // 设置认定资料附件列表
        setDocumentAttachments(registration, detailDto, attachmentMap);
    }

    /**
     * 设置患者身份证附件
     */
    private void setPatientIdCardAttachment(ChronicDiseaseRegistration registration,
                                            ChronicDiseaseRegistrationDetailResponseDto detailDto,
                                            Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap) {
        if (registration.getPatientIdCardAttachmentId() != null) {
            ChronicDiseaseRegistrationAttachment attachment =
                    attachmentMap.get(registration.getPatientIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setPatientIdCardAttachment(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    /**
     * 设置患者医保卡附件
     */
    private void setPatientInsuranceCardAttachment(ChronicDiseaseRegistration registration,
                                                   ChronicDiseaseRegistrationDetailResponseDto detailDto,
                                                   Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap) {
        if (registration.getPatientInsuranceCardAttachmentId() != null) {
            ChronicDiseaseRegistrationAttachment attachment =
                    attachmentMap.get(registration.getPatientInsuranceCardAttachmentId());
            if (attachment != null) {
                detailDto.setPatientInsuranceCardAttachment(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    /**
     * 设置代办人身份证附件
     */
    private void setAgentIdCardAttachment(ChronicDiseaseRegistration registration,
                                          ChronicDiseaseRegistrationDetailResponseDto detailDto,
                                          Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap) {
        if (registration.getAgentIdCardAttachmentId() != null) {
            ChronicDiseaseRegistrationAttachment attachment =
                    attachmentMap.get(registration.getAgentIdCardAttachmentId());
            if (attachment != null) {
                detailDto.setAgentIdCardAttachment(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment)
                );
            }
        }
    }

    /**
     * 设置认定资料附件列表
     */
    private void setDocumentAttachments(ChronicDiseaseRegistration registration,
                                        ChronicDiseaseRegistrationDetailResponseDto detailDto,
                                        Map<String, ChronicDiseaseRegistrationAttachment> attachmentMap) {
        if (registration.getDocumentAttachmentIds() != null && !registration.getDocumentAttachmentIds().isEmpty()) {
            List<ChronicDiseaseRegistrationAttachment> documentAttachments = registration.getDocumentAttachmentIds()
                    .stream()
                    .map(attachmentMap::get)
                    .filter(Objects::nonNull)
                    .toList();

            if (!documentAttachments.isEmpty()) {
                detailDto.setDocumentAttachments(
                        ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDtoList(documentAttachments)
                );
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<ChronicDiseaseRegistration> save(
            @NotNull @Valid ChronicDiseasePatientFormDto dto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) throws AttachmentValidationException {
        // 1. 验证身份证附件
        ChronicDiseaseRegistrationAttachment idCardAttachment = validateAttachment(
                dto.getPatientIdCardAttachmentId(),
                openId,
                ChronicDiseaseRegistrationAttachmentType.ID_CARD
        );

        // 2. 验证医保卡附件
        ChronicDiseaseRegistrationAttachment insuranceCardAttachment = validateAttachment(
                dto.getPatientInsuranceCardAttachmentId(),
                openId,
                ChronicDiseaseRegistrationAttachmentType.INSURANCE_CARD
        );

        // 3. 使用MapStruct映射DTO到实体
        ChronicDiseaseRegistration registration = ChronicDiseaseRegistrationMapper.INSTANCE.fromPatientFormDto(dto);

        // 4. 设置必要的字段
        registration.setOpenId(openId);
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT); // 默认草稿状态
        registration.setCreateTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 5. 保存申报主记录
        boolean saveSuccess = this.save(registration);
        if (!saveSuccess) {
            throw new DataUpdateFailedException("保存慢病申报记录失败", "保存申报记录");
        }

        // 6. 更新附件记录的申报主表ID
        updateAttachmentRegistrationId(idCardAttachment, registration.getId());
        updateAttachmentRegistrationId(insuranceCardAttachment, registration.getId());

        // 7. 记录创建事件到时间线
        safeRecordTimeline(() -> timelineService.recordRegistrationCreated(
                registration.getId(),
                getOperatorName(registration)
        ));

        return Optional.of(registration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChronicDiseaseRegistration clearAgentFlag(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "ChronicDiseaseRegistration",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "ChronicDiseaseRegistration",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "ChronicDiseaseRegistration",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 清除代办标志
        boolean updateSuccess = this.getBaseMapper().clearAgent(id) == 1;
        if (!updateSuccess) {
            throw new DataUpdateFailedException("清除代办标志失败，请重试", "清除代办标志", id);
        }

        // 5. 记录清除代办人事件到时间线
        safeRecordTimeline(() -> timelineService.recordAgentInfoCleared(
                id,
                getOperatorName(registration)
        ));

        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChronicDiseaseRegistration updateAgent(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid ChronicDiseaseAgentUpdateDto agentDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "ChronicDiseaseRegistration",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "ChronicDiseaseRegistration",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "ChronicDiseaseRegistration",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证代办人身份证附件
        ChronicDiseaseRegistrationAttachment agentIdCardAttachment = validateAttachment(
                agentDto.getAgentIdCardAttachmentId(),
                openId,
                ChronicDiseaseRegistrationAttachmentType.ID_CARD,
                id  // 允许附件已经关联到当前申报记录
        );

        // 5. 更新代办人信息
        registration.setAgentFlag(1);
        registration.setAgentName(agentDto.getAgentName());
        registration.setAgentIdCardNo(agentDto.getAgentIdCardNo());
        registration.setAgentIdCardAttachmentId(agentDto.getAgentIdCardAttachmentId());
        registration.setAgentMobile(agentDto.getAgentMobile());
        registration.setAgentRelation(agentDto.getAgentRelation());
        registration.setUpdateTime(LocalDateTime.now());

        // 6. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新代办人信息失败，请重试", "更新代办人信息", id);
        }

        // 7. 更新附件记录的申报主表ID
        updateAttachmentRegistrationId(agentIdCardAttachment, id);

        // 8. 记录设置代办人事件到时间线
        safeRecordTimeline(() -> timelineService.recordAgentInfoSet(
                id,
                getOperatorName(registration),
                agentDto.getAgentName()
        ));

        return registration;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChronicDisease> fetchAvailableDiseases(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException("申报记录不存在", "ChronicDiseaseRegistration", id);
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException("无权访问该申报记录", "ChronicDiseaseRegistration", id, openId);
        }

        // 3. 从申报记录中提取医保类型
        InsuranceType insuranceType = registration.getInsuranceType();
        if (insuranceType == null) {
            throw new IllegalStateException("申报记录医保类型为空，申报ID: " + id);
        }

        // 4. 根据医保类型查询支持的慢性病病种
        return chronicDiseaseService.findByInsuranceType(insuranceType);
    }

    /**
     * 验证附件是否满足业务规则
     *
     * @param attachmentId 附件ID
     * @param openId       用户OpenID
     * @param expectedType 期望的附件类型
     * @return 附件记录
     * @throws AttachmentValidationException 当附件不满足业务规则时抛出
     */
    private ChronicDiseaseRegistrationAttachment validateAttachment(
            String attachmentId,
            String openId,
            ChronicDiseaseRegistrationAttachmentType expectedType
    ) {
        return validateAttachment(attachmentId, openId, expectedType, null);
    }

    /**
     * 验证附件是否满足业务规则
     *
     * @param attachmentId   附件ID
     * @param openId         用户OpenID
     * @param expectedType   期望的附件类型
     * @param registrationId 允许附件已经关联到的申报记录ID
     * @return 附件记录
     * @throws AttachmentValidationException 当附件不满足业务规则时抛出
     */
    private ChronicDiseaseRegistrationAttachment validateAttachment(
            String attachmentId,
            String openId,
            ChronicDiseaseRegistrationAttachmentType expectedType,
            String registrationId
    ) {
        String typeName = expectedType.getDescription();

        // 1. 检查附件是否存在
        ChronicDiseaseRegistrationAttachment attachment = attachmentService.getById(attachmentId);
        if (attachment == null) {
            throw new AttachmentValidationException(
                    typeName + "附件不存在，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 2. 检查附件是否属于当前用户
        if (!openId.equals(attachment.getOpenId())) {
            throw new AttachmentValidationException(
                    typeName + "附件不属于当前用户，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 3. 检查附件类型是否正确
        if (!expectedType.equals(attachment.getType())) {
            throw new AttachmentValidationException(
                    typeName + "附件类型不正确，期望: " + expectedType + "，实际: " + attachment.getType(),
                    attachmentId,
                    typeName
            );
        }

        // 4. 检查附件是否已被使用
        if (attachment.getRegistrationId() != null && !attachment.getRegistrationId().equals(registrationId)) {
            throw new AttachmentValidationException(
                    typeName + "附件已被使用，附件ID: " + attachmentId,
                    attachmentId,
                    typeName
            );
        }

        // 5. 检查文件是否真实存在
        if (!attachmentFileChecker.isFileExists(attachment.getFileUrl())) {
            throw new AttachmentValidationException(
                    typeName + "附件对应的文件不存在，文件URL: " + attachment.getFileUrl(),
                    attachmentId,
                    typeName
            );
        }

        return attachment;
    }

    /**
     * 更新附件记录中的申报主表ID
     *
     * @param attachment     附件记录
     * @param registrationId 申报主表ID
     */
    private void updateAttachmentRegistrationId(ChronicDiseaseRegistrationAttachment attachment, String registrationId) {
        if (Objects.equals(registrationId, attachment.getRegistrationId())) {
            return;
        }

        boolean updateSuccess = attachmentService.updateRegistrationId(attachment.getId(), registrationId);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新附件关联的申报ID失败", "更新附件关联", attachment.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChronicDiseaseRegistration updateDisease(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Positive Integer diseaseId,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "ChronicDiseaseRegistration",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "ChronicDiseaseRegistration",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "ChronicDiseaseRegistration",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证病种ID是否有效
        ChronicDisease disease = chronicDiseaseService.getById(diseaseId);
        if (disease == null) {
            throw new ResourceNotFoundException(
                    "病种不存在，病种ID: " + diseaseId,
                    "ChronicDisease",
                    diseaseId.toString()
            );
        }

        // 5. 验证病种是否支持当前医保类型
        InsuranceType insuranceType = registration.getInsuranceType();
        if (insuranceType == null) {
            throw new IllegalStateException("申报记录医保类型为空，申报ID: " + id);
        }

        boolean supportedByInsurance = chronicDiseaseService.isDiseaseSupported(disease, insuranceType);
        if (!supportedByInsurance) {
            throw new InvalidDiseaseException(
                    "病种不支持当前医保类型（" + insuranceType.getDescription() + "），病种ID: " + diseaseId,
                    diseaseId
            );
        }

        // 6. 更新申报记录的病种ID
        registration.setDiseaseId(diseaseId);
        registration.setUpdateTime(LocalDateTime.now());

        // 7. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新慢病病种失败，请重试", "更新慢病病种", id);
        }

        // 8. 记录病种选择事件到时间线
        safeRecordTimeline(() -> timelineService.recordDiseaseSelected(
                id,
                getOperatorName(registration),
                disease.getName()
        ));

        return registration;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChronicDiseaseRegistration updateDocumentAttachments(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid ChronicDiseaseDocumentAttachmentsUpdateDto attachmentsDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "ChronicDiseaseRegistration",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "ChronicDiseaseRegistration",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许用户编辑
        if (!registration.canBeModifiedByUser()) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
                    "ChronicDiseaseRegistration",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证所有认定资料附件
        List<ChronicDiseaseRegistrationAttachment> documentAttachments = new ArrayList<>();
        for (String attachmentId : attachmentsDto.getDocumentAttachmentIds()) {
            ChronicDiseaseRegistrationAttachment attachment = validateAttachment(
                    attachmentId,
                    openId,
                    ChronicDiseaseRegistrationAttachmentType.DOCUMENT,
                    id  // 允许附件已经关联到当前申报记录
            );
            documentAttachments.add(attachment);
        }

        // 5. 更新申报记录的认定资料附件ID列表
        registration.setDocumentAttachmentIds(attachmentsDto.getDocumentAttachmentIds());
        registration.setUpdateTime(LocalDateTime.now());

        // 6. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("更新认定资料附件失败，请重试", "更新认定资料附件", id);
        }

        // 7. 更新所有附件记录的申报主表ID
        for (ChronicDiseaseRegistrationAttachment attachment : documentAttachments) {
            updateAttachmentRegistrationId(attachment, id);
        }

        // 8. 记录认定资料上传事件到时间线
        safeRecordTimeline(() -> timelineService.recordDocumentsUploaded(
                id,
                getOperatorName(registration),
                attachmentsDto.getDocumentAttachmentIds().size()
        ));

        return registration;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChronicDiseaseRegistration submitRegistration(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    ) {
        // 1. 根据ID查询申报记录
        ChronicDiseaseRegistration registration = this.getById(id);
        if (registration == null) {
            throw new ResourceNotFoundException(
                    "申报记录不存在",
                    "ChronicDiseaseRegistration",
                    id
            );
        }

        // 2. 验证申报记录的openId是否与当前用户的openId一致
        if (!openId.equals(registration.getOpenId())) {
            throw new UnauthorizedResourceAccessException(
                    "申报记录不属于当前用户",
                    "ChronicDiseaseRegistration",
                    id,
                    openId
            );
        }

        // 3. 验证申报记录状态是否允许提交（仅DRAFT和REJECTED状态可以提交）
        if (registration.getStatus() != ChronicDiseaseRegistrationStatus.DRAFT
                && registration.getStatus() != ChronicDiseaseRegistrationStatus.REJECTED) {
            throw new EditNotAllowedException(
                    "申报记录当前状态不允许提交：" + registration.getStatus().getDescription(),
                    "ChronicDiseaseRegistration",
                    id,
                    registration.getStatus().getDescription()
            );
        }

        // 4. 验证申报信息完整性
        validateRegistrationCompleteness(registration);

        // 5. 记录状态变更前的状态
        ChronicDiseaseRegistrationStatus oldStatus = registration.getStatus();

        // 6. 更新申报状态为SUBMITTED，设置提交时间
        registration.setStatus(ChronicDiseaseRegistrationStatus.SUBMITTED);
        registration.setSubmitTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 7. 保存更新后的记录
        boolean updateSuccess = this.updateById(registration);
        if (!updateSuccess) {
            throw new DataUpdateFailedException("提交慢病申报失败，请重试", "提交申报记录", id);
        }

        // 8. 记录提交申报事件到时间线
        safeRecordTimeline(() -> timelineService.recordRegistrationSubmitted(
                id,
                getOperatorName(registration),
                oldStatus
        ));

        return registration;
    }

    /**
     * 验证慢病申报记录的完整性
     *
     * @param registration 申报记录
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateRegistrationCompleteness(ChronicDiseaseRegistration registration) {
        String id = registration.getId();

        // 验证申报人信息是否完整
        validatePatientInfo(registration, id);

        // 验证代办人信息是否完整（如果是代办申请）
        validateAgentInfo(registration, id);

        // 验证病种和认定资料
        validateDiseaseAndDocuments(registration, id);
    }

    /**
     * 验证申报人信息完整性
     *
     * @param registration 申报记录
     * @param id           申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validatePatientInfo(ChronicDiseaseRegistration registration, String id) {
        validateRequiredStringField(registration.getPatientIdCardAttachmentId(), "申报人身份证附件未上传", "patientIdCardAttachmentId", id);
        validateRequiredStringField(registration.getPatientIdCardNo(), "申报人身份证号未填写", "patientIdCardNo", id);
        validateRequiredStringField(registration.getPatientName(), "申报人姓名未填写", "patientName", id);
        validateRequiredStringField(registration.getPatientInsuranceCardAttachmentId(), "申报人医保卡附件未上传", "patientInsuranceCardAttachmentId", id);
        validateRequiredStringField(registration.getPatientInsuranceCardNo(), "申报人医保卡号未填写", "patientInsuranceCardNo", id);
        validateRequiredStringField(registration.getPatientMobile(), "申报人手机号未填写", "patientMobile", id);

        if (registration.getInsuranceType() == null) {
            throw new SubmissionValidationException("医保类型未选择", "insuranceType", id);
        }
    }

    /**
     * 验证代办人信息完整性（如果是代办申请）
     *
     * @param registration 申报记录
     * @param id           申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateAgentInfo(ChronicDiseaseRegistration registration, String id) {
        if (registration.getAgentFlag() != null && registration.getAgentFlag() == 1) {
            validateRequiredStringField(registration.getAgentName(), "代办人姓名未填写", "agentName", id);
            validateRequiredStringField(registration.getAgentIdCardNo(), "代办人身份证号未填写", "agentIdCardNo", id);
            validateRequiredStringField(registration.getAgentIdCardAttachmentId(), "代办人身份证附件未上传", "agentIdCardAttachmentId", id);
            validateRequiredStringField(registration.getAgentMobile(), "代办人手机号未填写", "agentMobile", id);

            if (registration.getAgentRelation() == null) {
                throw new SubmissionValidationException("代办人与患者关系未选择", "agentRelation", id);
            }
        }
    }

    /**
     * 验证病种和认定资料完整性
     *
     * @param registration 申报记录
     * @param id           申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateDiseaseAndDocuments(ChronicDiseaseRegistration registration, String id) {
        if (registration.getDiseaseId() == null) {
            throw new SubmissionValidationException("慢病病种未选择", "diseaseId", id);
        }

        if (registration.getDocumentAttachmentIds() == null || registration.getDocumentAttachmentIds().isEmpty()) {
            throw new SubmissionValidationException("慢病认定资料未上传", "documentAttachmentIds", id);
        }
    }

    /**
     * 验证必填字符串字段
     *
     * @param value          字段值
     * @param errorMessage   错误消息
     * @param fieldName      字段名
     * @param registrationId 申报记录ID
     * @throws SubmissionValidationException 当验证失败时抛出异常
     */
    private void validateRequiredStringField(String value, String errorMessage, String fieldName, String registrationId) {
        if (value == null || value.trim().isEmpty()) {
            throw new SubmissionValidationException(errorMessage, fieldName, registrationId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChronicDiseaseRegistrationListResponseDto> getUserRegistrationList(
            @NotBlank @Size(min = 16, max = 28) String openId,
            ChronicDiseaseRegistrationStatus status
    ) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ChronicDiseaseRegistration> queryWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        queryWrapper.eq(ChronicDiseaseRegistration::getOpenId, openId);

        if (status != null) {
            queryWrapper.eq(ChronicDiseaseRegistration::getStatus, status);
        }

        queryWrapper.orderByDesc(ChronicDiseaseRegistration::getCreateTime);

        List<ChronicDiseaseRegistration> registrations = this.list(queryWrapper);

        return registrations.stream()
                .map(ChronicDiseaseRegistrationMapper.INSTANCE::toListResponseDto)
                .toList();
    }
}