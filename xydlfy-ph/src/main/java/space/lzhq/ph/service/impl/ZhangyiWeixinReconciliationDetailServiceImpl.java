package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.ZhangyiWeixinReconciliationDetail;
import space.lzhq.ph.mapper.ZhangyiWeixinReconciliationDetailMapper;
import space.lzhq.ph.service.IZhangyiWeixinReconciliationDetailService;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ZhangyiWeixinReconciliationDetailServiceImpl
        extends ServiceImpl<ZhangyiWeixinReconciliationDetailMapper, ZhangyiWeixinReconciliationDetail>
        implements IZhangyiWeixinReconciliationDetailService {

    @Override
    public List<ZhangyiWeixinReconciliationDetail> listByBillDate(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaQuery().likeRight(ZhangyiWeixinReconciliationDetail::getId, billDateStr).list();
    }

    @Override
    public List<ZhangyiWeixinReconciliationDetail> listNotBalanced(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaQuery().likeRight(ZhangyiWeixinReconciliationDetail::getId, billDateStr)
                .ne(ZhangyiWeixinReconciliationDetail::getResult, ZhangyiWeixinReconciliationDetail.RESULT_BALANCED)
                .list();
    }

    @Override
    public boolean deleteByBillDate(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaUpdate().likeRight(ZhangyiWeixinReconciliationDetail::getId, billDateStr).remove();
    }

}
