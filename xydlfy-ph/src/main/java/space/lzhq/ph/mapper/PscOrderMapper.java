package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.PscCarer;
import space.lzhq.ph.domain.PscOrder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
public interface PscOrderMapper {
    /**
     * 查询订单
     *
     * @param id 订单ID
     * @return 订单
     */
    PscOrder selectPscOrderById(Long id);

    /**
     * 查询订单列表
     *
     * @param pscOrder 订单
     * @return 订单集合
     */
    List<PscOrder> selectPscOrderList(PscOrder pscOrder);

    List<PscOrder> selectPscOrderListByConfirmTimeRangeAndStatus(
            @Param("minConfirmTime") LocalDateTime minConfirmTime,
            @Param("maxConfirmTime") LocalDateTime maxConfirmTime,
            @Param("status") int status
    );

    /**
     * 查询未完成订单
     */
    List<PscOrder> selectUnfinishedOrderList(@Param("admissionNumber") String admissionNumber);

    List<PscOrder> selectPscOrderListByCarer(
            @Param("carerEmployeeNo") String carerEmployeeNo,
            @Param("patientDepartment") String patientDepartment,
            @Param("examItemCode") String examItemCode,
            @Param("beginCreateTime") LocalDateTime beginCreateTime,
            @Param("endCreateTime") LocalDateTime endCreateTime
    );

    /**
     * 新增订单
     *
     * @param pscOrder 订单
     * @return 结果
     */
    int insertPscOrder(PscOrder pscOrder);

    /**
     * 修改订单
     *
     * @param pscOrder 订单
     * @return 结果
     */
    int updatePscOrder(PscOrder pscOrder);

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 结果
     */
    int deletePscOrderById(Long id);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePscOrderByIds(String[] ids);

    void assignCarer(@Param("ids") String[] ids, @Param("carer") PscCarer carer);

    void takeBatch(@Param("ids") String[] ids, @Param("carer") PscCarer carer);

    void release(@Param("ids") String[] ids, @Param("carerEmployeeNo") String carerEmployeeNo);
}
