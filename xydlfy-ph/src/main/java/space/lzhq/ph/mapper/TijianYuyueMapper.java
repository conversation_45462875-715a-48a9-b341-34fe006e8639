package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.TijianYuyue;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 体检预约记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Repository
public interface TijianYuyueMapper {
    /**
     * 查询体检预约记录
     *
     * @param id 体检预约记录ID
     * @return 体检预约记录
     */
    public TijianYuyue selectTijianYuyueById(Long id);

    /**
     * 查询体检预约记录列表
     *
     * @param tijianYuyue 体检预约记录
     * @return 体检预约记录集合
     */
    public List<TijianYuyue> selectTijianYuyueList(TijianYuyue tijianYuyue);

    /**
     * 根据身份证号查询最近一次体检预约记录
     *
     * @param idCardNo
     * @return
     */
    public TijianYuyue selectLatestOneByIdCardNo(String idCardNo);

    /**
     * 新增体检预约记录
     *
     * @param tijianYuyue 体检预约记录
     * @return 结果
     */
    public int insertTijianYuyue(TijianYuyue tijianYuyue);

    /**
     * 修改体检预约记录
     *
     * @param tijianYuyue 体检预约记录
     * @return 结果
     */
    public int updateTijianYuyue(TijianYuyue tijianYuyue);

    /**
     * 删除体检预约记录
     *
     * @param id 体检预约记录ID
     * @return 结果
     */
    public int deleteTijianYuyueById(Long id);

    /**
     * 批量删除体检预约记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTijianYuyueByIds(String[] ids);

    /**
     * 根据日期查询有效预约记录数
     *
     * @param date 日期
     * @return 有效预约记录数
     */
    public int countAvailableByDate(LocalDate date);

    /**
     * 查询指定日期范围内的预约人次
     *
     * @param minDate 最小日期
     * @param maxDate 最大日期
     * @return 指定日期范围内的预约人次
     */
    List<Map<String, Object>> countAvailableByDateRange(
            @Param("minDate") LocalDate minDate,
            @Param("maxDate") LocalDate maxDate
    );
}
