package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.WikiCategory;

import java.util.List;

/**
 * 知识分类Mapper接口
 */
@Repository
public interface WikiCategoryMapper {
    /**
     * 查询知识分类
     *
     * @param id 知识分类主键
     * @return 知识分类
     */
    WikiCategory selectWikiCategoryById(Long id);

    /**
     * 查询知识分类列表
     *
     * @param wikiCategory 知识分类
     * @return 知识分类集合
     */
    List<WikiCategory> selectWikiCategoryList(WikiCategory wikiCategory);

    List<WikiCategory> selectAll();

    /**
     * 新增知识分类
     *
     * @param wikiCategory 知识分类
     * @return 结果
     */
    int insertWikiCategory(WikiCategory wikiCategory);

    /**
     * 修改知识分类
     *
     * @param wikiCategory 知识分类
     * @return 结果
     */
    int updateWikiCategory(WikiCategory wikiCategory);

    /**
     * 删除知识分类
     *
     * @param id 知识分类主键
     * @return 结果
     */
    int deleteWikiCategoryById(Long id);

    /**
     * 批量删除知识分类
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteWikiCategoryByIds(String[] ids);
}
