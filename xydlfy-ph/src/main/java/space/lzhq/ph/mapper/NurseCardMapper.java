package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.NurseCard;

import java.util.List;

/**
 * 电子陪护证Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-11
 */
@Repository
public interface NurseCardMapper {
    /**
     * 查询电子陪护证
     *
     * @param id 电子陪护证ID
     * @return 电子陪护证
     */
    NurseCard selectNurseCardById(Long id);

    /**
     * 查询电子陪护证列表
     *
     * @param nurseCard 电子陪护证
     * @return 电子陪护证集合
     */
    List<NurseCard> selectNurseCardList(NurseCard nurseCard);

    /**
     * 新增电子陪护证
     *
     * @param nurseCard 电子陪护证
     * @return 结果
     */
    int insertNurseCard(NurseCard nurseCard);

    /**
     * 修改电子陪护证
     *
     * @param nurseCard 电子陪护证
     * @return 结果
     */
    int updateNurseCard(NurseCard nurseCard);

    /**
     * 删除电子陪护证
     *
     * @param id 电子陪护证ID
     * @return 结果
     */
    int deleteNurseCardById(Long id);

    /**
     * 批量删除电子陪护证
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteNurseCardByIds(String[] ids);
}
