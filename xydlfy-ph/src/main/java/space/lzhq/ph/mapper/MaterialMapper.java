package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.Material;

import java.util.List;

/**
 * 材料Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Repository
public interface MaterialMapper {
    /**
     * 查询材料
     *
     * @param id 材料ID
     * @return 材料
     */
    Material selectMaterialById(String id);

    /**
     * 查询材料列表
     *
     * @param material 材料
     * @return 材料集合
     */
    List<Material> selectMaterialList(Material material);

    /**
     * 新增材料
     *
     * @param material 材料
     * @return 结果
     */
    int insertMaterial(Material material);

    /**
     * 批量新增材料
     *
     * @param materials 材料
     * @return 结果
     */
    int insertMaterials(@Param("materials") List<Material> materials);

    /**
     * 修改材料
     *
     * @param material 材料
     * @return 结果
     */
    int updateMaterial(Material material);

    /**
     * 删除材料
     *
     * @param id 材料ID
     * @return 结果
     */
    int deleteMaterialById(String id);

    /**
     * 批量删除材料
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteMaterialByIds(String[] ids);

    /**
     * 删除所有材料
     */
    void clearAll();
}
