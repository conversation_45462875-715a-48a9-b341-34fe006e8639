package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.WxCheck;

import java.util.List;

/**
 * 对账Mapper接口
 *
 * <AUTHOR>
 * @date 2020-06-09
 */
@Repository
public interface WxCheckMapper {
    /**
     * 查询对账
     *
     * @param id 对账ID
     * @return 对账
     */
    WxCheck selectWxCheckById(Long id);

    /**
     * 查询对账列表
     *
     * @param wxCheck 对账
     * @return 对账集合
     */
    List<WxCheck> selectWxCheckList(WxCheck wxCheck);

    /**
     * 新增对账
     *
     * @param wxCheck 对账
     * @return 结果
     */
    int insertWxCheck(WxCheck wxCheck);

    /**
     * 修改对账
     *
     * @param wxCheck 对账
     * @return 结果
     */
    int updateWxCheck(WxCheck wxCheck);

    /**
     * 删除对账
     *
     * @param id 对账ID
     * @return 结果
     */
    int deleteWxCheckById(Long id);

    /**
     * 批量删除对账
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxCheckByIds(String[] ids);
}
