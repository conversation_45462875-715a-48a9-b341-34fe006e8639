package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.TijianPackage;

import java.util.List;

/**
 * 体检套餐Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Repository
public interface TijianPackageMapper {
    /**
     * 查询体检套餐
     *
     * @param id 体检套餐ID
     * @return 体检套餐
     */
    public TijianPackage selectTijianPackageById(String id);

    /**
     * 查询体检套餐列表
     *
     * @param tijianPackage 体检套餐
     * @return 体检套餐集合
     */
    public List<TijianPackage> selectTijianPackageList(TijianPackage tijianPackage);

    /**
     * 新增体检套餐
     *
     * @param tijianPackage 体检套餐
     * @return 结果
     */
    public int insertTijianPackage(TijianPackage tijianPackage);

    /**
     * 修改体检套餐
     *
     * @param tijianPackage 体检套餐
     * @return 结果
     */
    public int updateTijianPackage(TijianPackage tijianPackage);

    /**
     * 删除体检套餐
     *
     * @param id 体检套餐ID
     * @return 结果
     */
    public int deleteTijianPackageById(String id);

    /**
     * 批量删除体检套餐
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTijianPackageByIds(String[] ids);

    /**
     * 删除所有体检套餐
     */
    public void clear();
}
