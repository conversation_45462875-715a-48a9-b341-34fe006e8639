package space.lzhq.ph.utils;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * XML工具类
 */
public class XmlUtils {
    private static final Logger log = LoggerFactory.getLogger(XmlUtils.class);

    /**
     * 生成响应XML
     *
     * @param success 是否成功
     * @param detail  详细信息
     * @return XML字符串
     */
    public static String generateResponseXml(boolean success, String detail) {
        try {
            Document document = DocumentHelper.createDocument();
            Element root = document.addElement("BSXml");

            Element header = root.addElement("MsgHeader");
            header.addElement("Sender").setText("YQTL");
            header.addElement("MsgType").setText("OutAppointSend");
            header.addElement("MsgVersion").setText("3.1");
            header.addElement("Status").setText(String.valueOf(success));
            header.addElement("Detail").setText(detail);

            return document.asXML();
        } catch (Exception e) {
            log.error("生成响应XML时发生错误", e);
            return String.format("<BSXml><MsgHeader><Status>false</Status><Detail>生成响应失败: %s</Detail></MsgHeader></BSXml>",
                    e.getMessage());
        }
    }

    /**
     * 解析请求XML
     *
     * @param xml XML字符串
     * @return Visit节点的Element对象
     */
    public static Element parseRequestXml(String xml) {
        try {
            Document document = DocumentHelper.parseText(xml);
            Element root = document.getRootElement();
            return root.element("Visit");
        } catch (Exception e) {
            log.error("解析请求XML时发生错误", e);
            throw new RuntimeException("XML解析失败: " + e.getMessage());
        }
    }
} 