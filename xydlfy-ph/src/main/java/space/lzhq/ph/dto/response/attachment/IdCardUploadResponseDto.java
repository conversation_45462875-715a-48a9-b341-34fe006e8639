package space.lzhq.ph.dto.response.attachment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationAttachmentMapper;
import space.lzhq.ph.dto.mapper.IdCardOcrResultMapper;

/**
 * 身份证上传响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "身份证上传响应数据")
public class IdCardUploadResponseDto {

    @Schema(description = "附件信息")
    private ChronicDiseaseRegistrationAttachmentDto attachment;

    @Schema(description = "OCR识别结果")
    private IdCardOcrResultDto ocrResult;

    /**
     * 从域实体创建响应 DTO
     *
     * @param attachment 慢性病登记附件域实体，不能为null
     * @param ocrResult  OCR识别结果，不能为null
     * @return 身份证上传响应DTO
     * @throws IllegalArgumentException 当参数为null时
     */
    public static IdCardUploadResponseDto fromDomain(ChronicDiseaseRegistrationAttachment attachment, WxOcrIdCardResult ocrResult) {
        if (attachment == null) {
            throw new IllegalArgumentException("Domain entity cannot be null");
        }
        if (ocrResult == null) {
            throw new IllegalArgumentException("OCR result cannot be null");
        }
        return new IdCardUploadResponseDto(
                ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(attachment),
                IdCardOcrResultMapper.INSTANCE.toDto(ocrResult)
        );
    }
}