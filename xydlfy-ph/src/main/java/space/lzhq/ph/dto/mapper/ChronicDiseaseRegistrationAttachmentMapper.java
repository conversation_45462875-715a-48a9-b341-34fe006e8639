package space.lzhq.ph.dto.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.dto.response.attachment.ChronicDiseaseRegistrationAttachmentDto;

import java.util.List;

@Mapper
public interface ChronicDiseaseRegistrationAttachmentMapper {

    ChronicDiseaseRegistrationAttachmentMapper INSTANCE = Mappers.getMapper(ChronicDiseaseRegistrationAttachmentMapper.class);

    /**
     * 将附件实体转换为附件DTO
     *
     * @param attachment 附件实体
     * @return 附件DTO
     */
    ChronicDiseaseRegistrationAttachmentDto toDto(ChronicDiseaseRegistrationAttachment attachment);

    /**
     * 将附件实体列表转换为附件DTO列表
     *
     * @param attachments 附件实体列表
     * @return 附件DTO列表
     */
    List<ChronicDiseaseRegistrationAttachmentDto> toDtoList(List<ChronicDiseaseRegistrationAttachment> attachments);

}