package space.lzhq.ph.dto.response.attachment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.dto.mapper.ChronicDiseaseRegistrationAttachmentMapper;

/**
 * 认定资料上传响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "认定资料上传响应数据")
public class DocumentUploadResponseDto {

    @Schema(description = "附件信息")
    private ChronicDiseaseRegistrationAttachmentDto attachment;

    /**
     * 从域实体创建响应 DTO
     */
    public static DocumentUploadResponseDto fromDomain(ChronicDiseaseRegistrationAttachment domain) {
        if (domain == null) {
            throw new IllegalArgumentException("Domain entity cannot be null");
        }

        ChronicDiseaseRegistrationAttachmentDto dto = ChronicDiseaseRegistrationAttachmentMapper.INSTANCE.toDto(domain);
        return new DocumentUploadResponseDto(dto);
    }

}