package space.lzhq.ph.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 慢病申报认定资料附件更新请求DTO
 */
@Data
@Schema(description = "慢病申报认定资料附件更新请求DTO")
public class ChronicDiseaseDocumentAttachmentsUpdateDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 认定资料附件ID列表
     */
    @Schema(description = "认定资料附件ID列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"abc123-def456-ghi789\", \"jkl012-mno345-pqr678\"]")
    @NotEmpty(message = "认定资料附件ID列表不能为空")
    @Size(max = 20, message = "认定资料不能超过20个")
    private List<@NotNull @Size(min = 32, max = 32, message = "附件ID长度必须为32位") String> documentAttachmentIds;

}