package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 慢病专家列表响应DTO
 */
@Data
@NoArgsConstructor
public class ChronicDiseaseExpertListResponseDto {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 专家姓名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 用户状态描述
     */
    private String statusName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginDate;
} 