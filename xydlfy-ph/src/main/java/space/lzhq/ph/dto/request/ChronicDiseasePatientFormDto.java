package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.xss.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.data.PhoneUtil;
import space.lzhq.ph.enums.InsuranceType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 慢病申报申报人认证表单请求DTO
 */
@Data
@Schema(description = "慢病申报申报人认证表单请求DTO")
public class ChronicDiseasePatientFormDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 申报患者身份证附件ID
     */
    @Schema(description = "申报患者身份证附件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123e4567-e89b-12d3-a456-************")
    @NotBlank(message = "申报患者身份证附件ID不能为空")
    private String patientIdCardAttachmentId;

    /**
     * 申报患者身份证号
     */
    @Schema(description = "申报患者身份证号", requiredMode = Schema.RequiredMode.REQUIRED, example = "110101199001011234")
    @NotBlank(message = "申报患者身份证号不能为空")
    private String patientIdCardNo;

    /**
     * 申报患者姓名
     */
    @Schema(description = "申报患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "申报患者姓名不能为空")
    @Xss(message = "申报患者姓名不能包含特殊字符")
    private String patientName;

    /**
     * 申报患者医保卡附件ID
     */
    @Schema(description = "申报患者医保卡附件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "456e7890-e89b-12d3-a456-426614174001")
    @NotBlank(message = "申报患者医保卡附件ID不能为空")
    private String patientInsuranceCardAttachmentId;

    /**
     * 申报患者医保卡号
     */
    @Schema(description = "申报患者医保卡号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1101011990010112345")
    @NotBlank(message = "申报患者医保卡号不能为空")
    private String patientInsuranceCardNo;

    /**
     * 申报患者医保类型
     */
    @Schema(description = "申报患者医保类型，可选值：CITY_WORKER(乌鲁木齐市职工医保), CITY_RESIDENT(乌鲁木齐市居民医保), DISTRICT(区医保（含铁路医保）), CORP(兵团医保（兵直、十一师、十二师）)", requiredMode = Schema.RequiredMode.REQUIRED, example = "CITY_WORKER")
    @NotNull(message = "申报患者医保类型不能为空")
    @JsonSerialize(using = space.lzhq.ph.serializer.EnumNameSerializer.class)
    private InsuranceType insuranceType;

    /**
     * 申报患者手机号
     */
    @Schema(description = "申报患者手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotBlank(message = "申报患者手机号不能为空")
    private String patientMobile;

    /**
     * 验证患者身份证号格式
     * 注意：只有在身份证号不为空时才验证格式，空值验证由 @NotBlank 处理
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "申报患者身份证号格式不正确")
    public boolean isPatientIdCardValid() {
        // 如果身份证号为空，跳过格式验证（由 @NotBlank 处理空值）
        if (StringUtils.isBlank(patientIdCardNo)) {
            return true;
        }
        return IdcardUtil.isValidCard(patientIdCardNo);
    }

    /**
     * 验证患者手机号格式
     * 注意：只有在手机号不为空时才验证格式，空值验证由 @NotBlank 处理
     *
     * @return 验证结果
     */
    @JsonIgnore
    @AssertTrue(message = "申报患者手机号格式不正确")
    public boolean isPatientMobileValid() {
        // 如果手机号为空，跳过格式验证（由 @NotBlank 处理空值）
        if (StringUtils.isBlank(patientMobile)) {
            return true;
        }
        return PhoneUtil.isMobile(patientMobile);
    }
}