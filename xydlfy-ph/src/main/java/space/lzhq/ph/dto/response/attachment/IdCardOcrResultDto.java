package space.lzhq.ph.dto.response.attachment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 身份证OCR识别结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "身份证OCR识别结果")
public class IdCardOcrResultDto {

    @Schema(description = "身份证类型", example = "Front", allowableValues = {"Front", "Back"})
    private String type;

    @Schema(description = "姓名", example = "苏辉")
    private String name;

    @Schema(description = "身份证号码", example = "511024199009308597")
    private String id;

    @Schema(description = "地址", example = "福建省泉州市丰泽区清源街道普明社区976号")
    private String addr;

    @Schema(description = "性别", example = "男")
    private String gender;

    @Schema(description = "民族", example = "汉")
    private String nationality;

    @Schema(description = "有效期", nullable = true, example = "null")
    private String validDate;

} 