package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.domain.ChronicDisease;
import space.lzhq.ph.dto.response.attachment.ChronicDiseaseRegistrationAttachmentDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.InsuranceType;
import space.lzhq.ph.enums.Relationship;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 慢病申报详情响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "慢病申报详情响应数据")
public class ChronicDiseaseRegistrationDetailResponseDto {

    @Schema(description = "申报ID", example = "abd2a91e6ff9f835a76f801e6ba9b3aa")
    private String id;

    @Schema(description = "用户OpenID", example = "omPh85Rdf1boaNRrC_AmUN0E62kU")
    private String openId;

    @Schema(description = "申报患者身份证附件", nullable = true)
    private ChronicDiseaseRegistrationAttachmentDto patientIdCardAttachment;

    @Schema(description = "申报患者身份证号", example = "110101199001011234")
    private String patientIdCardNo;

    @Schema(description = "申报患者姓名", example = "张三")
    private String patientName;

    @Schema(description = "申报患者性别", example = "男")
    private String patientGender;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Schema(description = "申报患者出生日期", example = "1990-01-01")
    private LocalDate patientBirthDate;

    @Schema(description = "申报患者医保卡附件", nullable = true)
    private ChronicDiseaseRegistrationAttachmentDto patientInsuranceCardAttachment;

    @Schema(description = "申报患者医保卡号", example = "1101011990010112345")
    private String patientInsuranceCardNo;

    @Schema(description = "医保类型")
    private InsuranceType insuranceType;

    @Schema(description = "申报患者手机号", example = "13800138000")
    private String patientMobile;

    @Schema(description = "是否代办", example = "0", allowableValues = {"0", "1"})
    private Integer agentFlag;

    @Schema(description = "代办人姓名", nullable = true, example = "null")
    private String agentName;

    @Schema(description = "代办人身份证号", nullable = true, example = "null")
    private String agentIdCardNo;

    @Schema(description = "代办人身份证附件", nullable = true)
    private ChronicDiseaseRegistrationAttachmentDto agentIdCardAttachment;

    @Schema(description = "代办人手机号", nullable = true, example = "null")
    private String agentMobile;

    @Schema(description = "代办人与患者关系", nullable = true, example = "null")
    private Relationship agentRelation;

    @Schema(description = "慢病病种", nullable = true)
    private ChronicDisease disease;

    @Schema(description = "认定资料附件列表", nullable = true)
    private List<ChronicDiseaseRegistrationAttachmentDto> documentAttachments;

    @Schema(description = "申报状态")
    private ChronicDiseaseRegistrationStatus status;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "提交时间", nullable = true, example = "null")
    private LocalDateTime submitTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审批时间", nullable = true, example = "null")
    private LocalDateTime approveTime;

    @Schema(description = "驳回原因", nullable = true, example = "null")
    private String rejectReason;

    @Schema(description = "整改意见", nullable = true, example = "null")
    private String correctionAdvice;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-05-23 18:51:30")
    private LocalDateTime createTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-05-23 18:51:30")
    private LocalDateTime updateTime;

    /**
     * 是否可以审批
     */
    private Boolean canApprove;
} 