package space.lzhq.ph.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用响应数据结构基类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "通用响应数据结构基类")
public class BaseResponseDto {
    
    @Schema(description = "响应码，0表示成功", example = "0")
    private int code;
    
    @Schema(description = "响应消息", example = "操作成功")
    private String msg;
} 