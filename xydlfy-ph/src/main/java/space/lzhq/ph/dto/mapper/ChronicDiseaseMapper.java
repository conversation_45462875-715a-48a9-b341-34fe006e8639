package space.lzhq.ph.dto.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import space.lzhq.ph.domain.ChronicDisease;
import space.lzhq.ph.dto.response.ChronicDiseaseResponseDto;

import java.util.List;

/**
 * 慢性病映射器
 * 负责慢性病实体类与DTO之间的转换
 */
@Mapper
public interface ChronicDiseaseMapper {

    ChronicDiseaseMapper INSTANCE = Mappers.getMapper(ChronicDiseaseMapper.class);

    /**
     * 将慢性病实体转换为响应DTO
     *
     * @param chronicDisease 慢性病实体
     * @return 慢性病响应DTO
     */
    ChronicDiseaseResponseDto toResponseDto(ChronicDisease chronicDisease);

    /**
     * 将慢性病实体列表转换为响应DTO列表
     *
     * @param chronicDiseases 慢性病实体列表
     * @return 慢性病响应DTO列表
     */
    List<ChronicDiseaseResponseDto> toResponseDtoList(List<ChronicDisease> chronicDiseases);

}