package space.lzhq.ph.dto.response.attachment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType;

import java.time.LocalDateTime;

/**
 * 慢病申报附件响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "慢病申报附件响应数据")
public class ChronicDiseaseRegistrationAttachmentDto {

    @Schema(description = "附件ID", example = "abd2a91e6ff9f835a76f801e6ba9b3aa")
    private String id;

    @Schema(description = "用户OpenID", example = "omPh85Rdf1boaNRrC_AmUN0E62kU")
    private String openId;

    @Schema(description = "关联的申报ID", nullable = true, example = "null")
    private String registrationId;

    @Schema(description = "附件类型")
    private ChronicDiseaseRegistrationAttachmentType type;

    @Schema(description = "文件名", example = "身份证_头像.jpg")
    private String fileName;

    @Schema(description = "文件URL", example = "https://ykdlfy.xjyqtl.cn:8888/profile/upload/2025/05/23/a52e9698b5fe7128a52e9698b5fe7128.jpg")
    private String fileUrl;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-05-23 18:51:30")
    private LocalDateTime createTime;

} 