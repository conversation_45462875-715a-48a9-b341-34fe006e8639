package space.lzhq.ph.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 慢病申报病种更新请求DTO
 */
@Data
@Schema(description = "慢病申报病种更新请求DTO")
public class ChronicDiseaseDiseaseUpdateDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 慢病病种ID
     */
    @Schema(description = "慢病病种ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "慢病病种ID不能为空")
    @Positive(message = "慢病病种ID必须为正整数")
    private Integer diseaseId;
} 