package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName(value = "V_DZPT_YBDZ", schema = "DZPT")
public class HisInsuranceOrder {

    /**
     * 结算时间
     */
    @TableField("结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime settlementTime;

    /**
     * 结算编号
     */
    @TableField("结算编号")
    private String settlementNo;

    /**
     * 医疗费用总额
     */
    @TableField("医疗费用总额")
    private BigDecimal totalMedicalFee;

    /**
     * 医保支付总额
     */
    @TableField("医保支付总额")
    private BigDecimal totalInsurancePayment;

    /**
     * 交易类型
     */
    @TableField("交易类型")
    private String tradeType;

    /**
     * 姓名
     */
    @TableField("姓名")
    private String patientName;

    /**
     * 个人编号
     */
    @TableField("个人编号")
    private String personalNo;

    /**
     * 应用
     */
    @TableField("应用")
    private String application;

    /**
     * 支付渠道
     */
    @TableField("支付渠道")
    private String payChannel;

    /**
     * 支付方式
     */
    @TableField("支付方式")
    private String payType;

    /**
     * 医疗类别
     */
    @TableField("医疗类别")
    private String medicalCategory;

    /**
     * 收款员
     */
    @TableField("收款员")
    private String cashier;

    /**
     * 备注
     */
    @TableField("BZ")
    private String remark;

    /**
     * 清算分中心
     */
    @TableField("清算分中心")
    private String clearingCenter;

    /**
     * 医保
     */
    @TableField("医保")
    private String insuranceType;

    /**
     * 个人账户
     */
    @TableField("个人账户")
    private BigDecimal personalAccount;

    /**
     * HISID
     */
    @TableField("HISID")
    private String hisId;

    /**
     * 险种类型
     */
    @TableField("险种类型")
    private String insuranceCategory;

    /**
     * 接口名称
     */
    @TableField("接口名称")
    private String interfaceName;

    // 以下是 getter 和 setter 方法
    public LocalDateTime getSettlementTime() {
        return settlementTime;
    }

    public void setSettlementTime(LocalDateTime settlementTime) {
        this.settlementTime = settlementTime;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public BigDecimal getTotalMedicalFee() {
        return totalMedicalFee;
    }

    public void setTotalMedicalFee(BigDecimal totalMedicalFee) {
        this.totalMedicalFee = totalMedicalFee;
    }

    public BigDecimal getTotalInsurancePayment() {
        return totalInsurancePayment;
    }

    public void setTotalInsurancePayment(BigDecimal totalInsurancePayment) {
        this.totalInsurancePayment = totalInsurancePayment;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPersonalNo() {
        return personalNo;
    }

    public void setPersonalNo(String personalNo) {
        this.personalNo = personalNo;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getMedicalCategory() {
        return medicalCategory;
    }

    public void setMedicalCategory(String medicalCategory) {
        this.medicalCategory = medicalCategory;
    }

    public String getCashier() {
        return cashier;
    }

    public void setCashier(String cashier) {
        this.cashier = cashier;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getClearingCenter() {
        return clearingCenter;
    }

    public void setClearingCenter(String clearingCenter) {
        this.clearingCenter = clearingCenter;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public BigDecimal getPersonalAccount() {
        return personalAccount;
    }

    public void setPersonalAccount(BigDecimal personalAccount) {
        this.personalAccount = personalAccount;
    }

    public String getHisId() {
        return hisId;
    }

    public void setHisId(String hisId) {
        this.hisId = hisId;
    }

    public String getInsuranceCategory() {
        return insuranceCategory;
    }

    public void setInsuranceCategory(String insuranceCategory) {
        this.insuranceCategory = insuranceCategory;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }
}