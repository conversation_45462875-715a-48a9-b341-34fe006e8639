package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 科室对象 ph_department
 *
 * @date 2020-05-24
 */
public class Department extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    @Excel(name = "科室分类编码")
    private String parentId;

    @Excel(name = "科室分类名称")
    private String parentName;

    /**
     * 编号
     */
    @Excel(name = "编号")
    private String id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String intro;

    private String logo;

    @Excel(name = "联系电话")
    private String telephone;

    /**
     * 位置
     */
    @Excel(name = "位置")
    private String address;

    @Excel(name = "关键词")
    private String keyword;

    /**
     * 排序号
     */
    @Excel(name = "排序号")
    private Long sortNo;

    /**
     * 简拼
     */
    @Excel(name = "简拼")
    private String simplePinyin;

    /**
     * 全拼
     */
    @Excel(name = "全拼")
    private String fullPinyin;

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getIntro() {
        return intro;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public void setSortNo(Long sortNo) {
        this.sortNo = sortNo;
    }

    public Long getSortNo() {
        return sortNo;
    }

    public void setSimplePinyin(String simplePinyin) {
        this.simplePinyin = simplePinyin;
    }

    public String getSimplePinyin() {
        return simplePinyin;
    }

    public void setFullPinyin(String fullPinyin) {
        this.fullPinyin = fullPinyin;
    }

    public String getFullPinyin() {
        return fullPinyin;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("intro", getIntro())
                .append("address", getAddress())
                .append("sortNo", getSortNo())
                .append("simplePinyin", getSimplePinyin())
                .append("fullPinyin", getFullPinyin())
                .toString();
    }
}
