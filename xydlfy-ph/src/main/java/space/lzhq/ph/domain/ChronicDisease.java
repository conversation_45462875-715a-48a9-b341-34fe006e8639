package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 慢性病及医保支持情况对象 chronic_disease
 */
@Data
@NoArgsConstructor
@TableName(value = "chronic_disease")
public class ChronicDisease implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 慢性病名称
     */
    @Xss(message = "慢性病名称不能包含特殊字符")
    @NotBlank(message = "慢性病名称不能为空")
    @Excel(name = "慢性病名称")
    private String name;

    /**
     * 乌鲁木齐市职工医保支持
     */
    @Excel(name = "乌鲁木齐市职工医保支持", readConverterExp = "0=不支持,1=支持")
    private Integer insuranceCityWorker;

    /**
     * 乌鲁木齐市居民医保支持
     */
    @Excel(name = "乌鲁木齐市居民医保支持", readConverterExp = "0=不支持,1=支持")
    private Integer insuranceCityResident;

    /**
     * 区医保（含铁路医保）支持
     */
    @Excel(name = "区医保支持", readConverterExp = "0=不支持,1=支持")
    private Integer insuranceDistrict;

    /**
     * 兵团医保（兵直、十一师、十二师）支持
     */
    @Excel(name = "兵团医保支持", readConverterExp = "0=不支持,1=支持")
    private Integer insuranceCorp;

    /**
     * 鉴定专家（已弃用，保留用于数据迁移）
     */
    @Xss(message = "鉴定专家不能包含特殊字符")
    @Excel(name = "鉴定专家")
    private String experts;

    /**
     * 关联的专家ID列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<Long> expertIds;

    /**
     * 关联的专家名称列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<String> expertNames;

    /**
     * 认定标准
     */
    @Xss(message = "认定标准不能包含特殊字符")
    @NotBlank(message = "认定标准不能为空")
    @Excel(name = "认定标准")
    private String criteria;

    /**
     * 认定资料
     */
    @Xss(message = "认定资料不能包含特殊字符")
    @NotBlank(message = "认定资料不能为空")
    @Excel(name = "认定资料")
    private String documents;
} 