package space.lzhq.ph.domain;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

public class HisSelfPayOrderSearchForm {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTradeDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTradeDate;

    private String bankOrderNo;

    private String patientId;

    public LocalDate getStartTradeDate() {
        return startTradeDate;
    }

    public void setStartTradeDate(LocalDate startTradeDate) {
        this.startTradeDate = startTradeDate;
    }

    public LocalDate getEndTradeDate() {
        return endTradeDate;
    }

    public void setEndTradeDate(LocalDate endTradeDate) {
        this.endTradeDate = endTradeDate;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }
}
