package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 慢病申报附件表对象 chronic_disease_registration_attachment
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "chronic_disease_registration_attachment")
public class ChronicDiseaseRegistrationAttachment implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "附件ID", example = "abd2a91e6ff9f835a76f801e6ba9b3aa")
    private String id;

    /**
     * 用户ID
     */
    @Schema(description = "用户OpenID", example = "omPh85Rdf1boaNRrC_AmUN0E62kU")
    private String openId;

    /**
     * 申报主表ID
     */
    @Schema(description = "关联的申报ID", nullable = true, example = "null")
    private String registrationId;

    /**
     * 附件类型
     */
    @Schema(description = "附件类型")
    private ChronicDiseaseRegistrationAttachmentType type;

    /**
     * 原始文件名
     */
    @Schema(description = "文件名", example = "身份证_头像.jpg")
    private String fileName;

    /**
     * 文件存储路径
     */
    @Schema(description = "文件URL", example = "https://ykdlfy.xjyqtl.cn:8888/profile/upload/2025/05/23/a52e9698b5fe7128a52e9698b5fe7128.jpg")
    private String fileUrl;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-05-23 18:51:30")
    private LocalDateTime createTime;

} 