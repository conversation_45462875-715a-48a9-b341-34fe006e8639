package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.common.ServiceType;

import java.io.Serial;

/**
 * 退款记录对象 ph_wx_refund
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
public class WxRefund extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * openid
     */
    @Excel(name = "openid")
    private String openid;

    @Excel(name = "患者标识")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 充值订单金额
     */
    @Excel(name = "充值订单金额")
    private Long totalAmount;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额")
    private Long amount;

    /**
     * 掌医充值订单号
     */
    @Excel(name = "掌医充值订单号")
    private String zyPayNo;

    /**
     * 微信充值订单号
     */
    @Excel(name = "微信充值订单号")
    private String wxPayNo;

    /**
     * 掌医退款订单号
     */
    @Excel(name = "掌医退款订单号")
    private String zyRefundNo;

    /**
     * 微信退款订单号
     */
    @Excel(name = "微信退款订单号")
    private String wxRefundNo;

    /**
     * HIS交易状态
     */
    @Excel(name = "HIS交易状态")
    private String hisTradeStatus;

    /**
     * 微信交易状态
     */
    @Excel(name = "微信交易状态")
    private String wxTradeStatus;

    /**
     * 人工退款状态
     */
    @Excel(name = "人工退款状态")
    private Integer manualRefundState;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public String getPatientNo() {
        return patientNo;
    }

    public void setPatientNo(String patientNo) {
        this.patientNo = patientNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public Long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getAmount() {
        return amount;
    }

    public void setZyPayNo(String zyPayNo) {
        this.zyPayNo = zyPayNo;
    }

    public String getZyPayNo() {
        return zyPayNo;
    }

    public void setWxPayNo(String wxPayNo) {
        this.wxPayNo = wxPayNo;
    }

    public String getWxPayNo() {
        return wxPayNo;
    }

    public void setZyRefundNo(String zyRefundNo) {
        this.zyRefundNo = zyRefundNo;
    }

    public String getZyRefundNo() {
        return zyRefundNo;
    }

    public void setWxRefundNo(String wxRefundNo) {
        this.wxRefundNo = wxRefundNo;
    }

    public String getWxRefundNo() {
        return wxRefundNo;
    }

    public void setHisTradeStatus(String hisTradeStatus) {
        this.hisTradeStatus = hisTradeStatus;
    }

    public String getHisTradeStatus() {
        return hisTradeStatus;
    }

    public void setWxTradeStatus(String wxTradeStatus) {
        this.wxTradeStatus = wxTradeStatus;
    }

    public String getWxTradeStatus() {
        return wxTradeStatus;
    }

    public Integer getManualRefundState() {
        return manualRefundState;
    }

    public void setManualRefundState(Integer manualRefundState) {
        this.manualRefundState = manualRefundState;
    }

    public boolean isMenzhen() {
        return ServiceType.MZ.name().equals(this.type);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("createTime", getCreateTime())
                .append("type", getType())
                .append("openid", getOpenid())
                .append("jzCardNo", getJzCardNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("amount", getAmount())
                .append("totalAmount", getTotalAmount())
                .append("zyPayNo", getZyPayNo())
                .append("wxPayNo", getWxPayNo())
                .append("zyRefundNo", getZyRefundNo())
                .append("wxRefundNo", getWxRefundNo())
                .append("hisTradeStatus", getHisTradeStatus())
                .append("wxTradeStatus", getWxTradeStatus())
                .append("manualRefundState", getManualRefundState())
                .toString();
    }
}
