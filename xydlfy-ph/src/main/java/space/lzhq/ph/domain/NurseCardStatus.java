package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.BaseEnum;

/**
 * 电子陪护证状态
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum NurseCardStatus implements BaseEnum {
    INIT(0, "待审核"),
    NORMAL(1, "正常"),
    EXPIRED(11, "已过期"),
    INVALID(12, "已作废"),
    CANCELLED(13, "已取消"),
    REJECTED(14, "审核不通过");

    private Integer code;
    private String description;

    NurseCardStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
