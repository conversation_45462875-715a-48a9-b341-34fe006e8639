package space.lzhq.ph.domain;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

public class HisInsuranceOrderSearchForm {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startSettlementDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endSettlementDate;

    private String settlementNo;

    private String personalNo;

    public LocalDate getStartSettlementDate() {
        return startSettlementDate;
    }

    public void setStartSettlementDate(LocalDate startSettlementDate) {
        this.startSettlementDate = startSettlementDate;
    }

    public LocalDate getEndSettlementDate() {
        return endSettlementDate;
    }

    public void setEndSettlementDate(LocalDate endSettlementDate) {
        this.endSettlementDate = endSettlementDate;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public String getPersonalNo() {
        return personalNo;
    }

    public void setPersonalNo(String personalNo) {
        this.personalNo = personalNo;
    }
}
