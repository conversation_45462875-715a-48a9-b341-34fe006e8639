package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@TableName(value = "reconciliation_daily")
public class DailyReconciliation {

    @TableId("date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    protected LocalDate date;

    private BigDecimal zhangyiWeixinAppPayAmount;

    private BigDecimal zhangyiWeixinHisPayAmount;

    private BigDecimal zhangyiWeixinDiffPayAmount;

    private Integer zhangyiWeixinPayBalanced;

    private BigDecimal zhangyiAlipayAppPayAmount;

    private BigDecimal zhangyiAlipayHisPayAmount;

    private BigDecimal zhangyiAlipayDiffPayAmount;

    private Integer zhangyiAlipayPayBalanced;

    private BigDecimal guoguangAtmAppPayAmount;

    private BigDecimal guoguangAtmHisPayAmount;

    private BigDecimal guoguangAtmDiffPayAmount;

    private Integer guoguangAtmPayBalanced;

    private BigDecimal ccbPosAppPayAmount;

    private BigDecimal ccbPosHisPayAmount;

    private BigDecimal ccbPosDiffPayAmount;

    private Integer ccbPosPayBalanced;

    private BigDecimal abcPosAppPayAmount;

    private BigDecimal abcPosHisPayAmount;

    private BigDecimal abcPosDiffPayAmount;

    private Integer abcPosPayBalanced;

    private BigDecimal totalAppPayAmount;

    private BigDecimal totalHisPayAmount;

    private BigDecimal totalDiffPayAmount;

    private Integer totalPayBalanced;

    private BigDecimal zhangyiWeixinAppRefundAmount;

    private BigDecimal zhangyiWeixinHisRefundAmount;

    private BigDecimal zhangyiWeixinDiffRefundAmount;

    private Integer zhangyiWeixinRefundBalanced;

    private BigDecimal zhangyiAlipayAppRefundAmount;

    private BigDecimal zhangyiAlipayHisRefundAmount;

    private BigDecimal zhangyiAlipayDiffRefundAmount;

    private Integer zhangyiAlipayRefundBalanced;

    private BigDecimal guoguangAtmAppRefundAmount;

    private BigDecimal guoguangAtmHisRefundAmount;

    private BigDecimal guoguangAtmDiffRefundAmount;

    private Integer guoguangAtmRefundBalanced;

    private BigDecimal ccbPosAppRefundAmount;

    private BigDecimal ccbPosHisRefundAmount;

    private BigDecimal ccbPosDiffRefundAmount;

    private Integer ccbPosRefundBalanced;

    private BigDecimal abcPosAppRefundAmount;

    private BigDecimal abcPosHisRefundAmount;

    private BigDecimal abcPosDiffRefundAmount;

    private Integer abcPosRefundBalanced;

    private BigDecimal weixinTransferAppRefundAmount;

    private BigDecimal weixinTransferHisRefundAmount;

    private BigDecimal weixinTransferDiffRefundAmount;

    private Integer weixinTransferRefundBalanced;

    private BigDecimal totalAppRefundAmount;

    private BigDecimal totalHisRefundAmount;

    private BigDecimal totalDiffRefundAmount;

    private Integer totalRefundBalanced;

    private BigDecimal zhangyiWeixinNetinAmount;

    private BigDecimal zhangyiAlipayNetinAmount;

    private BigDecimal guoguangAtmNetinAmount;

    private BigDecimal ccbPosNetinAmount;

    private BigDecimal abcPosNetinAmount;

    private BigDecimal weixinTransferNetinAmount;

    private BigDecimal totalNetinAmount;

    private BigDecimal hisNetinAmount;

    private BigDecimal diffNetinAmount;

    private Integer netinBalanced;
}
