package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ExcelIgnoreUnannotated
@TableName(value = "V_DZPT_DZMB", schema = "DZPT")
public class HisSelfPayOrder {

    /**
     * 交易时间
     */
    @ExcelProperty(value = "交易时间", index = 0)
    @ColumnWidth(25)
    @TableField("交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeTime;

    /**
     * 交易订单号
     */
    @ExcelProperty(value = "交易订单号", index = 1)
    @ColumnWidth(25)
    @TableField("交易订单号")
    private String tradeOrderNo;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额", index = 2)
    @ColumnWidth(15)
    @TableField("交易金额")
    private BigDecimal tradeAmount;

    /**
     * 交易类型
     */
    @ExcelProperty(value = "交易类型", index = 3)
    @ColumnWidth(15)
    @TableField("交易类型")
    private String tradeType;

    /**
     * 费率
     */
    @ExcelProperty(value = "费率", index = 4)
    @ColumnWidth(10)
    @TableField("费率")
    private String feeRate;

    /**
     * 手续费金额
     */
    @ExcelProperty(value = "手续费金额", index = 5)
    @ColumnWidth(15)
    @TableField("手续费金额")
    private String feeAmount;

    /**
     * 退款订单号
     */
    @ExcelProperty(value = "退款订单号", index = 6)
    @ColumnWidth(30)
    @TableField("退款订单号")
    private String refundOrderNo;

    /**
     * 退款金额
     */
    @ExcelProperty(value = "退款金额", index = 7)
    @ColumnWidth(15)
    @TableField("退款金额")
    private String refundAmount;

    /**
     * 退款时间
     */
    @ExcelProperty(value = "退款时间", index = 8)
    @ColumnWidth(20)
    @TableField("退款时间")
    private String refundTime;

    /**
     * 病人姓名
     */
    @ExcelProperty(value = "病人姓名", index = 9)
    @ColumnWidth(15)
    @TableField("病人姓名")
    private String patientName;

    /**
     * 病人ID
     */
    @ExcelProperty(value = "病人ID", index = 10)
    @ColumnWidth(25)
    @TableField("病人ID")
    private String patientId;

    /**
     * 应用
     */
    @ExcelProperty(value = "应用", index = 11)
    @ColumnWidth(15)
    @TableField("应用")
    private String application;

    /**
     * 支付渠道
     */
    @ExcelProperty(value = "支付渠道", index = 12)
    @ColumnWidth(15)
    @TableField("支付渠道")
    private String payChannel;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式", index = 13)
    @ColumnWidth(15)
    @TableField("支付方式")
    private String payType;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型", index = 14)
    @ColumnWidth(15)
    @TableField("业务类型")
    private String businessType;

    /**
     * 收款员
     */
    @ExcelProperty(value = "收款员", index = 15)
    @ColumnWidth(15)
    @TableField("收款员")
    private String cashier;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 16)
    @ColumnWidth(20)
    @TableField("备注")
    private String remark;

    /**
     * 银行订单号
     */
    @ExcelProperty(value = "银行订单号", index = 17)
    @ColumnWidth(35)
    @TableField("银行订单号")
    private String bankOrderNo;

    /**
     * 原始订单号
     */
    @ExcelProperty(value = "原始订单号", index = 18)
    @ColumnWidth(35)
    @TableField("原始订单号")
    private String originalOrderNo;

    /**
     * 入机日期
     */
    @TableField("RJRQ")
    private String entryDate;

    /**
     * 回执日期
     */
    @TableField("HZRQ")
    private String receiptDate;

    public LocalDateTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalDateTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getTradeOrderNo() {
        return tradeOrderNo;
    }

    public void setTradeOrderNo(String tradeOrderNo) {
        this.tradeOrderNo = tradeOrderNo;
    }

    public BigDecimal getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(BigDecimal tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(String feeRate) {
        this.feeRate = feeRate;
    }

    public String getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(String feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getCashier() {
        return cashier;
    }

    public void setCashier(String cashier) {
        this.cashier = cashier;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(String entryDate) {
        this.entryDate = entryDate;
    }

    public String getReceiptDate() {
        return receiptDate;
    }

    public void setReceiptDate(String receiptDate) {
        this.receiptDate = receiptDate;
    }

    public boolean isPayment() {
        return "支付".equals(tradeType);
    }

    public boolean isRefund() {
        return "退款".equals(tradeType);
    }

    public boolean isMenzhen() {
        return "门诊".equals(remark);
    }

    public boolean isZhuyuan() {
        return "住院".equals(remark);
    }
}
