package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.mospital.bsoft.mip.CreateOrderResult;
import org.mospital.wecity.mip.UserQueryResult;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName(value = "mip_wx_order")
public class MipWxOrder implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public MipWxOrder(UserQueryResult userQueryResult, Patient patient, CreateOrderResult.Data order) {
        this.createTime = LocalDateTime.now();
        this.openid = patient.getOpenId();

        this.userCardType = CharSequenceUtil.defaultIfNull(userQueryResult.getUserCardType(), "1");
        this.userName = userQueryResult.getUserName();
        this.userCardNo = userQueryResult.getUserCardNo();
        this.patientName = patient.getName();
        this.patientIdCardNo = patient.getIdCardNo();

        if (userQueryResult.isFamilyPay()) {
            this.familyPay = 1;
            this.payAuthNo = userQueryResult.getFamilyPayAuthNo();
        } else {
            this.familyPay = 0;
            this.payAuthNo = userQueryResult.getPayAuthNo();
        }

        this.longitudeLatitude = String.valueOf(userQueryResult.getLongitudeLatitude());
        this.payOrderId = order.getPayOrderId();
        this.hisOrderId = order.getMedOrgOrd();
        this.orderStatus = String.valueOf(order.getOrderStatus().getCode());
        this.feeSumAmount = order.getFeeSumAmount();
        this.ownPayAmount = order.getOwnPayAmount();
        this.personalAccountPayAmount = order.getPersonalAccountPayAmount();
        this.fundPayAmount = order.getFundPayAmount();
        this.hospitalOutTradeNo = "";
        this.medTransactionId = "";
        this.hospOutRefundNo = "";
        this.medRefundId = "";
        this.cashRefundId = "";
        this.insuranceRefundId = "";
    }

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String openid;

    /**
     * 用户姓名
     */
    @Excel(name = "用户姓名")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String userName;

    /**
     * 用户证件号码
     */
    @Excel(name = "用户证件号码")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String userCardNo;

    private String patientName;

    private String patientIdCardNo;

    /**
     * 用户证件类型
     */
    @Excel(name = "用户证件类型")
    private String userCardType;

    /**
     * 医保授权码
     */
    @Excel(name = "医保授权码")
    private String payAuthNo;

    /**
     * 是否亲属医保移动支付：0-否，1-是
     */
    private Integer familyPay;

    /**
     * 经纬度
     */
    @Excel(name = "经纬度")
    private String longitudeLatitude;

    /**
     * 支付订单号
     */
    @Excel(name = "支付订单号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String payOrderId;

    /**
     * HIS订单号
     */
    @Excel(name = "支付订单号")
    private String hisOrderId;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态")
    private String orderStatus;

    /**
     * 费用总额
     */
    @Excel(name = "费用总额")
    private BigDecimal feeSumAmount;

    /**
     * 现金支付金额
     */
    @Excel(name = "现金支付金额")
    private BigDecimal ownPayAmount;

    /**
     * 个人账户支出
     */
    @Excel(name = "个人账户支出")
    private BigDecimal personalAccountPayAmount;

    /**
     * 医保基金支出
     */
    @Excel(name = "医保基金支出")
    private BigDecimal fundPayAmount;

    /**
     * 第三方服务商订单号
     */
    @Excel(name = "第三方服务商订单号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalOutTradeNo;

    /**
     * 诊疗单ID
     */
    @Excel(name = "诊疗单ID")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String medTransactionId;

    private String settlementIds;

    private String insuranceOrderId;

    private String cashOrderId;

    private String medTradeState;

    private String cashTradeStatus;

    private String insuranceTradeStatus;

    private String tradeStatusDesc;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime unlockTime;

    /**
     * 医院退款订单号
     */
    @Excel(name = "医院退款订单号")
    private String hospOutRefundNo;

    /**
     * 微信生成的退款医疗订单号
     */
    @Excel(name = "微信生成的退款医疗订单号")
    private String medRefundId;

    /**
     * 微信医保支付生成的微信支付退款订单号
     */
    @Excel(name = "微信医保支付生成的微信支付退款订单号")
    private String cashRefundId;

    /**
     * 微信医保支付生成的医保退款单号
     */
    @Excel(name = "微信医保支付生成的医保退款单号")
    private String insuranceRefundId;

    public boolean isSelfPay() {
        return familyPay != null && familyPay == 0;
    }

}
