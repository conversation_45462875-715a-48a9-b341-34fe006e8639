package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.hutool.core.data.id.IdUtil;
import org.mospital.alipay.AlipayBillItem;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@ExcelIgnoreUnannotated
@TableName(value = "reconciliation_zhangyi_alipay_detail")
@Data
@NoArgsConstructor
public class ZhangyiAlipayReconciliationDetail implements ReconciliationDetail {

    @TableId("id")
    private String id;

    @ExcelProperty(value = "账单日")
    @ColumnWidth(15)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDate;

    @ExcelProperty(value = "支付宝交易号")
    @ColumnWidth(35)
    private String channelOrderNo;

    @ExcelProperty(value = "应用订单号")
    @ColumnWidth(25)
    private String appOrderNo;

    @ExcelProperty(value = "HIS交易时间")
    @ColumnWidth(25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime hisTradeTime;

    @ExcelProperty(value = "应用交易时间")
    @ColumnWidth(25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appTradeTime;

    @ExcelProperty(value = "HIS交易金额")
    @ColumnWidth(12)
    private BigDecimal hisTradeAmount;

    @ExcelProperty(value = "应用交易金额")
    @ColumnWidth(12)
    private BigDecimal appTradeAmount;

    @ExcelProperty(value = "交易类型")
    @ColumnWidth(12)
    private String tradeType;

    @ExcelProperty(value = "患者ID")
    @ColumnWidth(25)
    private String patientId;

    @ExcelProperty(value = "患者姓名")
    @ColumnWidth(20)
    private String patientName;

    @ExcelProperty(value = "支付宝账户")
    @ColumnWidth(30)
    private String channelAccount;

    @ExcelProperty(value = "业务类型")
    @ColumnWidth(12)
    private String businessType;

    @ExcelProperty(value = "结果", converter = ResultConverter.class)
    @ColumnWidth(15)
    private Integer result;

    public ZhangyiAlipayReconciliationDetail(LocalDate billDate, AlipayBillItem alipayBillItem) {
        this.id = billDate.format(DateTimeFormatter.BASIC_ISO_DATE) + IdUtil.fastSimpleUUID();
        this.billDate = billDate;
        this.appTradeTime = alipayBillItem.getCompleteTime();
        this.channelOrderNo = alipayBillItem.getTradeNo();
        this.channelAccount = alipayBillItem.getBuyer();
        this.result = RESULT_APP_SURPLUS;
        if (alipayBillItem.isPay()) {
            this.appOrderNo = alipayBillItem.getOutTradeNo();
            this.appTradeAmount = alipayBillItem.getTotalAmount();
            this.tradeType = TRADE_TYPE_PAY;
        } else {
            this.appOrderNo = alipayBillItem.getRefundNo();
            this.appTradeAmount = alipayBillItem.getTotalAmount().negate();
            this.tradeType = TRADE_TYPE_REFUND;
        }
    }

    public ZhangyiAlipayReconciliationDetail(LocalDate billDate, HisSelfPayOrder hisOrder) {
        this.id = billDate.format(DateTimeFormatter.BASIC_ISO_DATE) + IdUtil.fastSimpleUUID();
        this.billDate = billDate;
        this.hisTradeTime = hisOrder.getTradeTime();
        this.hisTradeAmount = hisOrder.getTradeAmount();
        this.patientId = hisOrder.getPatientId();
        this.patientName = hisOrder.getPatientName();
        this.businessType = hisOrder.getRemark();
        this.result = RESULT_HIS_SURPLUS;
        if (hisOrder.isPayment()) {
            this.appOrderNo = hisOrder.getBankOrderNo();
            this.tradeType = TRADE_TYPE_PAY;
        } else {
            this.appOrderNo = hisOrder.getBankOrderNo();
            this.tradeType = TRADE_TYPE_REFUND;
        }
    }

    public static class ResultConverter implements Converter<Integer> {
        @Override
        public WriteCellData<?> convertToExcelData(Integer resultValue, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String resultDescription = switch (resultValue) {
                case RESULT_BALANCED -> "平账";
                case RESULT_APP_SURPLUS -> "支付宝多";
                case RESULT_HIS_SURPLUS -> "HIS多";
                case RESULT_AMOUNT_MISMATCH -> "金额不一致";
                default -> "未知";
            };
            return new WriteCellData<>(resultDescription);
        }
    }
} 