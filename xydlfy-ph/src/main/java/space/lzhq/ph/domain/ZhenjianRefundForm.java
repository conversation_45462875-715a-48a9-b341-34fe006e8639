package space.lzhq.ph.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZhenjianRefundForm implements Serializable {

    @Serial
    private static final long serialVersionUID = -6722078016383106499L;

    private BigDecimal amount;

    private String orderNumber;

} 