package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.hutool.core.data.id.IdUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@NoArgsConstructor
@ExcelIgnoreUnannotated
@TableName(value = "reconciliation_abc_pos_detail")
public class AbcPosReconciliationDetail implements ReconciliationDetail {

    @TableId("id")
    private String id;

    @ExcelProperty(value = "账单日")
    @ColumnWidth(15)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDate;

    @ExcelProperty(value = "银行订单号")
    @ColumnWidth(30)
    private String bankOrderNo;

    @ExcelProperty(value = "POS订单号")
    @ColumnWidth(35)
    private String appOrderNo;

    @ExcelProperty(value = "HIS交易时间")
    @ColumnWidth(25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime hisTradeTime;

    @ExcelProperty(value = "POS交易时间")
    @ColumnWidth(25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appTradeTime;

    @ExcelProperty(value = "HIS交易金额")
    @ColumnWidth(12)
    private BigDecimal hisTradeAmount;

    @ExcelProperty(value = "POS交易金额")
    @ColumnWidth(12)
    private BigDecimal appTradeAmount;

    @ExcelProperty(value = "交易类型")
    @ColumnWidth(12)
    private String tradeType;

    @ExcelProperty(value = "患者ID")
    @ColumnWidth(20)
    private String patientId;

    @ExcelProperty(value = "患者姓名")
    @ColumnWidth(20)
    private String patientName;

    @ExcelProperty(value = "业务类型")
    @ColumnWidth(12)
    private String businessType;

    @ExcelProperty(value = "收款员")
    @ColumnWidth(20)
    private String cashier;

    @ExcelProperty(value = "POS编号")
    @ColumnWidth(20)
    private String posId;

    @ExcelProperty(value = "结果", converter = CcbPosReconciliationDetail.ResultConverter.class)
    @ColumnWidth(15)
    private Integer result;

    public AbcPosReconciliationDetail(LocalDate billDate, AbcPosOrder appOrder) {
        this.id = billDate.format(DateTimeFormatter.BASIC_ISO_DATE) + IdUtil.fastSimpleUUID();
        this.billDate = billDate;
        this.appTradeTime = appOrder.getTradeTime();
        this.bankOrderNo = appOrder.getTransactionId();
        this.appOrderNo = appOrder.getOutTradeNo();
        this.posId = appOrder.getPosId();
        this.result = RESULT_APP_SURPLUS;
        if (appOrder.isPay()) {
            this.appTradeAmount = appOrder.getAmount().abs();
            this.tradeType = TRADE_TYPE_PAY;
        } else {
            this.appTradeAmount = appOrder.getAmount().abs();
            this.tradeType = TRADE_TYPE_REFUND;
        }
    }

    public AbcPosReconciliationDetail(LocalDate billDate, HisSelfPayOrder hisOrder) {
        this.id = billDate.format(DateTimeFormatter.BASIC_ISO_DATE) + IdUtil.fastSimpleUUID();
        this.billDate = billDate;
        this.hisTradeTime = hisOrder.getTradeTime();
        this.hisTradeAmount = hisOrder.getTradeAmount();
        this.patientId = hisOrder.getPatientId();
        this.patientName = hisOrder.getPatientName();
        this.businessType = hisOrder.getRemark();
        this.cashier = hisOrder.getCashier();
        this.result = RESULT_HIS_SURPLUS;
        if (hisOrder.isPayment()) {
            this.appOrderNo = hisOrder.getRefundOrderNo();
            this.bankOrderNo = hisOrder.getBankOrderNo();
            this.tradeType = TRADE_TYPE_PAY;
        } else {
            this.appOrderNo = hisOrder.getBankOrderNo();
            this.bankOrderNo = hisOrder.getRefundOrderNo();
            this.tradeType = TRADE_TYPE_REFUND;
        }
    }

}
