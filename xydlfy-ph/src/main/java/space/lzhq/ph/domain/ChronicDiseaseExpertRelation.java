package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 慢病病种专家关联关系对象 chronic_disease_expert_relation
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "chronic_disease_expert_relation")
public class ChronicDiseaseExpertRelation extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 病种ID
     */
    private Integer diseaseId;

    /**
     * 专家ID（用户ID）
     */
    private Long expertId;

    public ChronicDiseaseExpertRelation(Integer diseaseId, Long expertId) {
        this.diseaseId = diseaseId;
        this.expertId = expertId;
    }
} 