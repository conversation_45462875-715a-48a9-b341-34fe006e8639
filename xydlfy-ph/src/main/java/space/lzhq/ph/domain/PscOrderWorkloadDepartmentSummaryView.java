package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@ExcelIgnoreUnannotated
public class PscOrderWorkloadDepartmentSummaryView implements Serializable {

    @Serial
    private static final long serialVersionUID = 4220022089814891818L;

    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate date;

    @ExcelProperty(value = "工号")
    @ColumnWidth(15)
    private String carerEmployeeNo;

    @ExcelProperty(value = "姓名")
    @ColumnWidth(15)
    private String carerName;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String carerMobile;

    @ExcelProperty(value = "科室")
    @ColumnWidth(15)
    private String patientDepartment;

    @ExcelProperty(value = {"床", "人数"})
    private Integer sickbedOrderCount;

    @ExcelProperty(value = {"床", "项目"})
    private BigDecimal sickbedItemCount;

    @ExcelProperty(value = {"轮椅", "人数"})
    private Integer wheelchairOrderCount;

    @ExcelProperty(value = {"轮椅", "项目"})
    private BigDecimal wheelchairItemCount;

    @ExcelProperty(value = {"陪检", "人数"})
    private Integer guideOrderCount;

    @ExcelProperty(value = {"陪检", "项目"})
    private BigDecimal guideItemCount;

    @ExcelProperty(value = {"手术", "人数"})
    private BigDecimal surgeryOrderCount;

    @ExcelProperty(value = {"手术", "项目"})
    private BigDecimal surgeryItemCount;

    @ExcelProperty(value = {"总合计", "总人数"})
    private Integer totalOrderCount;

    @ExcelProperty(value = {"总合计", "总项目"})
    private BigDecimal totalItemCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCarerEmployeeNo() {
        return carerEmployeeNo;
    }

    public void setCarerEmployeeNo(String carerEmployeeNo) {
        this.carerEmployeeNo = carerEmployeeNo;
    }

    public String getCarerName() {
        return carerName;
    }

    public void setCarerName(String carerName) {
        this.carerName = carerName;
    }

    public String getCarerMobile() {
        return carerMobile;
    }

    public void setCarerMobile(String carerMobile) {
        this.carerMobile = carerMobile;
    }

    public String getPatientDepartment() {
        return patientDepartment;
    }

    public void setPatientDepartment(String patientDepartment) {
        this.patientDepartment = patientDepartment;
    }

    public Integer getSickbedOrderCount() {
        return sickbedOrderCount;
    }

    public void setSickbedOrderCount(Integer sickbedOrderCount) {
        this.sickbedOrderCount = sickbedOrderCount;
    }

    public BigDecimal getSickbedItemCount() {
        return sickbedItemCount;
    }

    public void setSickbedItemCount(BigDecimal sickbedItemCount) {
        this.sickbedItemCount = sickbedItemCount;
    }

    public Integer getWheelchairOrderCount() {
        return wheelchairOrderCount;
    }

    public void setWheelchairOrderCount(Integer wheelchairOrderCount) {
        this.wheelchairOrderCount = wheelchairOrderCount;
    }

    public BigDecimal getWheelchairItemCount() {
        return wheelchairItemCount;
    }

    public void setWheelchairItemCount(BigDecimal wheelchairItemCount) {
        this.wheelchairItemCount = wheelchairItemCount;
    }

    public Integer getGuideOrderCount() {
        return guideOrderCount;
    }

    public void setGuideOrderCount(Integer guideOrderCount) {
        this.guideOrderCount = guideOrderCount;
    }

    public BigDecimal getGuideItemCount() {
        return guideItemCount;
    }

    public void setGuideItemCount(BigDecimal guideItemCount) {
        this.guideItemCount = guideItemCount;
    }

    public BigDecimal getSurgeryOrderCount() {
        return surgeryOrderCount;
    }

    public void setSurgeryOrderCount(BigDecimal surgeryOrderCount) {
        this.surgeryOrderCount = surgeryOrderCount;
    }

    public BigDecimal getSurgeryItemCount() {
        return surgeryItemCount;
    }

    public void setSurgeryItemCount(BigDecimal surgeryItemCount) {
        this.surgeryItemCount = surgeryItemCount;
    }

    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    public BigDecimal getTotalItemCount() {
        return totalItemCount;
    }

    public void setTotalItemCount(BigDecimal totalItemCount) {
        this.totalItemCount = totalItemCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PscOrderWorkloadSummary)) return false;

        PscOrderWorkloadSummary that = (PscOrderWorkloadSummary) o;

        if (!getCarerEmployeeNo().equals(that.getCarerEmployeeNo())) return false;
        if (!getCarerName().equals(that.getCarerName())) return false;
        if (!getCarerMobile().equals(that.getCarerMobile())) return false;
        if (!getSickbedOrderCount().equals(that.getSickbedOrderCount())) return false;
        if (!getSickbedItemCount().equals(that.getSickbedItemCount())) return false;
        if (!getWheelchairOrderCount().equals(that.getWheelchairOrderCount())) return false;
        if (!getWheelchairItemCount().equals(that.getWheelchairItemCount())) return false;
        if (!getGuideOrderCount().equals(that.getGuideOrderCount())) return false;
        if (!getGuideItemCount().equals(that.getGuideItemCount())) return false;
        if (!getSurgeryOrderCount().equals(that.getSurgeryOrderCount())) return false;
        if (!getSurgeryItemCount().equals(that.getSurgeryItemCount())) return false;
        if (!getTotalOrderCount().equals(that.getTotalOrderCount())) return false;
        return getTotalItemCount().equals(that.getTotalItemCount());
    }

    @Override
    public int hashCode() {
        int result = getCarerEmployeeNo().hashCode();
        result = 31 * result + getCarerName().hashCode();
        result = 31 * result + getCarerMobile().hashCode();
        result = 31 * result + getSickbedOrderCount().hashCode();
        result = 31 * result + getSickbedItemCount().hashCode();
        result = 31 * result + getWheelchairOrderCount().hashCode();
        result = 31 * result + getWheelchairItemCount().hashCode();
        result = 31 * result + getGuideOrderCount().hashCode();
        result = 31 * result + getGuideItemCount().hashCode();
        result = 31 * result + getSurgeryOrderCount().hashCode();
        result = 31 * result + getSurgeryItemCount().hashCode();
        result = 31 * result + getTotalOrderCount().hashCode();
        result = 31 * result + getTotalItemCount().hashCode();
        return result;
    }

    public static PscOrderWorkloadDepartmentSummaryView fromOrders(List<PscOrderWorkloadSummary> orders) {
        if (orders == null || orders.isEmpty()) {
            return null;
        }

        int sickbedOrderCount = 0;
        BigDecimal sickbedItemCount = BigDecimal.ZERO;
        int wheelchairOrderCount = 0;
        BigDecimal wheelchairItemCount = BigDecimal.ZERO;
        int guideOrderCount = 0;
        BigDecimal guideItemCount = BigDecimal.ZERO;
        BigDecimal surgeryOrderCount = BigDecimal.ZERO;
        BigDecimal surgeryItemCount = BigDecimal.ZERO;
        int totalOrderCount = 0;
        BigDecimal totalItemCount = BigDecimal.ZERO;

        for (PscOrderWorkloadSummary order : orders) {
            sickbedOrderCount += order.getSickbedOrderCount();
            sickbedItemCount = sickbedItemCount.add(order.getSickbedItemCount());
            wheelchairOrderCount += order.getWheelchairOrderCount();
            wheelchairItemCount = wheelchairItemCount.add(order.getWheelchairItemCount());
            guideOrderCount += order.getGuideOrderCount();
            guideItemCount = guideItemCount.add(order.getGuideItemCount());
            surgeryOrderCount = surgeryOrderCount.add(order.getSurgeryOrderCount());
            surgeryItemCount = surgeryItemCount.add(order.getSurgeryItemCount());
            totalOrderCount += order.getTotalOrderCount();
            totalItemCount = totalItemCount.add(order.getTotalItemCount());
        }

        PscOrderWorkloadDepartmentSummaryView view = new PscOrderWorkloadDepartmentSummaryView();
        view.setCarerEmployeeNo(orders.get(0).getCarerEmployeeNo());
        view.setCarerName(orders.get(0).getCarerName());
        view.setCarerMobile(orders.get(0).getCarerMobile());
        view.setPatientDepartment(orders.get(0).getPatientDepartment());
        view.setSickbedOrderCount(sickbedOrderCount);
        view.setSickbedItemCount(sickbedItemCount);
        view.setWheelchairOrderCount(wheelchairOrderCount);
        view.setWheelchairItemCount(wheelchairItemCount);
        view.setGuideOrderCount(guideOrderCount);
        view.setGuideItemCount(guideItemCount);
        view.setSurgeryOrderCount(surgeryOrderCount);
        view.setSurgeryItemCount(surgeryItemCount);
        view.setTotalOrderCount(totalOrderCount);
        view.setTotalItemCount(totalItemCount);

        if ("*".equals(view.getPatientDepartment())) {
            view.setPatientDepartment("合计");
        }

        return view;
    }
}
