package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.mospital.bsoft.hai.PatientInfo;
import org.mospital.common.StringKit;
import space.lzhq.ph.common.ClientType;
import space.lzhq.ph.ext.HisExt;

import java.io.Serial;
import java.util.Date;

/**
 * 就诊人对象 ph_patient
 *
 * @date 2020-05-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Patient extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 原始身份证号（用于验证），不会被序列化
     */
    @JsonIgnore
    private String originalIdCardNo;

    /**
     * 客户端
     */
    private Integer clientType;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 患者索引号
     */
    @Excel(name = "患者索引号")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 门诊号
     */
    @Excel(name = "门诊号")
    private String menzhenNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    private String admissionNumber;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别：0=男，1=女
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer gender;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * $column.columnComment
     */
    @Excel(name = "openid")
    private String openId;

    /**
     * $column.columnComment
     */
    @Excel(name = "unionid")
    private String unionId;

    /**
     * 是否默认卡
     */
    @Excel(name = "是否默认卡", readConverterExp = "1=是,0=否")
    private Integer active;

    /**
     * 电子健康卡主索引号
     */
    @Excel(name = "电子健康卡主索引号")
    private String empi;

    /**
     * 电子健康卡号
     */
    @Excel(name = "电子健康卡号")
    private String erhcCardNo;

    /**
     * 入院时间
     */
    @Excel(name = "入院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ruyuanTime;

    /**
     * 出院时间
     */
    @Excel(name = "出院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date chuyuanTime;

    /**
     * 门诊余额
     */
    private Double menzhenBalance;

    /**
     * 住院余额
     */
    private Double zhuyuanBalance;

    public Patient(Session session, PatientInfo menzhenPatient, String mobile) {
        this.clientType = session.getClientType();
        this.idCardNo = menzhenPatient.getPatientIdCard();
        this.patientNo = menzhenPatient.getPatientId();
        this.jzCardNo = menzhenPatient.getCardNo();
        this.menzhenNo = menzhenPatient.getOutpatientNumber();
        this.zhuyuanNo = "";
        this.name = menzhenPatient.getPatientName().trim();
        this.gender = "1".equals(menzhenPatient.getPatientSex()) ? 0 : 1;
        this.mobile = StrUtil.trim(StrUtil.defaultIfBlank(menzhenPatient.getPatientPhone(), mobile));
        this.openId = session.getOpenId();
        this.unionId = session.getUnionId();
        this.active = 0;
        this.empi = "";
        this.erhcCardNo = "";
        this.menzhenBalance = null;
        this.zhuyuanBalance = null;
    }

    public ClientType getClientTypeEnum() {
        if (getClientType() == null) {
            return ClientType.WEIXIN;
        } else {
            return ClientType.Companion.fromCode(getClientType());
        }
    }

    public Boolean isMale() {
        return getGender() != null && getGender() == 0;
    }

    public Boolean isFemale() {
        return getGender() != null && getGender() == 0;
    }

    public boolean hasErhcCard() {
        return !erhcCardNo.isEmpty();
    }

    public Patient maskSensitive() {
        if (this.originalIdCardNo == null) {
            this.originalIdCardNo = this.idCardNo;
        }
        this.idCardNo = StringKit.INSTANCE.hideIdCardNo(this.idCardNo);
        this.mobile = StringKit.INSTANCE.hideMobile(this.mobile);
        this.name = StringKit.INSTANCE.hideName(this.name);
        return this;
    }

    /**
     * 获取二维码加密字符串，仅在JSON序列化时使用
     */
    @JsonGetter("qrcode")
    public String getQrcode() {
        return HisExt.INSTANCE.encryptForQrCode(this.getJzCardNo());
    }

    /**
     * 获取是否可以充值，仅在JSON序列化时使用
     * 规则：
     * 1. 身份证号必须有效
     * 2. 年龄在14岁以下或65岁以上可以使用充值功能
     */
    @JsonGetter("canUseRecharge")
    public boolean canUseRecharge() {
        String idCardToCheck = originalIdCardNo != null ? originalIdCardNo : idCardNo;
        
        // 1. 检查身份证号是否有效
        if (!IdcardUtil.isValidCard(idCardToCheck)) {
            return false;
        }

        // 2. 获取年龄
        int age = IdcardUtil.getAge(idCardToCheck);

        // 3. 判断年龄是否符合条件（14岁以下或65岁以上）
        return age <= 14 || age >= 65;
    }
}