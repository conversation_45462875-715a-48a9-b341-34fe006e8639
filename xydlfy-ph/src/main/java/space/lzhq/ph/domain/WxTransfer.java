package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mospital.common.StringKit;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ph_wx_transfer")
public class WxTransfer {

    @TableField(exist = false)
    public static final List<String> FINAL_STATES = Arrays.asList(
            "SUCCESS",           // 转账成功
            "FAILED",            // 转账失败
            "BANK_FAIL"          // 银行退票
    );

    @TableField(exist = false)
    public static final String WAIT_USER_CONFIRM = "WAIT_USER_CONFIRM";

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    private String openId;

    private String userIdCardNo;

    private String userName;

    private String userMobile;

    private String patientIdCardNo;

    private String patientName;

    private String patientMobile;

    private String patientCardNo;

    private String patientId;

    private BigDecimal amount;

    private Boolean hisRefundSuccess;

    private String hisOrderNo;

    private String hisReceiptNo;

    private String hisRefundFailReason;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime hisRefundTime;

    /**
     * 商户转账单号
     */
    private String outBillNo;

    /**
     * 微信转账单号
     */
    private String transferBillNo;

    private String transferState;

    private String transferPackageInfo;

    private String transferFailReason;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transferCreateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transferUpdateTime;

    public WxTransfer maskSensitive() {
        this.userIdCardNo = StringKit.INSTANCE.hideIdCardNo(this.userIdCardNo);
        this.userMobile = StringKit.INSTANCE.hideMobile(this.userMobile);
        this.userName = StringKit.INSTANCE.hideName(this.userName);
        this.patientIdCardNo = StringKit.INSTANCE.hideIdCardNo(this.patientIdCardNo);
        this.patientMobile = StringKit.INSTANCE.hideMobile(this.patientMobile);
        this.patientName = StringKit.INSTANCE.hideName(this.patientName);
        this.patientCardNo = StringKit.INSTANCE.hideIdCardNo(this.patientCardNo);
        return this;
    }
}
