package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serial;
import java.io.Serializable;

@TableName(value = "psc_department")
public class PscDepartment implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = -8850129385384585974L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 部门名称
     */
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
