package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class PscOrderWorkloadSummaryView implements Serializable {

    @Serial
    private static final long serialVersionUID = 4220022089814891818L;

    @ExcelProperty(value = "工号")
    @ColumnWidth(15)
    private String carerEmployeeNo;

    @ExcelProperty(value = "姓名")
    @ColumnWidth(15)
    private String carerName;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String carerMobile;

    @ExcelProperty(value = {"床", "人数"})
    private Integer sickbedOrderCount;

    @ExcelProperty(value = {"床", "项目"})
    private BigDecimal sickbedItemCount;

    @ExcelProperty(value = {"轮椅", "人数"})
    private Integer wheelchairOrderCount;

    @ExcelProperty(value = {"轮椅", "项目"})
    private BigDecimal wheelchairItemCount;

    @ExcelProperty(value = {"陪检", "人数"})
    private Integer guideOrderCount;

    @ExcelProperty(value = {"陪检", "项目"})
    private BigDecimal guideItemCount;

    @ExcelProperty(value = {"手术", "人数"})
    private BigDecimal surgeryOrderCount;

    @ExcelProperty(value = {"手术", "项目"})
    private BigDecimal surgeryItemCount;

    @ExcelProperty(value = {"约单", "人数"})
    private Integer appointmentOrderCount;

    @ExcelProperty(value = {"约单", "项目"})
    private BigDecimal appointmentItemCount;

    @ExcelProperty(value = {"总合计", "总人数"})
    private Integer totalOrderCount;

    @ExcelProperty(value = {"总合计", "总项目"})
    private BigDecimal totalItemCount;

    public static PscOrderWorkloadSummaryView fromOrders(List<PscOrderWorkloadSummary> orders) {
        if (orders == null || orders.isEmpty()) {
            return null;
        }

        int sickbedOrderCount = 0;
        BigDecimal sickbedItemCount = BigDecimal.ZERO;
        int wheelchairOrderCount = 0;
        BigDecimal wheelchairItemCount = BigDecimal.ZERO;
        int guideOrderCount = 0;
        BigDecimal guideItemCount = BigDecimal.ZERO;
        BigDecimal surgeryOrderCount = BigDecimal.ZERO;
        BigDecimal surgeryItemCount = BigDecimal.ZERO;
        int appointmentOrderCount = 0;
        BigDecimal appointmentItemCount = BigDecimal.ZERO;
        int totalOrderCount = 0;
        BigDecimal totalItemCount = BigDecimal.ZERO;

        for (PscOrderWorkloadSummary order : orders) {
            sickbedOrderCount += order.getSickbedOrderCount();
            sickbedItemCount = sickbedItemCount.add(order.getSickbedItemCount());
            wheelchairOrderCount += order.getWheelchairOrderCount();
            wheelchairItemCount = wheelchairItemCount.add(order.getWheelchairItemCount());
            guideOrderCount += order.getGuideOrderCount();
            guideItemCount = guideItemCount.add(order.getGuideItemCount());
            surgeryOrderCount = surgeryOrderCount.add(order.getSurgeryOrderCount());
            surgeryItemCount = surgeryItemCount.add(order.getSurgeryItemCount());
            appointmentOrderCount += order.getAppointmentOrderCount();
            appointmentItemCount = appointmentItemCount.add(order.getAppointmentItemCount());
            totalOrderCount += order.getTotalOrderCount();
            totalItemCount = totalItemCount.add(order.getTotalItemCount());
        }

        PscOrderWorkloadSummaryView view = new PscOrderWorkloadSummaryView();
        view.setCarerEmployeeNo(orders.getFirst().getCarerEmployeeNo());
        view.setCarerName(orders.getFirst().getCarerName());
        view.setCarerMobile(orders.getFirst().getCarerMobile());
        view.setSickbedOrderCount(sickbedOrderCount);
        view.setSickbedItemCount(sickbedItemCount);
        view.setWheelchairOrderCount(wheelchairOrderCount);
        view.setWheelchairItemCount(wheelchairItemCount);
        view.setGuideOrderCount(guideOrderCount);
        view.setGuideItemCount(guideItemCount);
        view.setSurgeryOrderCount(surgeryOrderCount);
        view.setSurgeryItemCount(surgeryItemCount);
        view.setAppointmentOrderCount(appointmentOrderCount);
        view.setAppointmentItemCount(appointmentItemCount);
        view.setTotalOrderCount(totalOrderCount);
        view.setTotalItemCount(totalItemCount);
        return view;
    }
}
