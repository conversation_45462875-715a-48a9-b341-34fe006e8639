package space.lzhq.ph.domain;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class LisReport implements Serializable {

    @Serial
    private static final long serialVersionUID = -7652802343179495147L;

    private String id;
    private String name;
    private String patientId;
    private String type;
    private String gender;
    private String purpose;
    private String examTime;
    private Date reportTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getGender() {
        return gender;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getExamTime() {
        return examTime;
    }

    public void setExamTime(String examTime) {
        this.examTime = examTime;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public boolean isMenzhen() {
        return "2".equals(this.type) || "4".equals(this.type);
    }

    public boolean isZhuyuan() {
        return "1".equals(this.type) || "3".equals(this.type);
    }

    /**
     * 是否是微生物报告
     */
    public boolean isMicrobe() {
        return this.id.startsWith("00000000");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LisReport)) {
            return false;
        }
        LisReport lisReport = (LisReport) o;
        return getId().equals(lisReport.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }


}
