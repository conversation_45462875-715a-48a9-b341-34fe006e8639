package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.mospital.zktec.Package;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 体检套餐对象 ph_tijian_package
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
public class TijianPackage extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    private String id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 价格
     */
    @Excel(name = "价格")
    private BigDecimal price;

    /**
     * 性别
     */
    @Excel(name = "性别")
    private Integer gender;

    /**
     * 婚姻状况
     */
    @Excel(name = "婚姻状况")
    private Integer maritalStatus;

    public TijianPackage() {
    }

    public TijianPackage(Package that) {
        this.id = that.getId();
        this.name = that.getName();
        this.price = that.getPrice();
        this.gender = that.getGender();
        this.maritalStatus = that.getMaritalStatus();
        this.setRemark(that.getRemark());
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getGender() {
        return gender;
    }

    public void setMaritalStatus(Integer maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public Integer getMaritalStatus() {
        return maritalStatus;
    }

    public Boolean isAvailableForMale() {
        return getGender() == 0 || getGender() == 1;
    }

    public Boolean isAvailableForFemale() {
        return getGender() == 0 || getGender() == 2;
    }
    
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("price", getPrice())
                .append("gender", getGender())
                .append("maritalStatus", getMaritalStatus())
                .append("remark", getRemark())
                .toString();
    }
}
