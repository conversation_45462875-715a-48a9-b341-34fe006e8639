package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import cn.yqtl.pay.ccb.model.BillDetail;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.hutool.core.data.id.IdUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@NoArgsConstructor
@ExcelIgnoreUnannotated
@TableName(value = "reconciliation_guoguang_atm_detail")
public class GuoguangAtmReconciliationDetail implements ReconciliationDetail {

    @TableId("id")
    private String id;

    @ExcelProperty(value = "账单日")
    @ColumnWidth(15)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDate;

    @ExcelProperty(value = "银行订单号")
    @ColumnWidth(30)
    private String bankOrderNo;

    @ExcelProperty(value = "自助机订单号")
    @ColumnWidth(35)
    private String appOrderNo;

    @ExcelProperty(value = "HIS交易时间")
    @ColumnWidth(25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime hisTradeTime;

    @ExcelProperty(value = "自助机交易时间")
    @ColumnWidth(25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appTradeTime;

    @ExcelProperty(value = "HIS交易金额")
    @ColumnWidth(12)
    private BigDecimal hisTradeAmount;

    @ExcelProperty(value = "自助机交易金额")
    @ColumnWidth(12)
    private BigDecimal appTradeAmount;

    @ExcelProperty(value = "交易类型")
    @ColumnWidth(12)
    private String tradeType;

    @ExcelProperty(value = "患者ID")
    @ColumnWidth(20)
    private String patientId;

    @ExcelProperty(value = "患者姓名")
    @ColumnWidth(20)
    private String patientName;

    @ExcelProperty(value = "业务类型")
    @ColumnWidth(12)
    private String businessType;

    /**
     * 收款员
     */
    @ExcelProperty(value = "收款员")
    @ColumnWidth(20)
    private String cashier;

    @ExcelProperty(value = "结果", converter = ResultConverter.class)
    @ColumnWidth(15)
    private Integer result;

    public GuoguangAtmReconciliationDetail(LocalDate billDate, BillDetail appOrder) {
        this.id = billDate.format(DateTimeFormatter.BASIC_ISO_DATE) + IdUtil.fastSimpleUUID();
        this.billDate = billDate;
        this.appTradeTime = appOrder.getTradeTime();
        this.bankOrderNo = appOrder.getBankSerial();
        this.result = RESULT_APP_SURPLUS;
        if (appOrder.isPay()) {
            this.appOrderNo = appOrder.getOutTradeNo();
            this.appTradeAmount = appOrder.getTradeAmount();
            this.tradeType = TRADE_TYPE_PAY;
        } else {
            this.appOrderNo = appOrder.getOutTradeNo();
            this.appTradeAmount = appOrder.getTradeAmount().negate();
            this.tradeType = TRADE_TYPE_REFUND;
        }
    }

    public GuoguangAtmReconciliationDetail(LocalDate billDate, HisSelfPayOrder hisOrder) {
        this.id = billDate.format(DateTimeFormatter.BASIC_ISO_DATE) + IdUtil.fastSimpleUUID();
        this.billDate = billDate;
        this.hisTradeTime = hisOrder.getTradeTime();
        this.hisTradeAmount = hisOrder.getTradeAmount();
        this.patientId = hisOrder.getPatientId();
        this.patientName = hisOrder.getPatientName();
        this.businessType = hisOrder.getRemark();
        this.cashier = hisOrder.getCashier();
        this.result = RESULT_HIS_SURPLUS;
        if (hisOrder.isPayment()) {
            this.appOrderNo = hisOrder.getBankOrderNo();
            this.tradeType = TRADE_TYPE_PAY;
        } else {
            this.appOrderNo = hisOrder.getBankOrderNo();
            this.tradeType = TRADE_TYPE_REFUND;
        }
    }

    public static class ResultConverter implements Converter<Integer> {

        @Override
        public WriteCellData<?> convertToExcelData(Integer resultValue, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String resultDescription = switch (resultValue) {
                case RESULT_BALANCED -> "平账";
                case RESULT_APP_SURPLUS -> "自助机多";
                case RESULT_HIS_SURPLUS -> "HIS多";
                case RESULT_AMOUNT_MISMATCH -> "金额不一致";
                default -> "未知";
            };
            return new WriteCellData<>(resultDescription);
        }
    }
}