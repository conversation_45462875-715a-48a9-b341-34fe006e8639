package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import space.lzhq.ph.common.ServiceType;

import java.io.Serial;
import java.util.Date;

/**
 * 支付宝支付对象 alipay_payment
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AlipayPayment extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型", dictType = "service_type")
    private Integer type;

    /**
     * openid
     */
    @Excel(name = "支付宝用户ID")
    private String openid;

    /**
     * 患者ID
     */
    @Excel(name = "患者索引号")
    private String patientId;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 商户订单号
     */
    @Excel(name = "掌医订单号")
    private String outTradeNo;

    /**
     * 支付宝订单号
     */
    @Excel(name = "支付宝订单号")
    private String tradeNo;

    /**
     * 支付宝交易状态描述
     */
    @Excel(name = "支付宝交易状态描述")
    private String tradeStatus;

    /**
     * 结算单号
     */
    @Excel(name = "结算单号")
    private String settlementIds;

    /**
     * HIS收据号
     */
    @Excel(name = "HIS收据号")
    private String hisReceiptNo;

    /**
     * HIS交易状态描述
     */
    @Excel(name = "HIS交易状态描述")
    private String hisTradeStatus;

    /**
     * 充值到账时间
     */
    @Excel(name = "充值到账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date hisSuccessTime;

    /**
     * 订单金额/分
     */
    @Excel(name = "订单金额/分", cellType = Excel.ColumnType.NUMERIC)
    private Long totalAmount;

    /**
     * 已退款/分
     */
    @Excel(name = "已退费/分", cellType = Excel.ColumnType.NUMERIC)
    private Long exrefund;

    /**
     * 未退款/分
     */
    @Excel(name = "未退费/分", cellType = Excel.ColumnType.NUMERIC)
    private Long unrefund;

    /**
     * 手动充值状态
     */
    @Excel(name = "手动充值状态", dictType = "manual_state")
    private Integer manualRechargeState;

    public AlipayPayment() {
    }

    public AlipayPayment(Patient patient, ServiceType serviceType, String outTradeNo, Long totalAmount, String remark) {
        this.openid = patient.getOpenId();
        this.patientId = patient.getPatientNo();
        this.jzCardNo = patient.getJzCardNo();
        this.zhuyuanNo = patient.getZhuyuanNo();
        this.idCardNo = patient.getIdCardNo();
        this.setCreateTime(new Date());
        this.type = serviceType.getCode();
        this.outTradeNo = outTradeNo;
        this.totalAmount = totalAmount;
        this.tradeNo = "";
        this.tradeStatus = "";
        this.exrefund = 0L;
        this.unrefund = totalAmount;
        this.setRemark(remark);
        this.hisReceiptNo = "";
        this.hisSuccessTime = null;
        this.hisTradeStatus = "";
        this.manualRechargeState = Constants.MANUAL_INIT;
    }


    public boolean isMenzhen() {
        return Integer.valueOf(ServiceType.MZ.getCode()).equals(getType());
    }

    public boolean isZhuyuan() {
        return Integer.valueOf(ServiceType.ZY.getCode()).equals(getType());
    }

    public boolean isZhenjian() {
        return Integer.valueOf(ServiceType.ZJ.getCode()).equals(getType());
    }

}
