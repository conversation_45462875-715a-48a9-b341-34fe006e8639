package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@ExcelIgnoreUnannotated
@TableName(value = "psc_order_score_summary")
public class PscOrderScoreSummary implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = -8091171274047649385L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate date;

    @ExcelProperty(value = "工号")
    @ColumnWidth(15)
    private String carerEmployeeNo;

    @ExcelProperty(value = "姓名")
    @ColumnWidth(15)
    private String carerName;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String carerMobile;

    @ExcelProperty(value = {"评价人次", "非常不满意"})
    private Integer score1;

    @ExcelProperty(value = {"评价人次", "不满意"})
    private Integer score2;

    @ExcelProperty(value = {"评价人次", "一般"})
    private Integer score3;

    @ExcelProperty(value = {"评价人次", "满意"})
    private Integer score4;

    @ExcelProperty(value = {"评价人次", "非常满意"})
    private Integer score5;

    @ExcelProperty(value = "合计")
    private Integer total;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCarerEmployeeNo() {
        return carerEmployeeNo;
    }

    public void setCarerEmployeeNo(String carerEmployeeNo) {
        this.carerEmployeeNo = carerEmployeeNo;
    }

    public String getCarerName() {
        return carerName;
    }

    public void setCarerName(String carerName) {
        this.carerName = carerName;
    }

    public String getCarerMobile() {
        return carerMobile;
    }

    public void setCarerMobile(String carerMobile) {
        this.carerMobile = carerMobile;
    }

    public Integer getScore1() {
        return score1;
    }

    public void setScore1(Integer score1) {
        this.score1 = score1;
    }

    public Integer getScore2() {
        return score2;
    }

    public void setScore2(Integer score2) {
        this.score2 = score2;
    }

    public Integer getScore3() {
        return score3;
    }

    public void setScore3(Integer score3) {
        this.score3 = score3;
    }

    public Integer getScore4() {
        return score4;
    }

    public void setScore4(Integer score4) {
        this.score4 = score4;
    }

    public Integer getScore5() {
        return score5;
    }

    public void setScore5(Integer score5) {
        this.score5 = score5;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PscOrderScoreSummary that = (PscOrderScoreSummary) o;
        return Objects.equals(id, that.id) && Objects.equals(date, that.date) && Objects.equals(carerEmployeeNo, that.carerEmployeeNo) && Objects.equals(carerName, that.carerName) && Objects.equals(carerMobile, that.carerMobile) && Objects.equals(score1, that.score1) && Objects.equals(score2, that.score2) && Objects.equals(score3, that.score3) && Objects.equals(score4, that.score4) && Objects.equals(score5, that.score5) && Objects.equals(total, that.total);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, date, carerEmployeeNo, carerName, carerMobile, score1, score2, score3, score4, score5, total);
    }

    public static PscOrderScoreSummary fromSummariesByCarer(List<PscOrderScoreSummary> summaries) {
        if (summaries == null || summaries.isEmpty()) {
            return null;
        }

        int score1 = 0;
        int score2 = 0;
        int score3 = 0;
        int score4 = 0;
        int score5 = 0;
        int total = 0;

        for (PscOrderScoreSummary summary : summaries) {
            score1 += summary.getScore1();
            score2 += summary.getScore2();
            score3 += summary.getScore3();
            score4 += summary.getScore4();
            score5 += summary.getScore5();
            total += summary.getTotal();
        }

        PscOrderScoreSummary result = new PscOrderScoreSummary();
        result.setCarerEmployeeNo(summaries.get(0).getCarerEmployeeNo());
        result.setCarerName(summaries.get(0).getCarerName());
        result.setCarerMobile(summaries.get(0).getCarerMobile());
        result.setScore1(score1);
        result.setScore2(score2);
        result.setScore3(score3);
        result.setScore4(score4);
        result.setScore5(score5);
        result.setTotal(total);
        return result;
    }
}
