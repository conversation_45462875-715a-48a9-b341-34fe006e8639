package space.lzhq.ph.webservice.impl;

import jakarta.annotation.PreDestroy;
import jakarta.jws.WebService;
import org.dom4j.Element;
import org.dromara.hutool.core.data.id.IdUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.OutpatientStopClinic;
import space.lzhq.ph.service.OutpatientStopClinicService;
import space.lzhq.ph.utils.XmlUtils;
import space.lzhq.ph.webservice.BSXmlWsEntryClass;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@WebService(
        serviceName = "BSXmlWsEntryClassService",
        portName = "BSXmlWsEntryClassPort",
        targetNamespace = "http://ws.access.hai/",
        endpointInterface = "space.lzhq.ph.webservice.BSXmlWsEntryClass"
)
public class BSXmlWsEntryClassImpl implements BSXmlWsEntryClass {

    private static final Logger log = LoggerFactory.getLogger(BSXmlWsEntryClassImpl.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OutpatientStopClinicService outpatientStopClinicService;
    private final ExecutorService subscribeMessageExecutorService;


    @Autowired
    public BSXmlWsEntryClassImpl(OutpatientStopClinicService outpatientStopClinicService, @Qualifier("subscribeMessageExecutorService") ExecutorService subscribeMessageExecutorService) {
        this.outpatientStopClinicService = outpatientStopClinicService;
        this.subscribeMessageExecutorService = subscribeMessageExecutorService;
    }

    @PreDestroy
    public void destroy() {
        if (subscribeMessageExecutorService != null) {
            subscribeMessageExecutorService.shutdown();
            try {
                if (!subscribeMessageExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    subscribeMessageExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                subscribeMessageExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    public String invoke(String service, String urid, String pwd, String parameter) {
        String traceId = IdUtil.simpleUUID();
        log.info("SOAP#{} service: {}, urid: {}, parameter: {}", traceId, service, urid, parameter);

        if (!BSXmlWsEntryClass.AUTH_URID.equals(urid) || !BSXmlWsEntryClass.AUTH_PASSWORD.equals(pwd)) {
            log.info("SOAP#{} service: {}, urid: {} 认证失败", traceId, service, urid);
            return XmlUtils.generateResponseXml(false, "认证失败");
        }

        try {
            if ("OutAppointSend_Stop".equals(service)) {
                return handleOutpatientStopClinic(traceId, parameter);
            }
            log.info("SOAP#{} service: {}, 未知的服务类型", traceId, service);
            return XmlUtils.generateResponseXml(false, "未知的服务类型: " + service);
        } catch (Exception e) {
            log.error("SOAP#{} service: {}, 发生错误: ", traceId, service, e);
            return XmlUtils.generateResponseXml(false, "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 处理门诊停诊消息
     */
    private String handleOutpatientStopClinic(String traceId, String parameter) {
        try {
            Element visit = XmlUtils.parseRequestXml(parameter);

            OutpatientStopClinic stopClinic = parseOutpatientStopClinic(visit);
            if (outpatientStopClinicService.existsByAppointmentId(stopClinic.getAppointmentId())) {
                log.info("SOAP#{} 消息已存在", traceId);
                return XmlUtils.generateResponseXml(true, "消息已存在");
            }

            boolean success = outpatientStopClinicService.save(stopClinic);
            if (success) {
                log.info("SOAP#{} 消息已保存", traceId);
                // 发送订阅消息
                CompletableFuture.runAsync(() -> {
                    try {
                        outpatientStopClinicService.sendSubscribeMessage(stopClinic);
                    } catch (Exception e) {
                        log.error("SOAP#{} 发送订阅消息失败: ", traceId, e);
                    }
                }, subscribeMessageExecutorService);
            } else {
                log.info("SOAP#{} 消息保存失败", traceId);
            }
            return XmlUtils.generateResponseXml(success, success ? "成功" : "保存失败");
        } catch (Exception e) {
            log.error("SOAP#{} 发生错误: ", traceId, e);
            return XmlUtils.generateResponseXml(false, "处理门诊停诊消息失败: " + e.getMessage());
        }
    }

    @NotNull
    private static OutpatientStopClinic parseOutpatientStopClinic(Element visit) {
        OutpatientStopClinic stopClinic = new OutpatientStopClinic();
        stopClinic.setScheduleMark(visit.elementTextTrim("ScheduleMark"));
        stopClinic.setAppointmentId(visit.elementTextTrim("AppointmentId"));
        stopClinic.setSourcePatientId(visit.elementTextTrim("SourcePatientId"));
        stopClinic.setRegisteredDateTime(LocalDateTime.parse(visit.elementTextTrim("RegisteredDateTime"), DATE_TIME_FORMATTER));
        stopClinic.setAppointmentDateTime(LocalDateTime.parse(visit.elementTextTrim("AppointmentDateTime"), DATE_TIME_FORMATTER));
        stopClinic.setIdCard(visit.elementTextTrim("IdCard"));
        stopClinic.setName(visit.elementTextTrim("Name"));
        stopClinic.setRegisteredDept(visit.elementTextTrim("RegisteredDept"));
        stopClinic.setRegisteredDeptName(visit.elementTextTrim("RegisteredDeptNane"));
        stopClinic.setRegisteredDoctor(visit.elementTextTrim("RegisteredDcotor"));
        stopClinic.setRegisteredDoctorName(visit.elementTextTrim("RegisteredDcotorName"));
        stopClinic.setOperatingName(visit.elementTextTrim("OperatingName"));
        stopClinic.setOperatingDateTime(LocalDateTime.parse(visit.elementTextTrim("OperatingDateTime"), DATE_TIME_FORMATTER));
        stopClinic.setMessageContent(visit.elementTextTrim("messageContent"));
        stopClinic.setCreateTime(LocalDateTime.now());
        stopClinic.setIsRead(false);
        return stopClinic;
    }
}
