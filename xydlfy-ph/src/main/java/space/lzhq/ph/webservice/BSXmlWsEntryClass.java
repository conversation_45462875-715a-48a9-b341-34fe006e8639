package space.lzhq.ph.webservice;

import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebService;

@WebService(targetNamespace = "http://ws.access.hai/")
public interface BSXmlWsEntryClass {

    /**
     * 用户ID凭证
     */
    String AUTH_URID = "YQTL";

    /**
     * 用户密码凭证
     */
    String AUTH_PASSWORD = "ysrNyK62ton7b4PN";

    @WebMethod
    String invoke(
            @WebParam(name = "service") String service,
            @WebParam(name = "urid") String urid,
            @WebParam(name = "pwd") String pwd,
            @WebParam(name = "parameter") String parameter
    );

} 