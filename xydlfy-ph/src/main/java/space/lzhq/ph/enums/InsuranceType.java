package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 医保类型枚举
 * 定义系统支持的各种医保类型
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum InsuranceType {
    CITY_WORKER("乌鲁木齐市职工医保"),
    CITY_RESIDENT("乌鲁木齐市居民医保"),
    DISTRICT("区医保（含铁路医保）"),
    CORP("兵团医保（兵直、十一师、十二师）");

    @Getter
    @Schema(description = "医保类型名称", example = "乌鲁木齐市职工医保")
    private final String description;

    InsuranceType(String description) {
        this.description = description;
    }

    @Schema(description = "医保类型编码", example = "CITY_WORKER")
    public String getCode() {
        return name();
    }
    
} 