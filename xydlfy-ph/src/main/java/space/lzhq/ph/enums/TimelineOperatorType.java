package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 时间线操作者类型枚举
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TimelineOperatorType {

    APPLICANT("申请人"),
    AGENT("代办人"),
    SYSTEM("系统"),
    REVIEWER("审核员"),
    ADMIN("管理员");

    @Getter
    @Schema(description = "操作者描述", example = "代办人")
    private final String description;

    TimelineOperatorType(String description) {
        this.description = description;
    }

    @Schema(description = "操作者编码", example = "AGENT")
    public String getCode() {
        return name();
    }

}