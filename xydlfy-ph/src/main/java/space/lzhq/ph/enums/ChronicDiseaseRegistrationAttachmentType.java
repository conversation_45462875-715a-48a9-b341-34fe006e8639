package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ChronicDiseaseRegistrationAttachmentType {
    ID_CARD("身份证"),
    INSURANCE_CARD("医保卡"),
    DOCUMENT("认定资料");

    @Getter
    @Schema(description = "名称", example = "身份证")
    private final String description;

    ChronicDiseaseRegistrationAttachmentType(String description) {
        this.description = description;
    }

    @Schema(description = "编码", example = "ID_CARD")
    public String getCode() {
        return name();
    }

}