package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ChronicDiseaseRegistrationStatus {
    DRAFT("草稿（待提交）"),
    SUBMITTED("已提交（待审核）"),
    APPROVED("审核通过"),
    REJECTED("审核驳回（待整改）");

    @Getter
    @Schema(description = "名称", example = "身份证")
    private final String description;

    ChronicDiseaseRegistrationStatus(String description) {
        this.description = description;
    }

    @Schema(description = "编码", example = "DRAFT")
    public String getCode() {
        return name();
    }

    /**
     * 判断当前状态是否允许申请人或代理人编辑记录
     *
     * @return 如果状态为DRAFT（草稿）或REJECTED（审核驳回）时返回true，其他状态返回false
     */
    @Schema(description = "是否可编辑", example = "true")
    public boolean isEditableByApplicantOrAgent() {
        return this == DRAFT || this == REJECTED;
    }

    /**
     * 判断当前状态是否允许审批
     *
     * @return 如果状态为SUBMITTED（已提交）时返回true，其他状态返回false
     */
    @Schema(description = "是否可审批", example = "true")
    public boolean canApprove() {
        return this == SUBMITTED;
    }

    /**
     * 判断当前状态是否允许删除
     *
     * @return 如果状态为DRAFT（草稿）时返回true，其他状态返回false
     */
    @Schema(description = "是否可删除", example = "true")
    public boolean canDelete() {
        return this == DRAFT;
    }

    /**
     * 判断当前状态是否为待审批状态
     *
     * @return 如果状态为SUBMITTED（已提交）时返回true，其他状态返回false
     */
    @Schema(description = "是否为待审批状态", example = "true")
    public boolean isPendingApproval() {
        return this == SUBMITTED;
    }

}