<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('预约记录列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>操作类型：</label>
                            <select name="operationType" th:with="type=${@dict.getType('reservation_operation_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <label>操作时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginOperationTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endOperationTime]"/>
                        </li>
                        <li>
                            <label>身份证号：</label>
                            <input type="text" name="idCardNo"/>
                        </li>
                        <li>
                            <label>就诊卡号：</label>
                            <input type="text" name="jzCardNo"/>
                        </li>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>手机号：</label>
                            <input type="text" name="mobile"/>
                        </li>
                        <li>
                            <label>就诊日期：</label>
                            <input type="text" class="time-input" placeholder="请选择就诊日期" name="jzDate"/>
                        </li>
                        <li>
                            <label>科室编码：</label>
                            <input type="text" name="departmentCode"/>
                        </li>
                        <li>
                            <label>科室名称：</label>
                            <input type="text" name="departmentName"/>
                        </li>
                        <li>
                            <label>医生编码：</label>
                            <input type="text" name="doctorCode"/>
                        </li>
                        <li>
                            <label>预约号：</label>
                            <input type="text" name="reservationNumber"/>
                        </li>
                        <li>
                            <label>操作结果：</label>
                            <input type="text" name="operationResult"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:reservation:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var operationTypeDatas = [[${@dict.getType('reservation_operation_type')}]];
    var operationResultDatas = [[${@dict.getType('reservation_operation_result')}]];
    var prefix = ctx + "ph/reservation";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "预约记录",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'operationType',
                    title: '操作类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(operationTypeDatas, value);
                    }
                },
                {
                    field: 'operationTime',
                    title: '操作时间'
                },
                {
                    field: 'idCardNo',
                    title: '身份证号'
                },
                {
                    field: 'jzCardNo',
                    title: '就诊卡号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'mobile',
                    title: '手机号'
                },
                {
                    field: 'jzDate',
                    title: '就诊日期'
                },
                {
                    field: 'jzTime',
                    title: '就诊时间'
                },
                {
                    field: 'departmentCode',
                    title: '科室编码'
                },
                {
                    field: 'departmentName',
                    title: '科室名称'
                },
                {
                    field: 'departmentLocation',
                    title: '科室位置'
                },
                {
                    field: 'doctorCode',
                    title: '医生编码'
                },
                {
                    field: 'visitNumber',
                    title: '顺序号'
                },
                {
                    field: 'reservationNumber',
                    title: '预约号'
                },
                {
                    field: 'operationResult',
                    title: '操作结果',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(operationResultDatas, value);
                    }
                },
                {
                    field: 'operationDesc',
                    title: '操作描述'
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>
