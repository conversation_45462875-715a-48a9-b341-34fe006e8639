<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <title>慢病病种列表</title>
    <th:block th:insert="~{include :: header('慢病病种列表')}" />
    <style>
      .insurance-status {
        display: inline-block;
        width: 60px;
        text-align: center;
        padding: 3px 0;
        border-radius: 3px;
      }
      .insurance-support {
        background-color: #337ab7;
        color: white;
      }
      .insurance-unsupport {
        background-color: #f0f0f0;
        color: #333;
      }
      .btn-operation {
        display: inline-block;
        width: 60px;
        text-align: center;
        padding: 3px 0;
        border-radius: 3px;
        color: white;
        margin: 2px;
        border: none;
        cursor: pointer;
      }
      .btn-edit {
        background-color: #5cb85c;
      }
      .btn-delete {
        background-color: #d9534f;
      }
      .btn-operation i {
        margin-right: 3px;
      }
      
      /* 美化搜索和重置按钮 */
      .btn-custom {
        border-radius: 4px;
        border: none;
        padding: 6px 15px;
        transition: all 0.3s;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        font-weight: 500;
      }
      
      .btn-search {
        background-color: #1890ff;
        color: white;
      }
      
      .btn-search:hover {
        background-color: #40a9ff;
      }
      
      .btn-reset {
        background-color: #faad14;
        color: white;
      }
      
      .btn-reset:hover {
        background-color: #ffc53d;
      }
      
      /* 工具栏按钮美化 */
      .btn-toolbar {
        padding: 8px 16px;
        margin-right: 10px;
        border-radius: 4px;
        border: none;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        font-weight: 500;
        transition: all 0.3s;
      }
      
      .btn-add {
        background-color: #52c41a;
        color: white;
      }
      
      .btn-add:hover {
        background-color: #73d13d;
      }
      
      .btn-modify {
        background-color: #2f54eb;
        color: white;
      }
      
      .btn-modify:hover {
        background-color: #597ef7;
      }
      
      .btn-remove {
        background-color: #f5222d;
        color: white;
      }
      
      .btn-remove:hover {
        background-color: #ff4d4f;
      }
      
      .btn-export {
        background-color: #fa8c16;
        color: white;
      }
      
      .btn-export:hover {
        background-color: #ffa940;
      }
      
      /* 调整搜索区域样式 */
      .search-collapse {
        padding: 15px;
        background: #f9f9f9;
        border-radius: 4px;
        margin-bottom: 15px;
      }
      
      .select-list ul li {
        margin-bottom: 5px;
      }
      
      /* 调整按钮内的图标间距 */
      .btn-custom i, .btn-toolbar i {
        margin-right: 5px;
      }
    </style>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label class="control-label" for="name">病种名称：</label>
                  <input type="text" name="name" id="name" />
                </li>
                <li>
                  <button
                    type="button"
                    class="btn btn-custom btn-search"
                    onclick="$.table.search()"
                  >
                    <i class="fa fa-search"></i>&nbsp;搜索
                  </button>
                  <button
                    type="button"
                    class="btn btn-custom btn-reset"
                    onclick="$.form.reset()"
                  >
                    <i class="fa fa-refresh"></i>&nbsp;重置
                  </button>
                </li>
              </ul>
            </div>
          </form>
        </div>

        <fieldset class="btn-group-sm" id="toolbar">
          <button
            type="button"
            class="btn btn-toolbar btn-add"
            onclick="$.operate.add()"
            shiro:hasPermission="ph:chronic-disease:add"
          >
            <i class="fa fa-plus"></i> 添加
          </button>
          <button
            type="button"
            class="btn btn-toolbar btn-modify single disabled"
            onclick="$.operate.edit()"
            shiro:hasPermission="ph:chronic-disease:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </button>
          <button
            type="button"
            class="btn btn-toolbar btn-remove multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="ph:chronic-disease:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </button>
          <button
            type="button"
            class="btn btn-toolbar btn-export"
            onclick="$.table.exportExcel()"
            shiro:hasPermission="ph:chronic-disease:export"
          >
            <i class="fa fa-download"></i> 导出
          </button>
        </fieldset>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:insert="~{include :: footer}" />
    <script th:inline="javascript">
      /*<![CDATA[*/
      var editFlag = /*[[${@permission.hasPermi('ph:chronic-disease:edit')}]]*/ false;
      var removeFlag = /*[[${@permission.hasPermi('ph:chronic-disease:remove')}]]*/ false;
      var prefix = ctx + "ph/chronic-disease";

      $(function () {
        var options = {
          url: prefix + "/list",
          createUrl: prefix + "/add",
          updateUrl: prefix + "/edit/{id}",
          removeUrl: prefix + "/remove",
          exportUrl: prefix + "/export",
          modalName: "慢病病种",
          showSearch: false,
          showColumns: false,
          showToggle: false,
          columns: [
            {
              checkbox: true,
            },
            {
              field: "id",
              title: "主键",
              visible: false,
            },
            {
              field: "name",
              title: "病种名称",
            },
            {
              field: "insuranceCityWorker",
              title: "市职工医保",
              formatter: function (value) {
                return value == 1
                  ? '<span class="insurance-status insurance-support">支持</span>'
                  : '<span class="insurance-status insurance-unsupport">不支持</span>';
              },
            },
            {
              field: "insuranceCityResident",
              title: "市居民医保",
              formatter: function (value) {
                return value == 1
                  ? '<span class="insurance-status insurance-support">支持</span>'
                  : '<span class="insurance-status insurance-unsupport">不支持</span>';
              },
            },
            {
              field: "insuranceDistrict",
              title: "区医保",
              formatter: function (value) {
                return value == 1
                  ? '<span class="insurance-status insurance-support">支持</span>'
                  : '<span class="insurance-status insurance-unsupport">不支持</span>';
              },
            },
            {
              field: "insuranceCorp",
              title: "兵团医保",
              formatter: function (value) {
                return value == 1
                  ? '<span class="insurance-status insurance-support">支持</span>'
                  : '<span class="insurance-status insurance-unsupport">不支持</span>';
              },
            },
            {
              field: "expertNames",
              title: "鉴定专家",
              formatter: function (value, row) {
                if (value && value.length > 0) {
                  return value.join("、");
                }
                return '<span style="color: #999;">暂无</span>';
              },
            },
            {
              title: "操作",
              align: "center",
              formatter: function (value, row, index) {
                var actions = [];
                actions.push(
                  '<button type="button" class="btn-operation btn-edit ' +
                    editFlag +
                    '" onclick="$.operate.edit(\'' +
                    row.id +
                    '\')"><i class="fa fa-edit"></i>编辑</button>'
                );
                actions.push(
                  '<button type="button" class="btn-operation btn-delete ' +
                    removeFlag +
                    '" onclick="$.operate.remove(\'' +
                    row.id +
                    '\')"><i class="fa fa-remove"></i>删除</button>'
                );
                return actions.join("");
              },
            },
          ],
        };
        $.table.init(options);
      });
      /*]]>*/
    </script>
  </body>
</html>
