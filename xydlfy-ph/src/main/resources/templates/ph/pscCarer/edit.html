<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改陪检')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-pscCarer-edit" th:object="${pscCarer}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">工号：</label>
            <div class="col-sm-8">
                <input name="employeeNo" th:field="*{employeeNo}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">姓名：</label>
            <div class="col-sm-8">
                <input name="name" th:field="*{name}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">手机号：</label>
            <div class="col-sm-8">
                <input name="mobile" th:field="*{mobile}" class="form-control" type="text" required>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/pscCarer";
    $("#form-pscCarer-edit").validate({
        focusCleanup: true,
        rules: {
            employeeNo: {
                required: true,
                remote: {
                    url: prefix + "/isEmployeeNoUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        id: function () {
                            return $("input[name='id']").val();
                        },
                        employeeNo: function () {
                            return $("input[name='employeeNo']").val();
                        }
                    }
                }
            },
            mobile: {
                isPhone: true,
                remote: {
                    url: prefix + "/isMobileUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        id: function () {
                            return $("input[name='id']").val();
                        },
                        mobile: function () {
                            return $("input[name='mobile']").val();
                        }
                    }
                }
            }
        },
        messages: {
            employeeNo: {
                remote: "工号已存在"
            },
            mobile: {
                isPhone: "请输入正确的手机号",
                remote: "手机号已存在"
            }
        }
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-pscCarer-edit').serialize());
        }
    }
</script>
</body>
</html>
