<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('错单'+${billDate})}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/check";
    var id = [[${id}]];

    $(function () {
        $.table.init({
            url: prefix + "/diffOrders/" + id,
            modalName: "错单",
            pagination: false,
            columns: [
                {
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'businessType',
                    title: '业务类型'
                },
                {
                    field: 'tradeType', 
                    title: '交易类型'
                },
                {
                    field: 'patientId',
                    title: '患者索引号'
                },
                {
                    field: 'patientName',
                    title: '患者姓名'
                },
                {
                    field: 'bankOrderNo',
                    title: '银行订单号'
                },
                {
                    field: 'appOrderNo',
                    title: '应用订单号'
                },
                {
                    field: 'appTradeTime',
                    title: '应用交易时间'
                },
                {
                    field: 'hisTradeTime',
                    title: 'HIS交易时间'
                },
                {
                    field: 'appTradeAmount',
                    title: '应用交易金额'
                },
                {
                    field: 'hisTradeAmount',
                    title: 'HIS交易金额'
                }
            ]
        });
    });
</script>
</body>
</html>