<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增对账')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-check-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信支付总金额：</label>
                <div class="col-sm-8">
                    <input name="wxPayAmount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">HIS充值总金额：</label>
                <div class="col-sm-8">
                    <input name="hisPayAmount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付差额：</label>
                <div class="col-sm-8">
                    <input name="diffPayAmount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付平账？：</label>
                <div class="col-sm-8">
                    <select name="payBalanced" class="form-control m-b" th:with="type=${@dict.getType('yes_no')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信退款总金额：</label>
                <div class="col-sm-8">
                    <input name="wxRefundAmount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">HIS退款总金额：</label>
                <div class="col-sm-8">
                    <input name="hisRefundAmount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">退款差额：</label>
                <div class="col-sm-8">
                    <input name="diffRefundAmount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">退款平账？：</label>
                <div class="col-sm-8">
                    <input name="refundBalanced" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信净流入：</label>
                <div class="col-sm-8">
                    <input name="wxNetIn" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">HIS净流入：</label>
                <div class="col-sm-8">
                    <input name="hisNetIn" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "ph/check"
        $("#form-check-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-check-add').serialize());
            }
        }
    </script>
</body>
</html>