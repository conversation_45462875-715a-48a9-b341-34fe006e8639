<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改材料')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-material-edit" th:object="${material}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">名称：</label>
            <div class="col-sm-8">
                <input name="name" th:field="*{name}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">拼音码：</label>
            <div class="col-sm-8">
                <input name="pinyinCode" th:field="*{pinyinCode}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">类型编码：</label>
            <div class="col-sm-8">
                <input name="typeCode" th:field="*{typeCode}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">类型：</label>
            <div class="col-sm-8">
                <input name="typeName" th:field="*{typeName}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">单位：</label>
            <div class="col-sm-8">
                <input name="unit" th:field="*{unit}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">价格：</label>
            <div class="col-sm-8">
                <input name="price" th:field="*{price}" class="form-control" type="text" required>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/material";
    $("#form-material-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-material-edit').serialize());
        }
    }
</script>
</body>
</html>