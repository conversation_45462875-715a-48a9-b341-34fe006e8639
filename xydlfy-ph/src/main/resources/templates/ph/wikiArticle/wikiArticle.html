<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('文章列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>栏目：</label>
                            <select name="categoryId">
                                <option value="">所有</option>
                                <!--/*@thymesVar id="categories" type="java.util.List<space.lzhq.ph.domain.WikiCategory>"*/-->
                                <option th:each="category:${categories}"
                                        th:value="${category.id}"
                                        th:text="${category.name}"
                                ></option>
                            </select>
                        </li>
                        <li>
                            <label>标题：</label>
                            <input type="text" name="title"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="ph:wikiArticle:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()"
               shiro:hasPermission="ph:wikiArticle:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="ph:wikiArticle:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:wikiArticle:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:wikiArticle:edit')}]];
    var removeFlag = [[${@permission.hasPermi('ph:wikiArticle:remove')}]];
    var prefix = ctx + "ph/wikiArticle";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "文章",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'categoryName',
                    title: '栏目'
                },
                {
                    field: 'title',
                    title: '标题'
                },
                {
                    field: 'coverImage',
                    title: '封面',
                    formatter: function (value, row, index) {
                        return value ? '<img src="' + value + '" style="width: 48px;height: 48px;"/>' : '';
                    }
                },
                {
                    field: 'videoUrl',
                    title: '视频',
                    formatter: function (value, row, index) {
                        return value ? `<a href="${value}" target="_blank">播放</a>` : '';
                    }
                },
                {
                    field: 'updateTime',
                    title: '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        const actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>