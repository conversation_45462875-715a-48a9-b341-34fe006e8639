<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改文章')}"/>
    <th:block th:insert="~{include :: select2-css}"/>
    <th:block th:insert="~{include :: summernote-css}"/>
    <th:block th:insert="~{include :: bootstrap-fileinput-css}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated">
    <form class="form-horizontal m" id="form-wikiArticle-edit" th:object="${article}">
        <input name="id" th:field="*{id}" type="hidden">
        <input name="categoryName" id="categoryName" th:field="*{categoryName}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">栏目：</label>
            <div class="col-sm-8">
                <select id="categoryId" name="categoryId" class="form-control m-b" required>
                    <option value="">请选择栏目</option>
                    <!--/*@thymesVar id="categories" type="java.util.List<space.lzhq.ph.domain.WikiCategory>"*/-->
                    <option th:each="category:${categories}"
                            th:value="${category.id}"
                            th:text="${category.name}"
                            th:field="*{categoryId}"
                    ></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">标题：</label>
            <div class="col-sm-8">
                <input name="title" th:field="*{title}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">摘要：</label>
            <div class="col-sm-8">
                <textarea id="summary" name="summary" th:field="*{summary}" maxlength="500" class="form-control"
                          type="text" required></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">排序号：</label>
            <div class="col-sm-8">
                <input name="sortNo" th:field="*{sortNo}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">封面：</label>
            <div class="col-sm-8">
                <input type="hidden" name="coverImage" th:field="*{coverImage}">
                <div class="file-loading">
                    <input class="form-control file-upload" id="coverImage" name="file" type="file">
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">视频链接：</label>
            <div class="col-sm-8">
                <input name="videoUrl" th:field="*{videoUrl}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">内容：</label>
            <div class="col-sm-8">
                <input id="content" name="content" type="hidden" th:field="*{content}">
                <div id="contentEditor" class="summernote"></div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: select2-js}"/>
<th:block th:insert="~{include :: summernote-js}"/>
<th:block th:insert="~{include :: bootstrap-fileinput-js}"/>
<script type="text/javascript">
    var prefix = ctx + "ph/wikiArticle";

    $(function () {
        $('#categoryId').on('change.select2', function (e) {
            var selections = $(this).select2('data');
            console.log(selections)
            if (selections.length === 0) {
                $('#categoryName').val('');
            } else {
                $('#categoryName').val(selections[0]['text']);
            }
        });
        $('.summernote').summernote({
            placeholder: '请输入内容',
            height: 400,
            lang: 'zh-CN',
            followingToolbar: false,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['picture']],
                ['view', ['fullscreen', 'help']],
            ],
            callbacks: {
                onImageUpload: function (files) {
                    sendFile(files[0], this);
                }
            }
        });
        $('#contentEditor').summernote('code', $('#content').val());

        $("#form-wikiArticle-edit").validate({
            focusCleanup: true
        });
    });

    // 上传文件
    function sendFile(file, obj) {
        var data = new FormData();
        data.append("file", file);
        $.ajax({
            type: "POST",
            url: ctx + "common/upload",
            data: data,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $(obj).summernote('editor.insertImage', result.url, result.fileName);
                } else {
                    $.modal.alertError(result.msg);
                }
            },
            error: function (error) {
                $.modal.alertWarning("图片上传失败。");
            }
        });
    }

    function submitHandler() {
        if ($.validate.form()) {
            $("#content").val($('.summernote').summernote('code'));
            $.operate.saveTab(prefix + "/edit", $('#form-wikiArticle-edit').serialize());
        }
    }

    $(".file-upload").each(function (i) {
        var val = $("input[name='" + this.id + "']").val()
        $(this).fileinput({
            'uploadUrl': ctx + 'common/upload',
            initialPreviewAsData: true,
            initialPreview: val ? [val] : [],
            maxFileCount: 1,
            autoReplace: true,
            allowedFileTypes: ['image'],
            showUpload: false,
            browseOnZoneClick: true,
        }).on('filebatchselected', function (event, files) {
            $(this).fileinput("upload");
        }).on('fileuploaded', function (event, data, previewId, index) {
            $("input[name='" + event.currentTarget.id + "']").val(data.response.url)
        }).on('fileremoved', function (event, id, index) {
            $("input[name='" + event.currentTarget.id + "']").val('')
        })
        $(this).fileinput('_initFileActions');
    });
</script>
</body>
</html>