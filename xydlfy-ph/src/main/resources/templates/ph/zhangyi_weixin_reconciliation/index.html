<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('微信小程序')}"/>
    <style>
        th, td {
            font-weight: bold !important;
        }
        #formId label {
            width: auto;
            margin-right: 10px;
        }
        .dropdown-menu {
            min-width: 160px;
        }
        .table > tbody > tr > td {
            overflow: visible !important;
        }
        .fixed-table-body {
            min-height: 461px;
        }
        /* 固定列样式调整 */
        .fixed-table-container .fixed-table-body-columns {
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .fixed-column {
            background-color: #fff;
        }

        #bootstrap-table tbody td {
            font-family: Consolas, "Courier New", SFMono-Regular, -apple-system, "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
            font-weight: 500;
            font-size: 12px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>日期</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始日期"
                                   name="startDate" th:value="${startDate}"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束日期"
                                   name="endDate" th:value="${endDate}"/>
                        </li>
                        <li>
                            <label>平账？</label>
                            <select name="totalBalanced" th:with="type=${@dict.getType('yes_no')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                <i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var downloadWeixinBillFlag = [[${@permission.hasPermi('ph:zhangyi_weixin_reconciliation:downloadWeixinBill')}]];
    var downloadHisBillFlag = [[${@permission.hasPermi('ph:zhangyi_weixin_reconciliation:downloadHisBill')}]];
    var reconcileFlag = [[${@permission.hasPermi('ph:zhangyi_weixin_reconciliation:reconcile')}]];
    var downloadReconciliationDetailsFlag = [[${@permission.hasPermi('ph:zhangyi_weixin_reconciliation:downloadReconciliationDetails')}]];
    var downloadNotBalancedReconciliationDetailsFlag = [[${@permission.hasPermi('ph:zhangyi_weixin_reconciliation:downloadNotBalancedReconciliationDetails')}]];
    var balancedDicts = [[${@dict.getType('yes_no')}]];
    var prefix = ctx + "ph/zhangyi_weixin_reconciliation";
    $(function () {
        var options = {
            url: prefix + "/page",
            modalName: "微信小程序对账",
            showSearch: false,
            showColumns: false,
            showToggle: false,
            fixedColumns: true,
            fixedNumber: 0,
            fixedRightNumber: 1,
            columns: [
                [
                    {
                        field: 'date',
                        title: '日期',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                    },
                    {
                        title: '充值',
                        align: 'center',
                        valign: 'middle',
                        colspan: 4
                    },
                    {
                        title: '退款',
                        align: 'center',
                        valign: 'middle',
                        colspan: 4
                    },
                    {
                        title: '合计',
                        align: 'center',
                        valign: 'middle',
                        colspan: 4
                    },
                    {
                        title: '操作',
                        align: 'center',
                        valign: 'middle',
                        rowspan: 2,
                        width: 100,
                        fixed: 'right',
                        formatter: function (value, row, index) {
                            const actions = [];
                            actions.push('<div class="btn-group" style="width:100%">');
                            actions.push('<button type="button" class="btn btn-primary btn-xs dropdown-toggle" style="width:100%" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">');
                            actions.push('操作 <span class="caret"></span>');
                            actions.push('</button>');
                            actions.push('<ul class="dropdown-menu dropdown-menu-right">');
                            
                            if ([[${user.isAdmin()}]]) {
                                actions.push('<li><a class="' + reconcileFlag + '" href="javascript:void(0)" onclick="reconcile(\'' + row.date + '\')"><i class="fa fa-refresh"></i> 对账</a></li>');
                            }
                            actions.push('<li><a class="' + downloadWeixinBillFlag + '" href="javascript:void(0)" onclick="downloadWeixinBill(\'' + row.date + '\')"><i class="fa fa-download"></i> 下载微信账单</a></li>');
                            actions.push('<li><a class="' + downloadHisBillFlag + '" href="javascript:void(0)" onclick="downloadHisBill(\'' + row.date + '\')"><i class="fa fa-download"></i> 下载HIS账单</a></li>');
                            actions.push('<li><a class="' + downloadReconciliationDetailsFlag + '" href="javascript:void(0)" onclick="downloadReconciliationDetails(\'' + row.date + '\')"><i class="fa fa-download"></i> 下载对账明细</a></li>');

                            if (row.totalBalanced != 1) {
                                actions.push('<li><a class="' + downloadNotBalancedReconciliationDetailsFlag + '" href="javascript:void(0)" onclick="downloadNotBalancedReconciliationDetails(\'' + row.date + '\')"><i class="fa fa-download"></i> 下载错单明细</a></li>');
                            }

                            actions.push('</ul>');
                            actions.push('</div>');
                            return actions.join('');
                        }
                    }
                ],
                [
                    {
                        field: 'appPayAmount',
                        title: '掌医',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'hisPayAmount',
                        title: 'HIS',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'diffPayAmount',
                        title: '掌医-HIS',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'payBalanced',
                        title: '平账？',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(balancedDicts, value);
                        }
                    },
                    {
                        field: 'appRefundAmount',
                        title: '掌医',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'hisRefundAmount',
                        title: 'HIS',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'diffRefundAmount',
                        title: '掌医-HIS',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'refundBalanced',
                        title: '平账？',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(balancedDicts, value);
                        }
                    },
                    {
                        field: 'appTotalAmount',
                        title: '掌医',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'hisTotalAmount',
                        title: 'HIS',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'diffTotalAmount',
                        title: '掌医-HIS',
                        align: 'center',
                        valign: 'middle',
                        formatter: formatAmountCell
                    },
                    {
                        field: 'totalBalanced',
                        title: '平账？',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(balancedDicts, value);
                        }
                    }
                ]
            ]
        };
        $.table.init(options);

        handleDropdownDirection('.fixed-table-container .btn-group', 200);
    });

    function downloadWeixinBill(date) {
        window.open(prefix + '/downloadWeixinBill/' + date, "_blank");
    }

    function downloadHisBill(date) {
        window.open(prefix + '/downloadHisBill/' + date, "_blank");
    }

    function reconcile(date) {
        $.operate.post(prefix + '/reconcile/' + date, {});
    }

    function downloadReconciliationDetails(date) {
        window.open(prefix + '/downloadReconciliationDetails/' + date, "_blank");
    }

    function downloadNotBalancedReconciliationDetails(date) {
        window.open(prefix + '/downloadNotBalancedReconciliationDetails/' + date, "_blank");
    }
</script>
</html>