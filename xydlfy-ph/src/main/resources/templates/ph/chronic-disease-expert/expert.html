<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:insert="~{include :: header('慢病专家管理')}" />
    <title>慢病专家管理</title>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="expert-form">
            <div class="select-list">
              <ul>
                <li>
                  专家姓名：<input
                    type="text"
                    name="userName"
                    placeholder="请输入专家姓名"
                  />
                </li>
                <li>
                  手机号码：<input
                    type="text"
                    name="phonenumber"
                    placeholder="请输入手机号码"
                  />
                </li>
                <li>
                  登录名：<input
                    type="text"
                    name="loginName"
                    placeholder="请输入登录名"
                  />
                </li>
                <li>
                  用户状态：<select
                    name="status"
                    th:with="type=${@dict.getType('sys_normal_disable')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <button
                    type="button"
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                  >
                    <i class="fa fa-search"></i>&nbsp;搜索
                  </button>
                  <button
                    type="button"
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                  >
                    <i class="fa fa-refresh"></i>&nbsp;重置
                  </button>
                </li>
              </ul>
            </div>
          </form>
        </div>

        <fieldset class="btn-group-sm" id="toolbar">
          <button
            type="button"
            class="btn btn-success"
            onclick="$.operate.add()"
            shiro:hasPermission="ph:chronic-disease-expert:add"
          >
            <i class="fa fa-plus"></i> 新增
          </button>
        </fieldset>

        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>

    <th:block th:insert="~{include :: footer}" />
    <script th:inline="javascript">
      /*<![CDATA[*/
      var editFlag = /*[[${@permission.hasPermi('ph:chronic-disease-expert:edit')}]]*/ false;
      var removeFlag = /*[[${@permission.hasPermi('ph:chronic-disease-expert:remove')}]]*/ false;
      var resetPwdFlag = /*[[${@permission.hasPermi('ph:chronic-disease-expert:resetPwd')}]]*/ false;
      var prefix = ctx + "ph/chronic-disease-expert";
      /*]]>*/

      $(function () {
        var options = {
          url: prefix + "/list",
          createUrl: prefix + "/add",
          updateUrl: prefix + "/edit/{id}",
          removeUrl: prefix + "/remove",
          sortName: "userId",
          sortOrder: "desc",
          modalName: "慢病专家",
          search: false,
          showSearch: false,
          showColumns: false,
          showToggle: false,
          columns: [
            {
              field: "userId",
              title: "专家ID",
              visible: false,
            },
            {
              field: "loginName",
              title: "登录名",
            },
            {
              field: "userName",
              title: "专家姓名",
            },
            {
              field: "phonenumber",
              title: "手机号码",
            },
            {
              field: "statusName",
              title: "状态",
              align: "center",
              formatter: function (value, row, index) {
                return statusTools(row);
              },
            },
            {
              field: "createTime",
              title: "创建时间",
            },
            {
              field: "loginDate",
              title: "最后登录",
              formatter: function (value, row, index) {
                return value || "从未登录";
              },
            },
            {
              title: "操作",
              align: "center",
              formatter: function (value, row, index) {
                var actions = [];
                actions.push(
                  '<a class="btn btn-success btn-xs ' +
                    editFlag +
                    '" href="javascript:void(0)" data-user-id="' +
                    $(document.createElement("div")).text(row.userId).html() +
                    '" data-action="edit"><i class="fa fa-edit"></i>编辑</a> '
                );
                actions.push(
                  '<a class="btn btn-danger btn-xs ' +
                    removeFlag +
                    '" href="javascript:void(0)" data-user-id="' +
                    $(document.createElement("div")).text(row.userId).html() +
                    '" data-action="remove"><i class="fa fa-remove"></i>删除</a> '
                );
                actions.push(
                  '<a class="btn btn-warning btn-xs ' +
                    resetPwdFlag +
                    '" href="javascript:void(0)" data-user-id="' +
                    $(document.createElement("div")).text(row.userId).html() +
                    '" data-action="resetPwd"><i class="fa fa-key"></i>重置密码</a> '
                );
                return actions.join("");
              },
            },
          ],
        };
        $.table.init(options);

        // 使用事件委托安全地处理操作按钮点击
        $("#bootstrap-table").on("click", "a[data-action]", function (e) {
          e.preventDefault();
          var $this = $(this);
          var userId = $this.data("user-id");
          var action = $this.data("action");

          if (!userId) {
            $.modal.msgError("用户ID不能为空");
            return;
          }

          switch (action) {
            case "edit":
              $.operate.edit(userId);
              break;
            case "remove":
              $.operate.remove(userId);
              break;
            case "resetPwd":
              resetPwd(userId);
              break;
            default:
              $.modal.msgError("未知操作类型");
          }
        });
      });

      function statusTools(row) {
        if (row.status == "0") {
          return '<i class="fa fa-circle text-success"></i> 正常';
        } else {
          return '<i class="fa fa-circle text-danger"></i> 停用';
        }
      }

      // 重置密码
      function resetPwd(userId) {
        var url = prefix + "/resetPwd/" + userId;
        $.modal.open("重置密码", url, "800", "500");
      }
    </script>
  </body>
</html>
