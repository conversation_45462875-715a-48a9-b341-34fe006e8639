<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.TijianPackageItemMapper">

    <resultMap type="space.lzhq.ph.domain.TijianPackageItem" id="TijianPackageItemResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="price" column="price"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptLocation" column="dept_location"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTijianPackageItemVo">
        select id, name, price, dept_id, dept_name, dept_location, remark
        from ph_tijian_package_item
    </sql>

    <select id="selectTijianPackageItemList" parameterType="TijianPackageItem" resultMap="TijianPackageItemResult">
        <include refid="selectTijianPackageItemVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="price != null ">and price = #{price}</if>
            <if test="deptId != null  and deptId != ''">and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{deptName}, '%')</if>
        </where>
    </select>

    <select id="selectTijianPackageItemById" parameterType="String" resultMap="TijianPackageItemResult">
        <include refid="selectTijianPackageItemVo"/>
        where id = #{id}
    </select>

    <select id="selectTijianPackageItemListByPackageId" parameterType="String" resultMap="TijianPackageItemResult">
        <include refid="selectTijianPackageItemVo"/>
        where id in (select item_id from ph_tijian_package_mapping where package_id = #{packageId})
    </select>

    <insert id="insertTijianPackageItem" parameterType="TijianPackageItem">
        insert into ph_tijian_package_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="price != null">price,</if>
            <if test="deptId != null and deptId != ''">dept_id,</if>
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="deptLocation != null and deptLocation != ''">dept_location,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="price != null">#{price},</if>
            <if test="deptId != null and deptId != ''">#{deptId},</if>
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="deptLocation != null and deptLocation != ''">#{deptLocation},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTijianPackageItem" parameterType="TijianPackageItem">
        update ph_tijian_package_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="price != null">price = #{price},</if>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="deptLocation != null and deptLocation != ''">dept_location = #{deptLocation},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTijianPackageItemById" parameterType="String">
        delete
        from ph_tijian_package_item
        where id = #{id}
    </delete>

    <delete id="deleteTijianPackageItemByIds" parameterType="String">
        delete from ph_tijian_package_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="clear">
        truncate table ph_tijian_package_item
    </update>
</mapper>