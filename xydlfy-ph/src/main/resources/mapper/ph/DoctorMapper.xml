<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.DoctorMapper">

    <resultMap type="space.lzhq.ph.domain.Doctor" id="DoctorResult">
        <result property="id" column="id"/>
        <result property="departmentId" column="department_id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="title" column="title"/>
        <result property="position" column="position"/>
        <result property="regType" column="reg_type"/>
        <result property="regFee" column="reg_fee"/>
        <result property="keyword" column="keyword"/>
        <result property="expertise" column="expertise"/>
        <result property="intro" column="intro"/>
        <result property="photo" column="photo"/>
        <result property="sortNo" column="sort_no"/>
        <result property="simplePinyin" column="simple_pinyin"/>
        <result property="fullPinyin" column="full_pinyin"/>
        <association property="department" column="department_id" javaType="space.lzhq.ph.domain.Department"
                     resultMap="DepartmentResult"/>
    </resultMap>

    <resultMap type="space.lzhq.ph.domain.Department" id="DepartmentResult">
        <result property="id" column="department_id"/>
        <result property="name" column="department_name"/>
        <result property="parentId" column="department_parent_id"/>
        <result property="parentName" column="department_parent_name"/>
        <result property="intro" column="department_intro"/>
        <result property="logo" column="department_logo"/>
        <result property="telephone" column="department_telephone"/>
        <result property="keyword" column="department_keyword"/>
        <result property="address" column="department_address"/>
        <result property="sortNo" column="department_sort_no"/>
        <result property="simplePinyin" column="department_simple_pinyin"/>
        <result property="fullPinyin" column="department_full_pinyin"/>
    </resultMap>

    <sql id="selectDoctorVo">
        select doc.id, doc.department_id, doc.name, doc.sex, doc.title, doc.expertise, doc.intro, doc.photo,
        doc.sort_no, doc.simple_pinyin, doc.full_pinyin, dept.name as department_name, dept.parent_id as department_parent_id,
        dept.parent_name as department_parent_name, dept.intro as department_intro,  dept.logo as department_logo,
        dept.telephone as department_telephone, dept.keyword as department_keyword, dept.address as department_address,
        dept.sort_no as department_sort_no, dept.simple_pinyin as department_simple_pinyin, dept.full_pinyin as department_full_pinyin
        from ph_doctor doc
        left join ph_department dept on doc.department_id = dept.id
    </sql>

    <select id="selectDoctorList" parameterType="Doctor" resultMap="DoctorResult">
        <include refid="selectDoctorVo"/>
        <where>
            <if test="departmentId != null and departmentId != ''">and department_id = #{departmentId}</if>
            <if test="name != null and name != ''">and doc.name like concat('%', #{name}, '%')</if>
            <if test="sex != null">and sex = #{sex}</if>
            <if test="simplePinyin != null and simplePinyin != ''">
                and doc.simple_pinyin like concat('%', lower(#{simplePinyin}), '%')
            </if>
            <if test="fullPinyin != null and fullPinyin != ''">
                and doc.full_pinyin like concat('%', lower(#{fullPinyin}), '%')
            </if>
        </where>
    </select>

    <select id="selectDoctorById" parameterType="String" resultMap="DoctorResult">
        <include refid="selectDoctorVo"/>
        where doc.id = #{id}
    </select>

    <insert id="insertDoctor" parameterType="Doctor">
        insert into ph_doctor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="title != null">title,</if>
            <if test="position != null">position,</if>
            <if test="regType != null">reg_type,</if>
            <if test="regFee != null">reg_fee,</if>
            <if test="keyword != null">keyword,</if>
            <if test="expertise != null">expertise,</if>
            <if test="intro != null">intro,</if>
            <if test="photo != null">photo,</if>
            <if test="sortNo != null">sort_no,</if>
            <if test="simplePinyin != null">simple_pinyin,</if>
            <if test="fullPinyin != null">full_pinyin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="name != null">#{name},</if>
            <if test="sex != null ">#{sex},</if>
            <if test="title != null">#{title},</if>
            <if test="position != null">#{position},</if>
            <if test="regType != null">#{regType},</if>
            <if test="regFee != null">#{regFee},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="expertise != null">#{expertise},</if>
            <if test="intro != null">#{intro},</if>
            <if test="photo != null">#{photo},</if>
            <if test="sortNo != null ">#{sortNo},</if>
            <if test="simplePinyin != null">#{simplePinyin},</if>
            <if test="fullPinyin != null">#{fullPinyin},</if>
        </trim>
    </insert>

    <insert id="insertDoctors" parameterType="java.util.List">
        insert into ph_doctor (id, department_id, name, sex, title, position, reg_type, reg_fee, keyword, expertise, intro, photo, sort_no, simple_pinyin,
        full_pinyin)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.departmentId},#{item.name},#{item.sex},#{item.title},#{item.position},#{item.regType},#{item.regFee},#{item.keyword},#{item.expertise},#{item.intro},#{item.photo},#{item.sortNo},#{item.simplePinyin},#{item.fullPinyin})
        </foreach>
    </insert>

    <update id="updateDoctor" parameterType="Doctor">
        update ph_doctor
        <trim prefix="SET" suffixOverrides=",">
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sex != null ">sex = #{sex},</if>
            <if test="title != null">title = #{title},</if>
            <if test="position != null">position = #{position},</if>
            <if test="regType != null">reg_type = #{regType},</if>
            <if test="regFee != null">reg_fee = #{regFee},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="expertise != null">expertise = #{expertise},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="sortNo != null ">sort_no = #{sortNo},</if>
            <if test="simplePinyin != null">simple_pinyin = #{simplePinyin},</if>
            <if test="fullPinyin != null">full_pinyin = #{fullPinyin},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateDoctors" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update ph_doctor
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.departmentId != null">department_id = #{item.departmentId},</if>
                <if test="item.name != null">name = #{item.name},</if>
                <if test="item.sex != null ">sex = #{item.sex},</if>
                <if test="item.title != null">title = #{item.title},</if>
                <if test="item.position != null">position = #{item.position},</if>
                <if test="item.regType != null">reg_type = #{item.regType},</if>
                <if test="item.regFee != null">reg_fee = #{item.regFee},</if>
                <if test="item.keyword != null">keyword = #{item.keyword},</if>
                <if test="item.expertise != null">expertise = #{item.expertise},</if>
                <if test="item.intro != null">intro = #{item.intro},</if>
                <if test="item.photo != null">photo = #{item.photo},</if>
                <if test="item.sortNo != null ">sort_no = #{item.sortNo},</if>
                <if test="item.simplePinyin != null">simple_pinyin = #{item.simplePinyin},</if>
                <if test="item.fullPinyin != null">full_pinyin = #{item.fullPinyin},</if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteDoctorById" parameterType="String">
        delete from ph_doctor where id = #{id}
    </delete>

    <delete id="deleteDoctorByIds" parameterType="String">
        delete from ph_doctor where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>