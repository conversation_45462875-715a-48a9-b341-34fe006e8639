<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.DrugMapper">

    <resultMap type="Drug" id="DrugResult">
        <result property="id" column="id"/>
        <result property="no" column="no"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="specification" column="specification"/>
        <result property="unit" column="unit"/>
        <result property="price" column="price"/>
    </resultMap>

    <sql id="selectDrugVo">
        select id, no, name, type, specification, unit, price
        from ph_drug
    </sql>

    <select id="selectDrugList" parameterType="Drug" resultMap="DrugResult">
        <include refid="selectDrugVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
        </where>
    </select>

    <select id="selectDrugById" parameterType="String" resultMap="DrugResult">
        <include refid="selectDrugVo"/>
        where id = #{id}
    </select>

    <insert id="insertDrug" parameterType="Drug">
        insert into ph_drug
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="no != null and no != ''">no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="specification != null and specification != ''">specification,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="price != null">price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="no != null and no != ''">#{no},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="specification != null and specification != ''">#{specification},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="price != null">#{price},</if>
        </trim>
    </insert>

    <insert id="insertDrugs">
        insert into ph_drug (no, name, type, specification, unit, price) VALUES
        <foreach collection="drugs" item="drug" separator=",">
            (#{drug.no}, #{drug.name}, #{drug.type}, #{drug.specification}, #{drug.unit}, #{drug.price})
        </foreach>
    </insert>

    <update id="updateDrug" parameterType="Drug">
        update ph_drug
        <trim prefix="SET" suffixOverrides=",">
            <if test="no != null and no != ''">no = #{no},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="specification != null and specification != ''">specification = #{specification},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="price != null">price = #{price},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDrugById" parameterType="String">
        delete
        from ph_drug
        where id = #{id}
    </delete>

    <delete id="deleteDrugByIds" parameterType="String">
        delete from ph_drug where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="clearAll">
        truncate table ph_drug
    </update>
</mapper>