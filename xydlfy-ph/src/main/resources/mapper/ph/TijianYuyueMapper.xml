<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.TijianYuyueMapper">

    <resultMap type="space.lzhq.ph.domain.TijianYuyue" id="TijianYuyueResult">
        <result property="id" column="id"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationTime" column="operation_time"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="nation" column="nation"/>
        <result property="profession" column="profession"/>
        <result property="area" column="area"/>
        <result property="address" column="address"/>
        <result property="date" column="date"/>
        <result property="time" column="time"/>
        <result property="packageId" column="package_id"/>
        <result property="packageName" column="package_name"/>
        <result property="operationResult" column="operation_result"/>
        <result property="operationDesc" column="operation_desc"/>
        <result property="reservationId" column="reservation_id"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectTijianYuyueVo">
        select id,
               operation_type,
               operation_time,
               id_card_no,
               name,
               mobile,
               marital_status,
               nation,
               profession,
               area,
               address,
               date,
               time,
               package_id,
               package_name,
               operation_result,
               operation_desc,
               reservation_id,
               status
        from ph_tijian_yuyue
    </sql>

    <select id="selectTijianYuyueList" parameterType="TijianYuyue" resultMap="TijianYuyueResult">
        <include refid="selectTijianYuyueVo"/>
        <where>
            <if test="operationType != null ">and operation_type = #{operationType}</if>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="date != null ">and date = #{date}</if>
            <if test="packageId != null  and packageId != ''">and package_id = #{packageId}</if>
            <if test="packageName != null  and packageName != ''">and package_name like concat('%', #{packageName},
                '%')
            </if>
            <if test="operationResult != null ">and operation_result = #{operationResult}</if>
            <if test="operationDesc != null  and operationDesc != ''">and operation_desc like concat('%',
                #{operationDesc}, '%')
            </if>
            <if test="reservationId != null  and reservationId != ''">and reservation_id = #{reservationId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
    </select>

    <select id="selectTijianYuyueById" parameterType="Long" resultMap="TijianYuyueResult">
        <include refid="selectTijianYuyueVo"/>
        where id = #{id}
    </select>

    <select id="selectLatestOneByIdCardNo" parameterType="String" resultMap="TijianYuyueResult">
        <include refid="selectTijianYuyueVo"/>
        where id_card_no = #{idCardNo}
        order by id desc
        limit 1
    </select>

    <select id="countAvailableByDate" resultType="java.lang.Integer">
        select count(*)
        from ph_tijian_yuyue
        where date = #{date}
          and status = 0
    </select>

    <select id="countAvailableByDateRange" resultType="java.util.Map"><![CDATA[
        select count(*)                      as count,
               date_format(date, '%Y-%m-%d') as date
        from ph_tijian_yuyue
        where date >= #{minDate}
          and date <= #{maxDate}
          and status = 0
        group by date
        ]]>
    </select>

    <insert id="insertTijianYuyue" parameterType="TijianYuyue" useGeneratedKeys="true" keyProperty="id">
        insert into ph_tijian_yuyue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationType != null">operation_type,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="maritalStatus != null">marital_status,</if>
            <if test="nation != null and nation != ''">nation,</if>
            <if test="profession != null and profession != ''">profession,</if>
            <if test="area != null and area != ''">area,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="date != null">date,</if>
            <if test="time != null and time != ''">time,</if>
            <if test="packageId != null and packageId != ''">package_id,</if>
            <if test="packageName != null and packageName != ''">package_name,</if>
            <if test="operationResult != null">operation_result,</if>
            <if test="operationDesc != null and operationDesc != ''">operation_desc,</if>
            <if test="reservationId != null">reservation_id,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationType != null">#{operationType},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="idCardNo != null and idCardNo != ''">#{idCardNo},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="maritalStatus != null">#{maritalStatus},</if>
            <if test="nation != null and nation != ''">#{nation},</if>
            <if test="profession != null and profession != ''">#{profession},</if>
            <if test="area != null and area != ''">#{area},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="date != null">#{date},</if>
            <if test="time != null and time != ''">#{time},</if>
            <if test="packageId != null and packageId != ''">#{packageId},</if>
            <if test="packageName != null and packageName != ''">#{packageName},</if>
            <if test="operationResult != null">#{operationResult},</if>
            <if test="operationDesc != null and operationDesc != ''">#{operationDesc},</if>
            <if test="reservationId != null">#{reservationId},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateTijianYuyue" parameterType="TijianYuyue">
        update ph_tijian_yuyue
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no = #{idCardNo},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="maritalStatus != null">marital_status = #{maritalStatus},</if>
            <if test="nation != null and nation != ''">nation = #{nation},</if>
            <if test="profession != null and profession != ''">profession = #{profession},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="date != null">date = #{date},</if>
            <if test="time != null and time != ''">time = #{time},</if>
            <if test="packageId != null and packageId != ''">package_id = #{packageId},</if>
            <if test="packageName != null and packageName != ''">package_name = #{packageName},</if>
            <if test="operationResult != null">operation_result = #{operationResult},</if>
            <if test="operationDesc != null and operationDesc != ''">operation_desc = #{operationDesc},</if>
            <if test="reservationId != null">reservation_id = #{reservationId},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTijianYuyueById" parameterType="Long">
        delete
        from ph_tijian_yuyue
        where id = #{id}
    </delete>

    <delete id="deleteTijianYuyueByIds" parameterType="String">
        delete from ph_tijian_yuyue where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>