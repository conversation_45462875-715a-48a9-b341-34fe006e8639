<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.TijianPackageMapper">

    <resultMap type="space.lzhq.ph.domain.TijianPackage" id="TijianPackageResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="price" column="price"/>
        <result property="gender" column="gender"/>
        <result property="maritalStatus" column="maritalStatus"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTijianPackageVo">
        select id, name, price, gender, maritalStatus, remark
        from ph_tijian_package
    </sql>

    <select id="selectTijianPackageList" parameterType="TijianPackage" resultMap="TijianPackageResult">
        <include refid="selectTijianPackageVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="gender != null ">and gender = #{gender}</if>
            <if test="maritalStatus != null ">and maritalStatus = #{maritalStatus}</if>
        </where>
    </select>

    <select id="selectTijianPackageById" parameterType="String" resultMap="TijianPackageResult">
        <include refid="selectTijianPackageVo"/>
        where id = #{id}
    </select>

    <insert id="insertTijianPackage" parameterType="TijianPackage">
        insert into ph_tijian_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="price != null">price,</if>
            <if test="gender != null">gender,</if>
            <if test="maritalStatus != null">maritalStatus,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="price != null">#{price},</if>
            <if test="gender != null">#{gender},</if>
            <if test="maritalStatus != null">#{maritalStatus},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTijianPackage" parameterType="TijianPackage">
        update ph_tijian_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="price != null">price = #{price},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="maritalStatus != null">maritalStatus = #{maritalStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTijianPackageById" parameterType="String">
        delete
        from ph_tijian_package
        where id = #{id}
    </delete>

    <delete id="deleteTijianPackageByIds" parameterType="String">
        delete from ph_tijian_package where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="clear">
        truncate table ph_tijian_package
    </update>
</mapper>