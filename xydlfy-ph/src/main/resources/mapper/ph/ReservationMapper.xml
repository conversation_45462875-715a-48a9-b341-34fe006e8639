<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.ReservationMapper">

    <resultMap type="space.lzhq.ph.domain.Reservation" id="ReservationResult">
        <result property="id" column="id"/>
        <result property="operationType" column="operation_type"/>
        <result property="reservationType" column="reservation_type"/>
        <result property="operationTime" column="operation_time"/>
        <result property="openid" column="openid"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="patientId" column="patient_id"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="jzDate" column="jz_date"/>
        <result property="jzTime" column="jz_time"/>
        <result property="period" column="period"/>
        <result property="departmentCode" column="department_code"/>
        <result property="departmentName" column="department_name"/>
        <result property="departmentLocation" column="department_location"/>
        <result property="doctorCode" column="doctor_code"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="visitNumber" column="visit_number"/>
        <result property="reservationNumber" column="reservation_number"/>
        <result property="operationResult" column="operation_result"/>
        <result property="operationDesc" column="operation_desc"/>
        <result property="status" column="status"/>
        <result property="alipayHospitalOrderId" column="alipay_hospital_order_id"/>
    </resultMap>

    <sql id="selectReservationVo">
        select id,
        operation_type,
        reservation_type,
        operation_time,
        openid,
        id_card_no,
        patient_id,
        name,
        mobile,
        jz_date,
        jz_time,
        period,
        department_code,
        department_name,
        department_location,
        doctor_code,
        doctor_name,
        visit_number,
        reservation_number,
        operation_result,
        operation_desc,
        status,
        alipay_hospital_order_id
        from ph_reservation
    </sql>

    <select id="selectReservationList" parameterType="Reservation" resultMap="ReservationResult">
        <include refid="selectReservationVo"/>
        <where>
            <if test="operationType != null ">and operation_type = #{operationType}</if>
            <if test="reservationType != null ">and reservation_type = #{reservationType}</if>
            <if test="params.beginOperationTime != null and params.beginOperationTime != '' and params.endOperationTime != null and params.endOperationTime != ''">
                and operation_time between #{params.beginOperationTime} and #{params.endOperationTime}
            </if>
            <if test="openid != null  and openid != ''">and openid = #{openid}</if>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="patientId != null  and patientId != ''">and patient_id = #{patientId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="jzDate != null ">and jz_date = #{jzDate}</if>
            <if test="departmentCode != null  and departmentCode != ''">and department_code = #{departmentCode}</if>
            <if test="departmentName != null  and departmentName != ''">and department_name like concat('%',
                #{departmentName}, '%')
            </if>
            <if test="doctorCode != null  and doctorCode != ''">and doctor_code = #{doctorCode}</if>
            <if test="doctorName != null  and doctorName != ''">and doctor_name = #{doctorName}</if>
            <if test="reservationNumber != null  and reservationNumber != ''">and reservation_number =
                #{reservationNumber}
            </if>
            <if test="operationResult != null ">and operation_result = #{operationResult}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="alipayHospitalOrderId != null ">and alipay_hospital_order_id = #{alipayHospitalOrderId}</if>
        </where>
    </select>

    <select id="selectReservationById" parameterType="Long" resultMap="ReservationResult">
        <include refid="selectReservationVo"/>
        where id = #{id}
    </select>

    <select id="selectOneByReservationNumber" resultMap="ReservationResult">
        <include refid="selectReservationVo"/>
        where reservation_number = #{reservationNumber}
        and operation_type = 0
        order by id desc
        limit 1
    </select>

    <insert id="insertReservation" parameterType="Reservation" useGeneratedKeys="true" keyProperty="id">
        insert into ph_reservation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationType != null">operation_type,</if>
            <if test="reservationType != null">reservation_type,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no,</if>
            <if test="patientId != null and patientId != ''">patient_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="jzDate != null">jz_date,</if>
            <if test="jzTime != null">jz_time,</if>
            <if test="period != null">period,</if>
            <if test="departmentCode != null and departmentCode != ''">department_code,</if>
            <if test="departmentName != null and departmentName != ''">department_name,</if>
            <if test="departmentLocation != null and departmentLocation != ''">department_location,</if>
            <if test="doctorCode != null and doctorCode != ''">doctor_code,</if>
            <if test="doctorName != null and doctorName != ''">doctor_name,</if>
            <if test="visitNumber != null and visitNumber != ''">visit_number,</if>
            <if test="reservationNumber != null and reservationNumber != ''">reservation_number,</if>
            <if test="operationResult != null">operation_result,</if>
            <if test="operationDesc != null and operationDesc != ''">operation_desc,</if>
            <if test="status != null">status,</if>
            <if test="alipayHospitalOrderId != null">alipay_hospital_order_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationType != null">#{operationType},</if>
            <if test="reservationType != null">#{reservationType},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="idCardNo != null and idCardNo != ''">#{idCardNo},</if>
            <if test="patientId != null and patientId != ''">#{patientId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="jzDate != null">#{jzDate},</if>
            <if test="jzTime != null">#{jzTime},</if>
            <if test="period != null">#{period},</if>
            <if test="departmentCode != null and departmentCode != ''">#{departmentCode},</if>
            <if test="departmentName != null and departmentName != ''">#{departmentName},</if>
            <if test="departmentLocation != null and departmentLocation != ''">#{departmentLocation},</if>
            <if test="doctorCode != null and doctorCode != ''">#{doctorCode},</if>
            <if test="doctorName != null and doctorName != ''">#{doctorName},</if>
            <if test="visitNumber != null and visitNumber != ''">#{visitNumber},</if>
            <if test="reservationNumber != null and reservationNumber != ''">#{reservationNumber},</if>
            <if test="operationResult != null">#{operationResult},</if>
            <if test="operationDesc != null and operationDesc != ''">#{operationDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="alipayHospitalOrderId != null">#{alipayHospitalOrderId},</if>
        </trim>
    </insert>

    <update id="updateReservation" parameterType="Reservation">
        update ph_reservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="reservationType != null">operationType = #{reservationType},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no = #{idCardNo},</if>
            <if test="patientId != null and patientId != ''">patient_id = #{patientId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="jzDate != null">jz_date = #{jzDate},</if>
            <if test="jzTime != null">jz_time = #{jzTime},</if>
            <if test="period != null">period = #{period},</if>
            <if test="departmentCode != null and departmentCode != ''">department_code = #{departmentCode},</if>
            <if test="departmentName != null and departmentName != ''">department_name = #{departmentName},</if>
            <if test="departmentLocation != null and departmentLocation != ''">department_location =
                #{departmentLocation},
            </if>
            <if test="doctorCode != null and doctorCode != ''">doctor_code = #{doctorCode},</if>
            <if test="doctorName != null and doctorName != ''">doctor_name = #{doctorName},</if>
            <if test="visitNumber != null and visitNumber != ''">visit_number = #{visitNumber},</if>
            <if test="reservationNumber != null and reservationNumber != ''">reservation_number =
                #{reservationNumber},
            </if>
            <if test="operationResult != null">operation_result = #{operationResult},</if>
            <if test="operationDesc != null and operationDesc != ''">operation_desc = #{operationDesc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="alipayHospitalOrderId != null">alipay_hospital_order_id = #{alipayHospitalOrderId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReservationById" parameterType="Long">
        delete
        from ph_reservation
        where id = #{id}
    </delete>

    <delete id="deleteReservationByIds" parameterType="String">
        delete from ph_reservation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
