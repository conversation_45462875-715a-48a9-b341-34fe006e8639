<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.TijianPackageMappingMapper">

    <resultMap type="space.lzhq.ph.domain.TijianPackageMapping" id="TijianPackageMappingResult">
        <result property="packageId" column="package_id"/>
        <result property="itemId" column="item_id"/>
    </resultMap>

    <sql id="selectTijianPackageMappingVo">
        select package_id, item_id
        from ph_tijian_package_mapping
    </sql>

    <select id="selectTijianPackageMappingByPackageId" parameterType="String" resultMap="TijianPackageMappingResult">
        <include refid="selectTijianPackageMappingVo"/>
        where package_id = #{packageId}
    </select>

    <delete id="deleteTijianPackageMappingByPackageId" parameterType="String">
        delete
        from ph_tijian_package_mapping
        where package_id = #{packageId}
    </delete>

    <delete id="deleteTijianPackageMappingByPackageIds" parameterType="String">
        delete from ph_tijian_package_mapping where package_id in
        <foreach item="packageId" collection="packageIds" open="(" separator="," close=")">
            #{packageId}
        </foreach>
    </delete>

    <insert id="insertList">
        insert into ph_tijian_package_mapping(package_id, item_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.packageId},#{item.itemId})
        </foreach>
    </insert>

    <update id="clear">
        truncate table ph_tijian_package_mapping
    </update>
</mapper>