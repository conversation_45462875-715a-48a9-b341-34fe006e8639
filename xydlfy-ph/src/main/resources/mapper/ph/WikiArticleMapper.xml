<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.WikiArticleMapper">

    <resultMap type="WikiArticle" id="WikiArticleResult">
        <result property="id" column="id"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="title" column="title"/>
        <result property="summary" column="summary"/>
        <result property="coverImage" column="cover_image"/>
        <result property="content" column="content"/>
        <result property="videoUrl" column="video_url"/>
        <result property="sortNo" column="sort_no"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectWikiArticleVo">
        select id,
               category_id,
               category_name,
               title,
               summary,
               cover_image,
               video_url,
               sort_no,
               create_by,
               create_time,
               update_by,
               update_time
        from ph_wiki_article
    </sql>

    <select id="selectWikiArticleList" parameterType="WikiArticle" resultMap="WikiArticleResult">
        <include refid="selectWikiArticleVo"/>
        <where>
            <if test="categoryId != null ">and category_id = #{categoryId}</if>
            <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
        </where>
    </select>

    <select id="selectWikiArticleById" parameterType="Long" resultMap="WikiArticleResult">
        select id,
               category_id,
               category_name,
               title,
               summary,
               content,
               cover_image,
               video_url,
               sort_no,
               create_by,
               create_time,
               update_by,
               update_time
        from ph_wiki_article
        where id = #{id}
        limit 1
    </select>

    <select id="countByCategoryId" parameterType="Long" resultType="Long">
        select count(*)
        from ph_wiki_article
        where category_id = #{categoryId}
    </select>

    <select id="selectListByCategoryId" parameterType="Long" resultMap="WikiArticleResult">
        <include refid="selectWikiArticleVo"/>
        where category_id = #{categoryId}
        order by sort_no desc, id desc
    </select>

    <insert id="insertWikiArticle" parameterType="WikiArticle" useGeneratedKeys="true" keyProperty="id">
        insert into ph_wiki_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null ">category_id,</if>
            <if test="categoryName != null  and categoryName != ''">category_name,</if>
            <if test="title != null  and title != ''">title,</if>
            <if test="summary != null  and summary != ''">summary,</if>
            <if test="coverImage != null and coverImage != ''">cover_image,</if>
            <if test="content != null  and content != ''">content,</if>
            <if test="videoUrl != null  and videoUrl != ''">video_url,</if>
            <if test="sortNo != null">sort_no,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateTime != null ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null ">#{categoryId},</if>
            <if test="categoryName != null  and categoryName != ''">#{categoryName},</if>
            <if test="title != null  and title != ''">#{title},</if>
            <if test="summary != null  and summary != ''">#{summary},</if>
            <if test="coverImage != null and coverImage != ''">#{coverImage},</if>
            <if test="content != null  and content != ''">#{content},</if>
            <if test="videoUrl != null  and videoUrl != ''">#{videoUrl},</if>
            <if test="sortNo != null">#{sortNo},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null ">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWikiArticle" parameterType="WikiArticle">
        update ph_wiki_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null ">category_id = #{categoryId},</if>
            <if test="categoryName != null  and categoryName != ''">category_name = #{categoryName},</if>
            <if test="title != null  and title != ''">title = #{title},</if>
            <if test="summary != null  and summary != ''">summary = #{summary},</if>
            <if test="coverImage != null  and coverImage != ''">cover_image = #{coverImage},</if>
            <if test="content != null  and content != ''">content = #{content},</if>
            <if test="videoUrl != null  and videoUrl != ''">video_url = #{videoUrl},</if>
            <if test="sortNo != null">sort_no = #{sortNo},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCategoryName">
        update ph_wiki_article
        set category_name = #{categoryName}
        where category_id = #{categoryId}
    </update>

    <delete id="deleteWikiArticleById" parameterType="Long">
        delete
        from ph_wiki_article
        where id = #{id}
    </delete>

    <delete id="deleteWikiArticleByIds" parameterType="String">
        delete from ph_wiki_article where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>