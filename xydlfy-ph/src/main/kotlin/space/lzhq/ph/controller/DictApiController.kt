package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.domain.entity.SysDictData
import com.ruoyi.framework.web.service.DictService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/open/dict")
class DictApiController {

    @Autowired
    private lateinit var dictService: DictService

    @Deprecated("此接口返回值不是标准的 AjaxResult，使用 findByType 替代")
    @GetMapping("/queryByType")
    fun queryByType(@RequestParam type: String): List<SysDictData> {
        return dictService.getType(type)
    }

    @GetMapping("/{type}")
    fun findByType(@PathVariable type: String): AjaxResult {
        return AjaxResult.success(dictService.getType(type))
    }

}
