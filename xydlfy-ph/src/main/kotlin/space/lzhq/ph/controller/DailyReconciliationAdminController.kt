package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.page.TableDataInfo
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import space.lzhq.ph.domain.ReconciliationSearchForm
import space.lzhq.ph.service.IDailyReconciliationService

@Controller
@RequestMapping("/ph/daily_reconciliation")
class DailyReconciliationAdminController : BaseController() {

    private val prefix = "ph/daily_reconciliation"

    @Autowired
    private lateinit var service: IDailyReconciliationService

    @RequiresPermissions("ph:daily_reconciliation:view")
    @GetMapping
    fun index(modelMap: ModelMap): String {
        modelMap.addAttribute("user", sysUser)
        return "$prefix/index"
    }

    @RequiresPermissions("ph:daily_reconciliation:view")
    @PostMapping("/page")
    @ResponseBody
    fun page(searchForm: ReconciliationSearchForm): TableDataInfo {
        startPage("date DESC")
        val list = service.listBySearchForm(searchForm)
        return getDataTable(list)
    }

}