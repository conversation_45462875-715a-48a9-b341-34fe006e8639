package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.file.MimeTypeUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.ext.FileUploader

@Controller
class FileApiController {

    companion object {
        private val log = LoggerFactory.getLogger(FileApiController::class.java)
    }

    @RequireSession
    @PostMapping("/api/upload")
    @ResponseBody
    fun upload(file: MultipartFile): AjaxResult {
        try {
            val newFile = FileUploader.upload(
                file = file,
                allowedExtension = MimeTypeUtils.IMAGE_EXTENSION
            )
            return AjaxResult.success(
                mapOf(
                    "url" to FileUploader.getUrl(file = newFile)
                )
            )
        } catch (e: Exception) {
            log.error("上传文件失败：", e)
            return AjaxResult.error("上传文件失败：${e.message}")
        }
    }

}
