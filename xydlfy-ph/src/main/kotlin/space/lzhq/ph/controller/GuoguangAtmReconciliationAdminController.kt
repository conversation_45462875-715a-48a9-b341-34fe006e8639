package space.lzhq.ph.controller

import cn.idev.excel.FastExcel
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import lombok.extern.slf4j.Slf4j
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.date.DatePattern
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.*
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.GuoguangAtmReconciliationDetail
import space.lzhq.ph.domain.HisSelfPayOrder
import space.lzhq.ph.domain.ReconciliationSearchForm
import space.lzhq.ph.service.CCBPayServiceExt
import space.lzhq.ph.service.IGuoguangAtmReconciliationDetailService
import space.lzhq.ph.service.IGuoguangAtmReconciliationService
import space.lzhq.ph.service.IHisSelfPayOrderService
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.time.LocalDate

@Controller
@RequestMapping("/ph/guoguang_atm_reconciliation")
@Slf4j
class GuoguangAtmReconciliationAdminController : BaseController() {

    private val prefix = "ph/guoguang_atm_reconciliation"

    @Autowired
    private lateinit var service: IGuoguangAtmReconciliationService

    @Autowired
    private lateinit var reconciliationDetailService: IGuoguangAtmReconciliationDetailService

    @Autowired
    private lateinit var hisSelfPayOrderService: IHisSelfPayOrderService

    @Autowired
    private lateinit var ccbPayServiceExt: CCBPayServiceExt

    @RequiresPermissions("ph:guoguang_atm_reconciliation:view")
    @GetMapping
    fun index(
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) @RequestParam(required = false) date: LocalDate?,
        modelMap: ModelMap
    ): String {
        modelMap.addAttribute("user", sysUser)
        modelMap.addAttribute("startDate", date?.format(DatePattern.NORM_DATE_FORMATTER) ?: "")
        modelMap.addAttribute("endDate", date?.format(DatePattern.NORM_DATE_FORMATTER) ?: "")
        return "$prefix/index"
    }

    @RequiresPermissions("ph:guoguang_atm_reconciliation:view")
    @PostMapping("/page")
    @ResponseBody
    fun page(searchForm: ReconciliationSearchForm): TableDataInfo {
        startPage("date DESC")
        val list = service.listBySearchForm(searchForm)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:guoguang_atm_reconciliation:downloadAtmBill")
    @GetMapping("/downloadAtmBill/{date}")
    fun downloadAtmBill(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<Resource> {
        val zipBillPath = ccbPayServiceExt.downloadBill(date)
        check(zipBillPath != null && Files.exists(zipBillPath)) { "账单文件不存在: $zipBillPath" }

        val resource = FileSystemResource(zipBillPath)
        val fileName = zipBillPath.fileName.toString()
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .contentLength(resource.contentLength())
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(resource)
    }

    @RequiresPermissions("ph:guoguang_atm_reconciliation:downloadHisBill")
    @GetMapping("/downloadHisBill/{date}")
    fun downloadHisBill(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val hisOrders = hisSelfPayOrderService.listForGuoguangAtm(date).sortedBy { it.tradeTime }

        val fileName = "国光自助机_HIS账单_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, HisSelfPayOrder::class.java).sheet(0, "Sheet1").doWrite(hisOrders)
                } catch (e: Exception) {
                    logger.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:guoguang_atm_reconciliation:reconcile")
    @PostMapping("/reconcile/{date}")
    @ResponseBody
    fun reconcile(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate): AjaxResult {
        val ok = service.reconcile(date)
        return toAjax(ok)
    }

    @RequiresPermissions("ph:guoguang_atm_reconciliation:downloadReconciliationDetails")
    @GetMapping("/downloadReconciliationDetails/{date}")
    fun downloadReconciliationDetails(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val reconciliationDetails = reconciliationDetailService.listByBillDate(date).sorted()

        val fileName = "国光自助机_对账明细_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, GuoguangAtmReconciliationDetail::class.java)
                        .sheet(0, "Sheet1")
                        .doWrite(reconciliationDetails)
                } catch (e: Exception) {
                    logger.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:guoguang_atm_reconciliation:downloadNotBalancedReconciliationDetails")
    @GetMapping("/downloadNotBalancedReconciliationDetails/{date}")
    fun downloadNotBalancedReconciliationDetails(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val reconciliationDetails = reconciliationDetailService.listNotBalanced(date).sorted()

        val fileName = "国光自助机_错单明细_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, GuoguangAtmReconciliationDetail::class.java)
                        .sheet(0, "Sheet1")
                        .doWrite(reconciliationDetails)
                } catch (e: Exception) {
                    logger.error(e.message, e)
                }
            })
    }
}