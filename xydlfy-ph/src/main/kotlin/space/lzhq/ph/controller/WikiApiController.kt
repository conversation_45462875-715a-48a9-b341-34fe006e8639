package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.service.IWikiArticleService
import space.lzhq.ph.service.IWikiCategoryService

@RestController
@RequestMapping("/open/wiki")
class WikiApiController : BaseController() {

    @Autowired
    private lateinit var wikiCategoryService: IWikiCategoryService

    @Autowired
    private lateinit var wikiArticleService: IWikiArticleService

    @GetMapping("/categories")
    fun categories(): AjaxResult {
        val categories = wikiCategoryService.selectAll()
        return AjaxResult.success(categories)
    }

    @GetMapping("/articles")
    fun articles(categoryId: Long): AjaxResult {
        val articles = wikiArticleService.selectListByCategoryId(categoryId)
        return AjaxResult.success(articles)
    }

    @GetMapping("/article")
    fun article(articleId: Long): AjaxResult {
        val article = wikiArticleService.selectWikiArticleById(articleId)
        return AjaxResult.success(article)
    }

}