package space.lzhq.ph.controller

import com.alipay.api.response.AlipayTradeCreateResponse
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import org.dromara.hutool.core.data.IdcardUtil
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.text.CharSequenceUtil
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.bsoft.hai.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.DingRobot
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.*
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.pay.ccb.CcbPayServices
import space.lzhq.ph.pay.ccb.bean.RefundResponse
import space.lzhq.ph.service.*
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Suppress("DuplicatedCode")
@RestController
@RequestMapping("/api/zhuyuan")
class ZhuyuanController : BaseController() {

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var alipayPaymentService: IAlipayPaymentService

    @Autowired
    private lateinit var alipayRefundService: IAlipayRefundService

    @Autowired
    private lateinit var patientService: IPatientService

    @GetMapping("patient/v1")
    @RequireSession
    fun getPatientV1(
        @RequestParam admissionNumber: String,
        @RequestParam idCardNo: String,
    ): AjaxResult {
        if (IdcardUtil.isValidCard(idCardNo).not()) {
            return AjaxResult.error("住院号或身份证号码不正确")
        }

        val zhuyuanPatientResponse = BsoftHaiServices.getZhuyuanPatientInfo(
            ZhuyuanPatientInfoForm(
                admissionNumber = admissionNumber
            )
        )
        return if (zhuyuanPatientResponse.isOk()) {
            val zhuyuanPatientInfo = zhuyuanPatientResponse.zhuyuanPatientInfo
            if (zhuyuanPatientInfo.patientIdCard != idCardNo) {
                AjaxResult.error("住院号或身份证号码不正确")
            } else {
                AjaxResult.success(zhuyuanPatientInfo)
            }
        } else {
            AjaxResult.error(zhuyuanPatientResponse.message)
        }
    }

    @GetMapping("zhuyuanBalance")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun getZhuyuanBalance(): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val zhuyuanHistoryListResponse = BsoftHaiServices.getZhuyuanHistoryListByIdCardNo(currentPatient.idCardNo)
        if (!zhuyuanHistoryListResponse.isOk()) {
            return AjaxResult.error(zhuyuanHistoryListResponse.message)
        }

        val zhuyuanHistory: ZhuyuanHistory = zhuyuanHistoryListResponse.zhuyuanHistoryList.firstOrNull {
            it.admissionNo == currentPatient.zhuyuanNo
        } ?: return AjaxResult.error("查询住院余额失败")

        currentPatient.admissionNumber = zhuyuanHistory.admissionNumber
        try {
            currentPatient.ruyuanTime = DateUtil.parse(zhuyuanHistory.admissionTime)

            if (CharSequenceUtil.isNotBlank(zhuyuanHistory.leaveTime)) {
                currentPatient.chuyuanTime = DateUtil.parse(zhuyuanHistory.leaveTime)
            }
        } catch (_: Exception) {
            // ignore
        }
        patientService.updatePatient(currentPatient)

        return if (currentPatient.chuyuanTime != null) {
            AjaxResult.error("已出院")
        } else {
            AjaxResult.success(zhuyuanHistory.balance.toBigDecimal().setScale(2, RoundingMode.HALF_UP))
        }
    }

    /**
     * 获取住院的费用清单
     * @param date 清单日
     */
    @GetMapping("v2/fee")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun feeV2(
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()

        val zhuyuanFeeResponse = BsoftHaiServices.getZhuyuanFeeList(
            ZhuyuanFeeForm(
                admissionNo = activePatient.zhuyuanNo,
                startTime = date.atStartOfDay(),
                endTime = date.atTime(23, 59, 59)
            )
        )

        return if (zhuyuanFeeResponse.isOk()) {
            val list = zhuyuanFeeResponse.zhuyuanFeeList.sortedBy { it.expenseTime }
            val totalMoney = list.sumOf {
                try {
                    it.totalMoney.toBigDecimal()
                } catch (_: Exception) {
                    BigDecimal.ZERO
                }
            }
            AjaxResult.success(
                mapOf(
                    "date" to date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    "list" to list,
                    "totalMoney" to totalMoney.setScale(2, RoundingMode.HALF_UP)
                )
            )
        } else {
            AjaxResult.error(zhuyuanFeeResponse.message)
        }
    }

    /**
     * 获取住院的账单
     */
    @GetMapping("bill")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun bill(): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val zhuyuanBillResponse = BsoftHaiServices.getZhuyuanBillList(
            form = ZhuyuanBillForm(
                admissionNo = activePatient.zhuyuanNo,
            )
        )

        return if (zhuyuanBillResponse.isOk()) {
            AjaxResult.success(zhuyuanBillResponse.zhuyuanBillList.sortedByDescending { it.paymentTime })
        } else {
            AjaxResult.error(zhuyuanBillResponse.message)
        }
    }

    /**
     * 获取出院带药
     */
    @GetMapping("dischargeInstructions")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun dischargeInstructions(): AjaxResult {
        val activePatient = request.getCurrentPatient()

        val dischargeInstructionResponse = BsoftHaiServices.getDischargeInstruction(
            DischargeInstructionForm(
                patientId = activePatient.patientNo,
                admissionNo = activePatient.zhuyuanNo
            )
        )
        return if (dischargeInstructionResponse.isOk()) {
            AjaxResult.success(dischargeInstructionResponse.dischargeInstruction)
        } else {
            AjaxResult.error(dischargeInstructionResponse.message)
        }
    }

    /**
     * 充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("ccbpay")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun ccbpay(
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-zhuyuan-chongzhi", true)) {
            return AjaxResult.error("住院充值功能正在升级维护，暂时使用，敬请谅解")
        }

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        val activePatient = request.getCurrentPatient()

        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.ZY
        return CcbPayServices.createUnifiedOrder(
            openid = activePatient.openId,
            clientIp = ip,
            amount = PaymentKit.fenToYuan(amount),
            serviceType = serviceType
        ) {
            wxPaymentService.createAndSave(activePatient, it, serviceType)
        }
    }

    /**
     * 快速充值
     * @param admissionNumber 住院号
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("ccbpayExpress")
    fun ccbpayExpress(
        @RequestParam admissionNumber: String,
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-zhuyuan-chongzhi", true)) {
            return AjaxResult.error("住院充值功能正在升级维护，暂时使用，敬请谅解")
        }

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        val zhuyuanPatientResponse = BsoftHaiServices.getZhuyuanPatientInfo(
            ZhuyuanPatientInfoForm(
                admissionNumber = admissionNumber
            )
        )
        if (!zhuyuanPatientResponse.isOk()) {
            return AjaxResult.error(zhuyuanPatientResponse.message)
        }

        val zhuyuanPatient: ZhuyuanPatientInfo = zhuyuanPatientResponse.zhuyuanPatientInfo
        val openid = request.getClientSession()!!.openId
        val mockPatient = Patient().also {
            it.openId = openid
            it.patientNo = zhuyuanPatient.patientId
            it.jzCardNo = zhuyuanPatient.admissionNumber  // 保存患者可见的住院号
            it.zhuyuanNo = zhuyuanPatient.admissionNo
        }
        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.ZY
        return CcbPayServices.createUnifiedOrder(
            openid = openid,
            clientIp = ip,
            amount = PaymentKit.fenToYuan(amount),
            serviceType = serviceType
        ) {
            wxPaymentService.createAndSave(mockPatient, it, serviceType)
        }
    }

    @PostMapping("refreshCcbOrder")
    fun refreshCcbOrder(@RequestParam zyPayNo: String): AjaxResult {
        synchronized(zyPayNo.intern()) {
            val payment = wxPaymentService.selectWxPaymentByZyPayNo(zyPayNo)
                ?: return AjaxResult.error("订单不存在[$zyPayNo]")

            if (!payment.wxPayNo.isNullOrBlank()) {
                // 本地订单状态已更新
                return AjaxResult.success(payment)
            }

            val queryOrderResponse = CcbPayServices.queryOrder(zyPayNo)
            wxPaymentService.updateOnQueryOrder(payment, queryOrderResponse)
            if (!queryOrderResponse.isSuccess) {
                return AjaxResult.error(queryOrderResponse.msg)
            }

            HisExt.recharge(payment.id)

            return AjaxResult.success(wxPaymentService.selectWxPaymentById(payment.id))
        }
    }

    /**
     * 支付宝充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("alipay")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun alipay(
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-zhuyuan-chongzhi", true)) {
            return AjaxResult.error("住院充值功能正在升级维护，暂停使用，敬请谅解")
        }

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        if (amount > 500000) {
            return AjaxResult.error("最大充值金额为5000元")
        }

        val activePatient = request.getCurrentPatient()

        val serviceType = ServiceType.ZY
        val outTradeNo = PaymentKit.newOrderId(serviceType)
        val remark = AlipaySetting.maMerchantName + '-' + serviceType.description
        val alipayPayment = AlipayPayment(
            activePatient,
            serviceType,
            outTradeNo,
            amount,
            remark
        )
        val ok = 1 == alipayPaymentService.insertAlipayPayment(alipayPayment)
        if (!ok) {
            return AjaxResult.error("操作失败，请稍后重试")
        }
        val alipayTradeCreateResponse: AlipayTradeCreateResponse = AlipayService.createOrder(
            totalAmount = PaymentKit.fenToYuan(amount),
            openid = activePatient.openId,
            outTradeNo = outTradeNo,
            subject = remark,
            body = remark
        )
        return if (alipayTradeCreateResponse.isSuccess) {
            AjaxResult.success(alipayTradeCreateResponse)
        } else {
            AjaxResult.error(alipayTradeCreateResponse.subCode + "-" + alipayTradeCreateResponse.subMsg)
        }
    }

    /**
     * 快速充值
     * @param admissionNumber 住院号
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("alipayExpress")
    fun alipayExpress(
        @RequestParam admissionNumber: String,
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-zhuyuan-chongzhi", true)) {
            return AjaxResult.error("住院充值功能正在升级维护，暂停使用，敬请谅解")
        }

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        if (amount > 500000) {
            return AjaxResult.error("最大充值金额为5000元")
        }

        val zhuyuanPatientResponse = BsoftHaiServices.getZhuyuanPatientInfo(
            ZhuyuanPatientInfoForm(
                admissionNumber = admissionNumber
            )
        )
        if (!zhuyuanPatientResponse.isOk()) {
            return AjaxResult.error(zhuyuanPatientResponse.message)
        }

        val zhuyuanPatient: ZhuyuanPatientInfo = zhuyuanPatientResponse.zhuyuanPatientInfo
        val openid = request.getClientSession()!!.openId
        val mockPatient = Patient().also {
            it.openId = openid
            it.patientNo = zhuyuanPatient.patientId
            it.jzCardNo = zhuyuanPatient.admissionNumber  // 保存患者可见的住院号
            it.zhuyuanNo = zhuyuanPatient.admissionNo
            it.idCardNo = zhuyuanPatient.patientIdCard
        }
        val serviceType = ServiceType.ZY
        val outTradeNo = PaymentKit.newOrderId(serviceType)
        val remark = AlipaySetting.maMerchantName + '-' + serviceType.description
        val alipayPayment = AlipayPayment(
            mockPatient,
            serviceType,
            outTradeNo,
            amount,
            remark
        )
        val ok = 1 == alipayPaymentService.insertAlipayPayment(alipayPayment)
        if (!ok) {
            return AjaxResult.error("操作失败，请稍后重试")
        }

        val alipayTradeCreateResponse: AlipayTradeCreateResponse = AlipayService.createOrder(
            totalAmount = PaymentKit.fenToYuan(amount),
            openid = openid,
            outTradeNo = outTradeNo,
            subject = remark,
            body = remark
        )
        return if (alipayTradeCreateResponse.isSuccess) {
            AjaxResult.success(alipayTradeCreateResponse)
        } else {
            AjaxResult.error(alipayTradeCreateResponse.subCode + "-" + alipayTradeCreateResponse.subMsg)
        }
    }

    @PostMapping("refreshAlipayOrder")
    @RequireSession(clientType = ClientType.ALIPAY)
    fun refreshAlipayOrder(@RequestParam outTradeNo: String): AjaxResult {
        synchronized(outTradeNo.intern()) {
            val payment = alipayPaymentService.selectAlipayPaymentByOutTradeNo(outTradeNo)
                ?: return AjaxResult.error("订单不存在[$outTradeNo]")

            val queryOrderResponse = AlipayService.queryOrder(payment.outTradeNo, "")
            alipayPaymentService.updateOnPay(payment, queryOrderResponse)

            HisExt.rechargeByAlipay(payment.id)

            return AjaxResult.success(alipayPaymentService.selectAlipayPaymentById(payment.id))
        }
    }

    @PostMapping("refund")
    fun refund(@RequestBody refundForm: ZhuyuanRefundForm): AjaxResult {
        return when (refundForm.payType) {
            "21" -> refundWeixin(refundForm)
            "15" -> refundAlipay(refundForm)
            else -> AjaxResult.error("不支持的支付方式[${refundForm.payType}]")
        }
    }

    private fun refundWeixin(refundForm: ZhuyuanRefundForm): AjaxResult {
        val cents: Long = PaymentKit.yuanToFen(refundForm.amount)
        if (cents <= 0L) {
            return AjaxResult.error("退款金额必须大于0")
        }

        val payment: WxPayment = wxPaymentService.selectOneByWxPayNo(refundForm.orderNumber)
            ?: return AjaxResult.error("订单不存在[${refundForm.orderNumber}]")

        if (!payment.isZhuyuan) {
            return AjaxResult.error("此订单不是住院订单")
        }

        if (payment.hisTradeStatus != Constants.RECHARGE_OK
            && !CharSequenceUtil.contains(payment.hisTradeStatus, "已结算或已经注销")
        ) {
            return AjaxResult.error("此订单状态异常，不支持退款")
        }

        if (cents > payment.unrefund) {
            return AjaxResult.error("退款金额超出订单限制")
        }

        if (cents > wxPaymentService.calculateNetInAmount(LocalDate.now())) {
            return AjaxResult.error("商户余额不足，建议您两小时后重试")
        }

        val refund: WxRefund = wxRefundService.createAndSave(
            payment,
            refundForm.amount,
            PaymentKit.newOrderId(payment.serviceType),
            Constants.REFUND_OK
        )
        wxPaymentService.updateOnRefund(payment, cents)

        val ccbRefundResponse: RefundResponse = CcbPayServices.refund(
            space.lzhq.ph.pay.ccb.bean.RefundParams(
                order = refund.zyPayNo,
                refundCode = refund.zyRefundNo,
                money = PaymentKit.fenToYuan(refund.amount).toDouble()
            )
        )

        // 更新退款记录
        wxRefundService.updateOnCcbRefund(refund, ccbRefundResponse)

        if (ccbRefundResponse.isSuccess || ccbRefundResponse.msg == "交易状态不确定") {
            return AjaxResult.success(
                mapOf(
                    "patientId" to refund.patientNo,
                    "zhuyuanNo" to refund.zhuyuanNo,
                    "totalAmount" to PaymentKit.fenToYuan(refund.totalAmount),
                    "refundAmount" to PaymentKit.fenToYuan(refund.amount),
                    "zyPayNo" to refund.zyPayNo,
                    "zyRefundNo" to refund.zyRefundNo,
                    "tradeNo" to refund.wxPayNo,
                )
            )
        } else {
            DingRobot.sendText(
                """
                        ***** 微信退款失败 *****
                        下单时间：【${DateUtil.formatDateTime(refund.createTime)}】
                        就诊卡号：【${refund.jzCardNo}】
                        掌医退款单号：【${refund.zyRefundNo}】
                        订单金额：【${PaymentKit.fenToYuan(refund.amount)}】
                        错误信息：【${ccbRefundResponse.msg}】                        
                    """.trimIndent()
            )
            return AjaxResult.error(ccbRefundResponse.msg)
        }
    }

    private fun refundAlipay(refundForm: ZhuyuanRefundForm): AjaxResult {
        val cents: Long = PaymentKit.yuanToFen(refundForm.amount)
        if (cents <= 0L) {
            return AjaxResult.error("退款金额必须大于0")
        }

        val payment: AlipayPayment = alipayPaymentService.selectAlipayPaymentByOutTradeNo(refundForm.orderNumber)
            ?: return AjaxResult.error("订单不存在[${refundForm.orderNumber}]")

        if (!payment.isZhuyuan) {
            return AjaxResult.error("此订单不是住院订单")
        }

        if (payment.hisTradeStatus != Constants.RECHARGE_OK) {
            return AjaxResult.error("此订单状态异常，不支持退款")
        }

        if (cents > payment.unrefund) {
            return AjaxResult.error("退款金额超出订单限制")
        }

        val refund = AlipayRefund(payment, refundForm)
        alipayRefundService.insertAlipayRefund(refund)

        payment.exrefund += cents
        payment.unrefund = payment.totalAmount - payment.exrefund
        alipayPaymentService.updateAlipayPayment(payment)

        alipayRefundService.refundAlipay(refund)
        return AjaxResult.success(
            mapOf(
                "patientId" to refund.patientId,
                "zhuyuanNo" to refund.zhuyuanNo,
                "totalAmount" to PaymentKit.fenToYuan(payment.totalAmount),
                "refundAmount" to PaymentKit.fenToYuan(refund.amount),
                "zyPayNo" to refund.outTradeNo,
                "zyRefundNo" to refund.outRefundNo,
                "tradeNo" to refund.tradeNo,
            )
        )
    }
}
