package space.lzhq.ph.controller

import com.github.binarywang.wxpay.service.WxPayService
import com.ruoyi.common.annotation.Log
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.domain.WxRefund
import space.lzhq.ph.pay.ccb.CcbPayServices
import space.lzhq.ph.pay.ccb.bean.QueryOrderResponse
import space.lzhq.ph.service.IWxRefundService

/**
 * 退款记录Controller
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Suppress("DuplicatedCode", "SpringMVCViewInspection")
@Controller
@RequestMapping("/ph/refund")
class WxRefundAdminController : BaseController() {
    private val prefix = "ph/refund"

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var wxPayService: WxPayService

    @RequiresPermissions("ph:refund:view")
    @GetMapping
    fun refund(): String {
        return "$prefix/refund"
    }

    /**
     * 查询退款记录列表
     */
    @RequiresPermissions("ph:refund:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxRefund: WxRefund): TableDataInfo {
        startPage()
        val list = wxRefundService.selectWxRefundList(wxRefund)
        return getDataTable(list)
    }

    /**
     * 导出退款记录列表
     */
    @RequiresPermissions("ph:refund:export")
    @Log(title = "退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(wxRefund: WxRefund): AjaxResult {
        val list = wxRefundService.selectWxRefundList(wxRefund)
        val util = ExcelUtil(WxRefund::class.java)
        return util.exportExcel(list, "refund")
    }

    /**
     * 查询订单
     */
    @RequiresPermissions("ph:refund:queryCCBOrder")
    @GetMapping("/queryCCBOrder/{id}")
    @ResponseBody
    fun queryCCBOrder(@PathVariable id: Long): AjaxResult {
        return try {
            val wxRefund = wxRefundService.selectWxRefundById(id)!!
            val queryOrderResponse: QueryOrderResponse = CcbPayServices.queryOrder(wxRefund.zyRefundNo)
            AjaxResult.success(queryOrderResponse)
        } catch (e: Exception) {
            AjaxResult.success(e.message)
        }
    }

    /**
     * 申请建行退款
     */
    @RequiresPermissions("ph:refund:requestCCBRefund")
    @PostMapping("/requestCCBRefund/{id}")
    @ResponseBody
    fun requestCCBRefund(@PathVariable id: Long): AjaxResult {
        val wxRefund = wxRefundService.selectWxRefundById(id)!!
        if (!wxRefund.hisTradeStatus.startsWith(Constants.REFUND_OK)
            || Constants.REFUND_OK == wxRefund.wxTradeStatus
            || Constants.MANUAL_INIT != wxRefund.manualRefundState
        ) {
            return AjaxResult.error("此订单不能申请微信退款")
        }
        wxRefund.manualRefundState = Constants.MANUAL_REQUESTED
        val ok = wxRefundService.updateWxRefund(wxRefund) == 1
        return if (ok) AjaxResult.success() else AjaxResult.error()
    }

    /**
     * 放行建行退款
     */
    @RequiresPermissions("ph:refund:approveCCBRefund")
    @PostMapping("/approveCCBRefund/{id}")
    @ResponseBody
    fun approveCCBRefund(@PathVariable id: Long): AjaxResult {
        val wxRefund = wxRefundService.selectWxRefundById(id)!!
        if (!wxRefund.hisTradeStatus.startsWith(Constants.REFUND_OK)
            || Constants.REFUND_OK == wxRefund.wxTradeStatus
            || Constants.MANUAL_REQUESTED != wxRefund.manualRefundState
        ) {
            return AjaxResult.error("此订单不能放行微信退款")
        }

        // 发起建行退款
        val refundResponse = CcbPayServices.refund(
            space.lzhq.ph.pay.ccb.bean.RefundParams(
                order = wxRefund.zyPayNo,
                refundCode = wxRefund.zyRefundNo,
                money = PaymentKit.fenToYuan(wxRefund.amount).toDouble()
            )
        )

        // 更新退款记录
        wxRefundService.updateOnCcbRefund(wxRefund, refundResponse)
        return AjaxResult.success(refundResponse)
    }

}