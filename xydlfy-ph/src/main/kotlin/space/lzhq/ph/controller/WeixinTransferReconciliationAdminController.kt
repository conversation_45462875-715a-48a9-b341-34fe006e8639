package space.lzhq.ph.controller

import cn.idev.excel.FastExcel
import com.github.binarywang.wxpay.bean.request.WxPayApplyFundFlowBillV3Request
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.date.DatePattern
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.*
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.HisSelfPayOrder
import space.lzhq.ph.domain.ReconciliationSearchForm
import space.lzhq.ph.domain.WeixinTransferReconciliationDetail
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IHisSelfPayOrderService
import space.lzhq.ph.service.IWeixinTransferReconciliationDetailService
import space.lzhq.ph.service.IWeixinTransferReconciliationService
import java.nio.charset.StandardCharsets
import java.time.LocalDate

@Controller
@RequestMapping("/ph/weixin_transfer_reconciliation")
class WeixinTransferReconciliationAdminController : BaseController() {

    private val log: Logger = LoggerFactory.getLogger(WeixinTransferReconciliationAdminController::class.java)

    private val prefix = "ph/weixin_transfer_reconciliation"

    @Autowired
    private lateinit var service: IWeixinTransferReconciliationService

    @Autowired
    private lateinit var reconciliationDetailService: IWeixinTransferReconciliationDetailService

    @Autowired
    private lateinit var hisSelfPayOrderService: IHisSelfPayOrderService

    @RequiresPermissions("ph:weixin_transfer_reconciliation:view")
    @GetMapping
    fun index(
        month: String?,
        modelMap: ModelMap
    ): String {
        modelMap.addAttribute("user", sysUser)
        modelMap.addAttribute("month", month ?: "")
        return "$prefix/index"
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:view")
    @PostMapping("/page")
    @ResponseBody
    fun page(searchForm: ReconciliationSearchForm): TableDataInfo {
        startPage("date ASC")
        val list = service.listBySearchForm(searchForm)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:downloadWeixinBill")
    @GetMapping("/downloadWeixinBill/{date}")
    fun downloadWeixinBill(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<Resource> {
        val billFile = WeixinExt.getOrDownloadFundBillFile(
            billDate = date,
            accountType = WxPayApplyFundFlowBillV3Request.AccountType.OPERATION
        )
        check(billFile != null && billFile.exists()) { "账单文件不存在" }

        val resource = FileSystemResource(billFile)
        val fileName = billFile.name
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .contentLength(resource.contentLength())
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(resource)
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:downloadHisBill")
    @GetMapping("/downloadHisBill/{date}")
    fun downloadHisBill(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val hisOrders = hisSelfPayOrderService.listForWeixinTransfer(date).sortedBy { it.tradeTime }

        val fileName = "微信商家转账_HIS账单_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, HisSelfPayOrder::class.java).sheet(0, "Sheet1").doWrite(hisOrders)
                } catch (e: Exception) {
                    log.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:reconcile")
    @PostMapping("/reconcile/{date}")
    @ResponseBody
    fun reconcile(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate): AjaxResult {
        val ok = service.reconcile(date)
        return toAjax(ok)
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:downloadReconciliationDetails")
    @GetMapping("/downloadReconciliationDetails/{date}")
    fun downloadReconciliationDetails(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val reconciliationDetails = reconciliationDetailService.listByBillDate(date).sorted()

        val fileName = "微信商家转账_对账明细_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, WeixinTransferReconciliationDetail::class.java)
                        .sheet(0, "Sheet1")
                        .doWrite(reconciliationDetails)
                } catch (e: Exception) {
                    log.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:downloadNotBalancedReconciliationDetails")
    @GetMapping("/downloadNotBalancedReconciliationDetails/{date}")
    fun downloadNotBalancedReconciliationDetails(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        var reconciliationDetails = reconciliationDetailService.listNotBalanced(date).sorted()

        val fileName = "微信商家转账_错单明细_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, WeixinTransferReconciliationDetail::class.java)
                        .sheet(0, "Sheet1")
                        .doWrite(reconciliationDetails)
                } catch (e: Exception) {
                    log.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:weixin_transfer_reconciliation:view")
    @GetMapping("/chartData")
    @ResponseBody
    fun chartData(
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate
    ): AjaxResult {
        val chartData = service.getChartData(startDate, endDate)
        return AjaxResult.success(chartData)
    }
}