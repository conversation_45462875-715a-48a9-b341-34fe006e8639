package space.lzhq.ph.controller

import com.baomidou.mybatisplus.core.metadata.IPage
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.domain.OutpatientStopClinic
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.OutpatientStopClinicService
import space.lzhq.ph.vo.OutpatientStopClinicVO

/**
 * 门诊停诊消息控制器
 */
@RestController
@RequestMapping("/api/stop-clinic-messages")
class OutpatientStopClinicController(
    private val outpatientStopClinicService: OutpatientStopClinicService
) : BaseController() {

    /**
     * 分页获取患者的停诊消息
     *
     * @param page 页码
     * @param size 每页大小
     * @return 分页数据
     */
    @GetMapping("/my")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPatientStopClinicMessages(
        @RequestParam(defaultValue = "1") page: Long,
        @RequestParam(defaultValue = "100") size: Long
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val pageResult: IPage<OutpatientStopClinic> =
            outpatientStopClinicService.getPageByPatientId(activePatient.patientNo, page, size)
        val voPage: IPage<OutpatientStopClinicVO> = pageResult.convert(::convertToVO)
        return AjaxResult.success(voPage)
    }

    /**
     * 获取停诊消息详情
     *
     * @param id 消息ID
     * @return 消息详情
     */
    @GetMapping("/{id}")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getStopClinicMessage(@PathVariable id: Long): AjaxResult {
        val stopClinic = outpatientStopClinicService.getById(id)
            ?: return AjaxResult.error("指定的消息不存在")
        if (stopClinic.sourcePatientId != request.getCurrentPatient().patientNo) {
            return AjaxResult.error("您无权查看此消息")
        }
        return AjaxResult.success(convertToVO(stopClinic))
    }

    /**
     * 标记消息为已读
     *
     * @param id 消息ID
     * @return 操作结果
     */
    @PutMapping("/{id}/read")
    fun markAsRead(@PathVariable id: Long): AjaxResult =
        toAjax(outpatientStopClinicService.markAsRead(id))

    /**
     * 将实体对象转换为VO对象
     *
     * @param stopClinic 实体对象
     * @return VO对象
     */
    private fun convertToVO(stopClinic: OutpatientStopClinic): OutpatientStopClinicVO =
        OutpatientStopClinicVO(
            id = stopClinic.id,
            scheduleMark = stopClinic.scheduleMark,
            appointmentId = stopClinic.appointmentId,
            registeredDeptName = stopClinic.registeredDeptName,
            registeredDoctorName = stopClinic.registeredDoctorName,
            registeredDateTime = stopClinic.registeredDateTime,
            appointmentDateTime = stopClinic.appointmentDateTime,
            messageContent = stopClinic.messageContent,
            isRead = stopClinic.isRead,
            createTime = stopClinic.createTime
        )
}