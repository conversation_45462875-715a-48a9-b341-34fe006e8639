package space.lzhq.ph.controller

import cn.idev.excel.FastExcel
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.date.DatePattern
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.*
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.HisSelfPayOrder
import space.lzhq.ph.domain.ReconciliationSearchForm
import space.lzhq.ph.domain.ZhangyiWeixinReconciliationDetail
import space.lzhq.ph.service.CCBPayServiceExt
import space.lzhq.ph.service.IHisSelfPayOrderService
import space.lzhq.ph.service.IZhangyiWeixinReconciliationDetailService
import space.lzhq.ph.service.IZhangyiWeixinReconciliationService
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.time.LocalDate

@Controller
@RequestMapping("/ph/zhangyi_weixin_reconciliation")
class ZhangyiWeixinReconciliationAdminController : BaseController() {

    private val log: Logger = LoggerFactory.getLogger(ZhangyiWeixinReconciliationAdminController::class.java)

    private val prefix = "ph/zhangyi_weixin_reconciliation"

    @Autowired
    private lateinit var service: IZhangyiWeixinReconciliationService

    @Autowired
    private lateinit var reconciliationDetailService: IZhangyiWeixinReconciliationDetailService

    @Autowired
    private lateinit var hisSelfPayOrderService: IHisSelfPayOrderService

    @Autowired
    private lateinit var ccbPayServiceExt: CCBPayServiceExt

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:view")
    @GetMapping
    fun index(
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) @RequestParam(required = false) date: LocalDate?,
        modelMap: ModelMap
    ): String {
        modelMap.addAttribute("user", sysUser)
        modelMap.addAttribute("startDate", date?.format(DatePattern.NORM_DATE_FORMATTER) ?: "")
        modelMap.addAttribute("endDate", date?.format(DatePattern.NORM_DATE_FORMATTER) ?: "")
        return "$prefix/index"
    }

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:view")
    @PostMapping("/page")
    @ResponseBody
    fun page(searchForm: ReconciliationSearchForm): TableDataInfo {
        startPage("date DESC")
        val list = service.listBySearchForm(searchForm)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:downloadWeixinBill")
    @GetMapping("/downloadWeixinBill/{date}")
    fun downloadWeixinBill(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<Resource> {
        val zipBillPath = ccbPayServiceExt.downloadBill(date)
        check(zipBillPath != null && Files.exists(zipBillPath)) { "账单文件不存在: $zipBillPath" }

        val resource = FileSystemResource(zipBillPath)
        val fileName = zipBillPath.fileName.toString()
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .contentLength(resource.contentLength())
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(resource)
    }

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:downloadHisBill")
    @GetMapping("/downloadHisBill/{date}")
    fun downloadHisBill(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val hisOrders = hisSelfPayOrderService.listForZhangyiWeixin(date).sortedBy { it.tradeTime }

        val fileName = "微信小程序_HIS账单_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, HisSelfPayOrder::class.java).sheet(0, "Sheet1").doWrite(hisOrders)
                } catch (e: Exception) {
                    log.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:reconcile")
    @PostMapping("/reconcile/{date}")
    @ResponseBody
    fun reconcile(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate): AjaxResult {
        val ok = service.reconcile(date)
        return toAjax(ok)
    }

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:downloadReconciliationDetails")
    @GetMapping("/downloadReconciliationDetails/{date}")
    fun downloadReconciliationDetails(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        val reconciliationDetails = reconciliationDetailService.listByBillDate(date).sorted()

        val fileName = "微信小程序_对账明细_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, ZhangyiWeixinReconciliationDetail::class.java)
                        .sheet(0, "Sheet1")
                        .doWrite(reconciliationDetails)
                } catch (e: Exception) {
                    log.error(e.message, e)
                }
            })
    }

    @RequiresPermissions("ph:zhangyi_weixin_reconciliation:downloadNotBalancedReconciliationDetails")
    @GetMapping("/downloadNotBalancedReconciliationDetails/{date}")
    fun downloadNotBalancedReconciliationDetails(
        @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate
    ): ResponseEntity<StreamingResponseBody> {
        var reconciliationDetails = reconciliationDetailService.listNotBalanced(date).sorted()

        val fileName = "微信小程序_错单明细_${date.format(DatePattern.NORM_DATE_FORMATTER)}.xlsx"
        val mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val contentDisposition = ContentDisposition.attachment().filename(fileName, StandardCharsets.UTF_8).build()

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .body(StreamingResponseBody {
                try {
                    FastExcel.write(it, ZhangyiWeixinReconciliationDetail::class.java)
                        .sheet(0, "Sheet1")
                        .doWrite(reconciliationDetails)
                } catch (e: Exception) {
                    log.error(e.message, e)
                }
            })
    }

}
