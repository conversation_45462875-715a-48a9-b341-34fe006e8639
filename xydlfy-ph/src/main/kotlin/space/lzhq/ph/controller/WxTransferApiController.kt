package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import org.mospital.bsoft.hai.BsoftHaiServices
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.PatientInfoToken
import space.lzhq.ph.domain.Session
import space.lzhq.ph.domain.WxTransfer
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IWxTransferService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/wx-transfer")
class WxTransferApiController : BaseController() {

    @Autowired
    private lateinit var wxTransferService: IWxTransferService

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @GetMapping("list")
    @RequireSession(ClientType.WEIXIN)
    fun list(): AjaxResult {
        val session: Session = request.getClientSession()!!
        val transfers = wxTransferService.listByOpenId(session.openId)
            .filter { it.hisRefundSuccess == true }
            .sortedByDescending { it.createTime }
            .map { it.maskSensitive() }

        return AjaxResult.success(transfers)
    }

    @PostMapping("apply")
    @RequireSession(ClientType.WEIXIN)
    fun apply(
        patientInfoToken: String,
        amount: BigDecimal
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wx-transfer-enabled", true)) {
            return AjaxResult.error("余额清退功能正在升级维护，暂停使用，敬请谅解")
        }

        if (amount <= 0.1.toBigDecimal()) {
            return AjaxResult.error("退款金额不能小于0.10")
        }

        if (amount > 200.00.toBigDecimal()) {
            return AjaxResult.error("退款金额不能大于200.00")
        }

        val session: Session = request.getClientSession()!!

        val today = LocalDate.now()

        // 检查单日单用户累计退款金额不得大于2000元
        val dailyUserSum = wxTransferService.sumAmountByOpenIdAndDate(session.openId, today)
        val dailyUserLimit = 2000.00.toBigDecimal()
        if (dailyUserSum.add(amount) > dailyUserLimit) {
            return AjaxResult.error("单日累计退款金额不能超过2000.00元，您今日已申请退款${dailyUserSum}元")
        }

        // 检查商户单日累计退款金额不得大于50000元
        val dailySum = wxTransferService.sumAmountByDate(today)
        val dailyLimit = 48000.00.toBigDecimal() // 预留2000元的缓冲
        if (dailySum.add(amount) > dailyLimit) {
            return AjaxResult.error("本日商户累计退款金额已达上限，请明日再试")
        }

        // 检查用户是否有转账状态不为SUCCESS的转账记录
        if (wxTransferService.hasUnsuccessfulTransfers(session.openId)) {
            return AjaxResult.error("您有未成功的转账记录，不能发起新的申请")
        }

        val patientInfoToken: PatientInfoToken = PatientInfoToken.parseToken(patientInfoToken)
            ?: return AjaxResult.error("患者信息不正确")

        val patientInfoResponse = BsoftHaiServices.getPatientInfoByPatientId(patientInfoToken.patientId)
        if (!patientInfoResponse.isOk()) {
            return AjaxResult.error("获取患者信息失败")
        }

        val patientInfo = patientInfoResponse.patientInfo
        if (patientInfo.cardBalance.toBigDecimal() < amount) {
            return AjaxResult.error("账户余额已发生变化，请重新申请")
        }

        val outBillNo = PaymentKit.newOrderId(ServiceType.TF)
        val now = LocalDateTime.now()
        val wxTransfer: WxTransfer = WxTransfer.builder()
            .openId(session.openId)
            .userIdCardNo(patientInfoToken.patientIdCardNo)
            .userName(patientInfoToken.patientName)
            .userMobile(patientInfoToken.patientMobile)
            .patientIdCardNo(patientInfoToken.patientIdCardNo)
            .patientName(patientInfoToken.patientName)
            .patientMobile(patientInfoToken.patientMobile)
            .patientCardNo(patientInfoToken.patientCardNo)
            .patientId(patientInfoToken.patientId)
            .amount(amount)
            .outBillNo(outBillNo)
            .createTime(now)
            .build()
        wxTransferService.save(wxTransfer)

        wxTransferService.menzhenRefund(wxTransfer)

        if (!wxTransfer.hisRefundSuccess) {
            return AjaxResult.error(wxTransfer.hisRefundFailReason)
        }

        wxTransferService.requestTransfer(wxTransfer)
        return AjaxResult.success(wxTransfer.maskSensitive())
    }

    @PostMapping("refresh-order")
    @RequireSession(ClientType.WEIXIN)
    fun refreshOrder(id: String): AjaxResult {
        if (id.isBlank()) {
            return AjaxResult.error("订单ID不能为空")
        }

        val session: Session = request.getClientSession()!!

        val wxTransfer = wxTransferService.lambdaQuery()
            .eq(WxTransfer::getId, id)
            .eq(WxTransfer::getOpenId, session.openId)
            .one()
            ?: return AjaxResult.error("转账记录不存在")

        try {
            wxTransferService.updateTransferState(wxTransfer)
            return AjaxResult.success(wxTransfer.maskSensitive())
        } catch (e: Exception) {
            logger.error("刷新转账状态失败，ID：{}，错误：{}", id, e.message, e)
            return AjaxResult.error("刷新转账状态失败")
        }
    }

}