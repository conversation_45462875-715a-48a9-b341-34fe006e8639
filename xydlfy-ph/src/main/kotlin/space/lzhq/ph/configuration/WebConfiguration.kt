package space.lzhq.ph.configuration

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import space.lzhq.ph.interceptor.*

@Configuration
open class WebConfiguration : WebMvcConfigurer {

    @Autowired
    private lateinit var wxSessionInterceptor: SessionInterceptor

    @Autowired
    private lateinit var activePatientInterceptor: ActivePatientInterceptor

    @Autowired
    private lateinit var pscNurseSessionInterceptor: PscNurseSessionInterceptor

    @Autowired
    private lateinit var pscCarerSessionInterceptor: PscCarerSessionInterceptor

    @Autowired
    private lateinit var traceIdInterceptor: TraceIdInterceptor

    companion object {
        const val ANY_PATH_PATTERN = "/**"
        const val API_PATH_PATTERN = "/api/**"
        const val PSC_PATH_PATTERN = "/api/psc/**"
    }

    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(wxSessionInterceptor).addPathPatterns(API_PATH_PATTERN)
        registry.addInterceptor(activePatientInterceptor).addPathPatterns(API_PATH_PATTERN)
            .excludePathPatterns(PSC_PATH_PATTERN)
        registry.addInterceptor(pscNurseSessionInterceptor).addPathPatterns(PSC_PATH_PATTERN)
        registry.addInterceptor(pscCarerSessionInterceptor).addPathPatterns(PSC_PATH_PATTERN)
        registry.addInterceptor(traceIdInterceptor).addPathPatterns(ANY_PATH_PATTERN)
    }

}
