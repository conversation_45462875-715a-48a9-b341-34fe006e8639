package space.lzhq.ph.ext

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import com.alibaba.fastjson.JSON
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult
import com.github.binarywang.wxpay.bean.request.*
import com.github.binarywang.wxpay.bean.result.*
import com.github.binarywang.wxpay.config.WxPayConfig
import com.github.binarywang.wxpay.constant.WxPayConstants
import com.github.binarywang.wxpay.exception.WxPayException
import com.github.binarywang.wxpay.service.WxInsurancePayService
import com.github.binarywang.wxpay.service.WxPayService
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.utils.spring.SpringUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import me.chanjar.weixin.common.service.WxOcrService
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.io.file.FileUtil
import org.dromara.hutool.core.text.StrUtil
import org.dromara.hutool.http.client.HttpDownloader
import org.mospital.common.StringKit
import org.mospital.wecity.mip.WeixinMipSetting
import org.slf4j.LoggerFactory
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.*
import space.lzhq.ph.pay.ccb.CcbPayServices
import space.lzhq.ph.service.IMipWxOrderService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IWxRefundService
import space.lzhq.ph.service.IWxSubscribeMessageService
import java.io.File
import java.io.InputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Suppress("unused")
object WeixinExt {

    private val log = LoggerFactory.getLogger(WeixinExt::class.java)

    fun createUnifiedOrderRequest(
        serviceType: ServiceType,
        openid: String,
        amount: Int,
        ip: String,
        remark: String = "",
    ): WxPayUnifiedOrderRequest {
        val now: Date = DateTime.now()
        val startTimeString: String = DateUtil.format(now, DatePattern.PURE_DATETIME_PATTERN)
        // 10分钟后过期
        val expireTimeString: String =
            DateUtil.format(DateUtil.offsetMinute(now, 10), DatePattern.PURE_DATETIME_PATTERN)

        val bodyString: String = "${Constants.MERCHANT_NAME}-" + when (serviceType) {
            ServiceType.MZ -> "门诊预交金"
            ServiceType.ZY -> "住院预交金"
            ServiceType.ZJ -> "诊间缴费"
            else -> throw UnsupportedOperationException("向统一下单服务提供了不正确的ServiceType：${serviceType.name}")
        } + (if (remark.isNotBlank()) "-$remark" else "")

        return WxPayUnifiedOrderRequest.newBuilder()
            .tradeType(WxPayConstants.TradeType.JSAPI)
            .body(bodyString)
            .outTradeNo(PaymentKit.newOrderId(serviceType))
            .totalFee(amount)
            .spbillCreateIp(ip)
            .timeStart(startTimeString)
            .timeExpire(expireTimeString)
            .notifyUrl(Constants.ON_WX_PAY)
            .openid(openid)
            .subOpenid(openid)
            .build()
    }

    fun pay(
        serviceType: ServiceType,
        amount: Int,
        ip: String,
        patient: Patient,
        remark: String = "",
        callback: (WxPayUnifiedOrderRequest, WxPayMpOrderResult) -> Unit,
    ): WxPayMpOrderResult {
        val unifiedOrderRequest: WxPayUnifiedOrderRequest = createUnifiedOrderRequest(
            serviceType = serviceType,
            openid = patient.openId,
            amount = amount,
            ip = ip,
            remark = remark
        )
        val wxPayService = SpringUtils.getBean(WxPayService::class.java)
        val orderResult: WxPayMpOrderResult = wxPayService.createOrder(unifiedOrderRequest)

        CoroutineScope(Dispatchers.IO).launch {
            callback(unifiedOrderRequest, orderResult)
        }

        return orderResult
    }

    /**
     * 退款
     * @param totalAmount 充值金额
     * @param amount 退款金额
     * @param wxPayNo 微信充值订单号
     * @param zyRefundNo 掌医退款订单号
     */
    @Throws(WxPayException::class)
    fun refund(
        totalAmount: Int,
        amount: Int,
        wxPayNo: String,
        zyRefundNo: String,
    ): WxPayRefundResult {
        val refundRequest: WxPayRefundRequest =
            WxPayRefundRequest.newBuilder()
                .transactionId(wxPayNo)
                .outRefundNo(zyRefundNo)
                .totalFee(totalAmount)
                .refundFee(amount)
                .notifyUrl(Constants.ON_WX_REFUND)
                .build()
        val wxPayService = SpringUtils.getBean(WxPayService::class.java)
        return wxPayService.refund(refundRequest)
    }

    fun downloadAndSaveWxBill(billDate: LocalDate): WxPayBillResult? {
        return try {
            val wxPayService = SpringUtils.getBean(WxPayService::class.java)!! as WxPayServiceImpl
            wxPayService.downloadAndSaveBill(billDate = billDate)
        } catch (e: WxPayException) {
            if (e.returnMsg == "No Bill Exist") {
                FileUtil.writeUtf8String("当日无账单", buildBillPath(billDate))
                val wxPayBillResult = WxPayBillResult()
                wxPayBillResult.totalRecord = "0"
                wxPayBillResult.totalFee = "0.00"
                wxPayBillResult.totalAmount = "0.00"
                wxPayBillResult.totalRefundFee = "0.00"
                wxPayBillResult
            } else {
                throw e
            }
        }
    }

    fun downloadAndSaveMipBill(billDate: LocalDate): File? {
        return try {
            val wxInsurancePayDownloadBillResult = getWxInsurancePayService().downloadBill(
                WxInsurancePayDownloadBillRequest(
                    billDate = billDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    billType = "ALL"
                )
            )
            val file = File(buildMipBillPath(billDate))
            HttpDownloader.downloadFile(wxInsurancePayDownloadBillResult.downloadUrl, file)
            file
        } catch (e: Exception) {
            null
        }
    }

    private fun downloadFundFlowBill(billDate: LocalDate, accountType: String): InputStream {
        val applyFundFlowBillV3Request = WxPayApplyFundFlowBillV3Request()
        applyFundFlowBillV3Request.billDate = billDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        applyFundFlowBillV3Request.accountType = accountType

        val wxPayService = SpringUtils.getBean(WxPayService::class.java)
        val applyFundFlowBill: WxPayApplyBillV3Result = wxPayService.applyFundFlowBill(applyFundFlowBillV3Request)
        return wxPayService.downloadV3(applyFundFlowBill.downloadUrl)
    }

    fun downloadAndSaveFundFlowBill(billDate: LocalDate, accountType: String): File? {
        return try {
            val inputStream = downloadFundFlowBill(billDate, accountType)
            val file = File(buildFundBillPath(billDate, accountType))
            FileUtil.writeFromStream(inputStream, file, true)
            file
        } catch (e: Exception) {
            log.error(e.message, e)
            null
        }
    }

    fun getOrDownloadBillFile(billDate: LocalDate): File {
        val path: String = buildBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveWxBill(billDate)
        return File(path)
    }

    fun getOrDownloadMipBillFile(billDate: LocalDate): File {
        val path: String = buildMipBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveMipBill(billDate)
        return File(path)
    }

    fun getOrDownloadFundBillFile(billDate: LocalDate, accountType: String): File? {
        val path: String = buildFundBillPath(billDate, accountType)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        return downloadAndSaveFundFlowBill(billDate, accountType)
    }

    fun getOrDownloadFundBill(billDate: LocalDate, accountType: String): WxPayFundFlowResult? {
        val file = getOrDownloadFundBillFile(billDate, accountType) ?: return null
        val string = FileUtil.readUtf8String(file)
        return parseFundFlowBill(string)
    }

    /**
     * 解析资金账单文件内容为WxPayFundFlowResult对象
     * @param responseContent 账单文件内容
     * @return WxPayFundFlowResult对象
     */
    private fun parseFundFlowBill(responseContent: String): WxPayFundFlowResult {
        val wxPayFundFlowResult = WxPayFundFlowResult()
        val totalFundCount = "资金流水总笔数"

        var listStr = ""
        var objStr = ""

        // 分割数据为交易列表和汇总信息两部分
        if (responseContent.isNotBlank() && responseContent.contains(totalFundCount)) {
            listStr = responseContent.substring(0, responseContent.indexOf(totalFundCount))
            objStr = responseContent.substring(responseContent.indexOf(totalFundCount))
        }

        /*
        * 记账时间:2018-02-01 04:21:23 微信支付业务单号:50000305742018020103387128253 资金流水单号:1900009231201802015884652186 业务名称:退款
        * 业务类型:退款 收支类型:支出 收支金额（元）:0.02 账户结余（元）:0.17 资金变更提交申请人:system 备注:缺货 业务凭证号:REF4200000068201801293084726067
        * 参考以上格式进行取值
        */
        val wxPayFundFlowBaseResultList = mutableListOf<WxPayFundFlowBaseResult>()

        // 去掉逗号，用空格替代
        val newStr = listStr.replace(",", " ")
        // 数据分组
        val tempStr = newStr.split("`")
        // 分组标题
        val t = tempStr[0].split(" ")
        // 计算循环次数
        val j = tempStr.size / t.size
        // 记录数组下标
        var k = 1

        for (i in 0 until j) {
            val wxPayFundFlowBaseResult = WxPayFundFlowBaseResult()

            wxPayFundFlowBaseResult.billingTime = tempStr[k].trim()
            wxPayFundFlowBaseResult.bizTransactionId = tempStr[k + 1].trim()
            wxPayFundFlowBaseResult.fundFlowId = tempStr[k + 2].trim()
            wxPayFundFlowBaseResult.bizName = tempStr[k + 3].trim()
            wxPayFundFlowBaseResult.bizType = tempStr[k + 4].trim()
            wxPayFundFlowBaseResult.financialType = tempStr[k + 5].trim()
            wxPayFundFlowBaseResult.financialFee = tempStr[k + 6].trim()
            wxPayFundFlowBaseResult.accountBalance = tempStr[k + 7].trim()
            wxPayFundFlowBaseResult.fundApplicant = tempStr[k + 8].trim()
            wxPayFundFlowBaseResult.memo = tempStr[k + 9].trim()
            wxPayFundFlowBaseResult.bizVoucherId = tempStr[k + 10].trim()

            wxPayFundFlowBaseResultList.add(wxPayFundFlowBaseResult)
            k += t.size
        }

        wxPayFundFlowResult.wxPayFundFlowBaseResultList = wxPayFundFlowBaseResultList

        /*
        * 资金流水总笔数,收入笔数,收入金额,支出笔数,支出金额 `20.0,`17.0,`0.35,`3.0,`0.18
        * 参考以上格式进行取值
        */
        val totalStr = objStr.replace(",", " ")
        val totalTempStr = totalStr.split("`")
        wxPayFundFlowResult.totalRecord = totalTempStr[1]
        wxPayFundFlowResult.incomeRecord = totalTempStr[2]
        wxPayFundFlowResult.incomeAmount = totalTempStr[3]
        wxPayFundFlowResult.expenditureRecord = totalTempStr[4]
        wxPayFundFlowResult.expenditureAmount = totalTempStr[5]

        return wxPayFundFlowResult
    }

    /**
     * 发送订阅消息
     */
    fun sendSubscribeMessage(
        messageId: String?,
        messagePage: String?,
        messageDataList: List<WxMaSubscribeMessage.MsgData>,
        openid: String,
        companionId: String,
    ): WxSubscribeMessage? {
        if (messageId.isNullOrBlank()) {
            return null
        }

        val wxSubscribeMessageConfiguration = SpringUtils.getBean(WxSubscribeMessageConfiguration::class.java)
        val type: String = when (messageId) {
            wxSubscribeMessageConfiguration.yuyueId -> "Yuyue"
            wxSubscribeMessageConfiguration.cancelYuyueId -> "CancelYuyue"
            wxSubscribeMessageConfiguration.stopClinicId -> "StopClinic"
            wxSubscribeMessageConfiguration.receiveTransferId -> "ReceiveTransfer"
            else -> "Unknown"
        }

        val wxSubscribeMessageService = SpringUtils.getBean(IWxSubscribeMessageService::class.java)
        if (wxSubscribeMessageService.existsByCompanionIdAndTypeAndSendStatus(companionId, type, true)) {
            return null
        }

        val subscribeMessage: WxMaSubscribeMessage = WxMaSubscribeMessage.builder().apply {
            if (!messagePage.isNullOrBlank()) {
                this.page(messagePage)
            }

            this.templateId(messageId)
            this.toUser(openid)
            this.data(messageDataList)
        }.build()
        val data = JSON.toJSONString(subscribeMessage.data)

        val wxSubscribeMessage = WxSubscribeMessage.builder()
            .templateId(subscribeMessage.templateId)
            .openid(subscribeMessage.toUser)
            .data(data)
            .type(type)
            .companionId(companionId)
            .sendTime(LocalDateTime.now())
            .sendStatus(true)
            .error("OK")
            .build()

        try {
            val wxMaService = SpringUtils.getBean(WxMaService::class.java)
            wxMaService.msgService.sendSubscribeMsg(subscribeMessage)
        } catch (e: Exception) {
            wxSubscribeMessage.sendStatus = false
            wxSubscribeMessage.error = e.message ?: "未知错误"
        }

        wxSubscribeMessageService.save(wxSubscribeMessage)
        return wxSubscribeMessage
    }

    /**
     * 构造 thing 类型的订阅消息数据
     * 5个以内纯汉字
     * @see [发送订阅消息](https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html)
     */
    fun newWxMaSubscribeMessagePhraseData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeNonBasicChinese(value).take(5))
    }

    /**
     * 构造 thing 类型的订阅消息数据
     * 20个以内字符、可汉字、数字、字母或符号组合
     * @see [发送订阅消息](https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html)
     */
    fun newWxMaSubscribeMessageThingData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, value.take(20))
    }

    /**
     * 构造 name 类型的订阅消息数据
     * 10个以内纯汉字或20个以内纯字母或符号
     * 中文名10个汉字内；纯英文名20个字母内；中文和字母混合按中文名算，10个字内
     * @see [发送订阅消息](https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html)
     */
    fun newWxMaSubscribeMessageNameData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeNonBasicChinese(value).take(10))
    }

    private var wxInsurancePayService: WxInsurancePayService? = null

    @Synchronized
    fun getWxInsurancePayService(): WxInsurancePayService {
        if (wxInsurancePayService == null) {
            val wxPayService: WxPayService = SpringUtils.getBean(WxPayService::class.java)
            val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                val originalPayConfig: WxPayConfig = wxPayService.config
                this.appId = originalPayConfig.appId
                this.mchId = originalPayConfig.mchId
                this.mchKey = WeixinMipSetting.ma.mipKey
                this.keyPath = originalPayConfig.keyPath
            }
            wxInsurancePayService = WxInsurancePayService(wxPayConfig, {
                wxMaService.getAccessToken(it)
            }, wxPayService)
        }
        return wxInsurancePayService!!
    }

    fun idCardOcr(file: File): WxOcrIdCardResult {
        val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
        val wxMaOcrService: WxOcrService = wxMaService.ocrService
        return wxMaOcrService.idCard(file)
    }

    fun refundWeixin(mipWxOrder: MipWxOrder): WxInsurancePayRefundResult {
        check(mipWxOrder.ownPayAmount > BigDecimal.ZERO) { "微信支付金额为0，无需退微信" }
        check(mipWxOrder.hospOutRefundNo.isNullOrBlank()) { "订单已退款" }
        check(!mipWxOrder.medTransactionId.isNullOrBlank()) { "订单未支付" }
        mipWxOrder.hospOutRefundNo = mipWxOrder.hospitalOutTradeNo + "#R"
        val refundRequest: WxInsurancePayRefundRequest = WxInsurancePayRefundRequest().apply {
            this.medTransId = mipWxOrder.medTransactionId
            this.hospOutRefundNo = mipWxOrder.hospOutRefundNo
            this.partRefundType = "CASH_ONLY"
            this.payOrderId = mipWxOrder.payOrderId
            this.refReason = "申请退款"
        }
        val refundResult: WxInsurancePayRefundResult = getWxInsurancePayService().refund(refundRequest)
        val mipWxOrderService: IMipWxOrderService = SpringUtils.getBean(IMipWxOrderService::class.java)
        mipWxOrderService.updateOnRefund(mipWxOrder, refundResult)

        return refundResult
    }

    fun refundWeixin(paymentId: Long, hisTradeStatus: String, amount: BigDecimal): WxRefund {
        val wxPaymentService: IWxPaymentService = SpringUtils.getBean(IWxPaymentService::class.java)
        val wxRefundService: IWxRefundService = SpringUtils.getBean(IWxRefundService::class.java)

        val payment: WxPayment = wxPaymentService.selectWxPaymentById(paymentId)
            ?: throw IllegalArgumentException("订单不存在")
        val cents = PaymentKit.yuanToFen(amount)
        check(cents > 0) { "退款金额必须大于0" }
        check(cents <= payment.unrefund) { "退款金额超出订单限制" }
        check(cents <= wxPaymentService.calculateNetInAmount(LocalDate.now())) { "商户余额不足，建议您两小时后重试" }

        val refund = wxRefundService.createAndSave(
            payment,
            amount,
            PaymentKit.newOrderId(payment.serviceType),
            hisTradeStatus
        )
        wxPaymentService.updateOnRefund(payment, cents)

        // 调用退款接口
        val ccbRefundResponse = CcbPayServices.refund(
            space.lzhq.ph.pay.ccb.bean.RefundParams(
                order = refund.zyPayNo,
                refundCode = refund.zyRefundNo,
                money = PaymentKit.fenToYuan(refund.amount).toDouble()
            )
        )

        // 更新退款记录
        wxRefundService.updateOnCcbRefund(refund, ccbRefundResponse)
        return refund
    }

}

private fun buildBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_wxbill.csv"

private fun buildMipBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_mipbill.csv"

private fun buildFundBillPath(billDate: LocalDate, accountType: String): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_fund_${accountType}.csv"


private fun formatWxBillDate(billDate: LocalDate): String =
    billDate.format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN))

private fun buildWxPayDownloadRequest(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayDownloadBillRequest {
    val wxPayDownloadBillRequest = WxPayDownloadBillRequest()
    wxPayDownloadBillRequest.billType = billType
    wxPayDownloadBillRequest.billDate = formatWxBillDate(billDate)
    wxPayDownloadBillRequest.tarType = tarType
    wxPayDownloadBillRequest.deviceInfo = deviceInfo
    return wxPayDownloadBillRequest
}

fun WxPayServiceImpl.downloadAndSaveBill(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayBillResult? {
    val wxPayDownloadBillRequest = buildWxPayDownloadRequest(billDate, billType, tarType, deviceInfo)
    val responseContent = this.downloadRawBill(wxPayDownloadBillRequest)
    FileUtil.writeUtf8String(responseContent, buildBillPath(billDate))
    return if (StrUtil.isEmpty(responseContent)) {
        null
    } else {
        WxPayBillResult.fromRawBillResultString(responseContent, wxPayDownloadBillRequest.billType)
    }
}

fun WxPayBillInfo.isMenzhen(): Boolean = outTradeNo.startsWith(ServiceType.MZ.name)
fun WxPayBillInfo.isZhuyuan(): Boolean = outTradeNo.startsWith(ServiceType.ZY.name)
fun WxPayBillInfo.isPay(): Boolean = this.refundId.isNullOrBlank()
fun WxPayBillInfo.isRefund(): Boolean = !this.isPay()
fun WxPayBillResult.calculateTotalPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() }.map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isMenzhen() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isZhuyuan() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateTotalRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isMenzhen() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isZhuyuan() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }