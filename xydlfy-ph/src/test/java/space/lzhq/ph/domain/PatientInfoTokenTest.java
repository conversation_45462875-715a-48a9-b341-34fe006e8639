package space.lzhq.ph.domain;

import org.dromara.hutool.core.util.RandomUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class PatientInfoTokenTest {

    @Test
    void shouldParseTokenCorrectlyWithRandomData() {
        // Given: 创建包含随机数据的患者信息token
        PatientInfoToken originalToken = PatientInfoToken.builder()
                .patientId(RandomUtil.randomNumbers(6))
                .patientIdCardNo(RandomUtil.randomNumbers(18))
                .patientCardNo(RandomUtil.randomNumbers(10))
                .patientName(RandomUtil.randomString(8))
                .patientMobile(RandomUtil.randomNumbers(11))
                .build();

        // When: 生成token字符串并解析
        String tokenString = originalToken.generateToken();
        PatientInfoToken parsedToken = PatientInfoToken.parseToken(tokenString);

        // Then: 解析后的token应该与原始token相等
        assertEquals(originalToken, parsedToken);
    }

}
