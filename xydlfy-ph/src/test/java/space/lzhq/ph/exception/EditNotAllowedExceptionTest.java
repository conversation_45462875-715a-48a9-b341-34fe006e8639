package space.lzhq.ph.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EditNotAllowedException 异常类测试
 */
class EditNotAllowedExceptionTest {

    @Test
    void testConstructorWithMessage() {
        String message = "编辑操作不被允许";
        EditNotAllowedException exception = new EditNotAllowedException(message);

        assertEquals(message, exception.getMessage());
        assertNull(exception.getResourceType());
        assertNull(exception.getResourceId());
        assertNull(exception.getCurrentStatus());
    }

    @Test
    void testConstructorWithFullDetails() {
        String message = "记录当前状态不允许修改";
        String resourceType = "ChronicDiseaseRegistration";
        String resourceId = "test-id-123";
        String currentStatus = "已提交（待审核）";

        EditNotAllowedException exception = new EditNotAllowedException(
                message, resourceType, resourceId, currentStatus);

        assertEquals(message, exception.getMessage());
        assertEquals(resourceType, exception.getResourceType());
        assertEquals(resourceId, exception.getResourceId());
        assertEquals(currentStatus, exception.getCurrentStatus());
    }

    @Test
    void testConstructorWithMessageAndCause() {
        String message = "编辑操作不被允许";
        RuntimeException cause = new RuntimeException("底层异常");
        EditNotAllowedException exception = new EditNotAllowedException(message, cause);

        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertNull(exception.getResourceType());
        assertNull(exception.getResourceId());
        assertNull(exception.getCurrentStatus());
    }

    @Test
    void testExceptionIsRuntimeException() {
        EditNotAllowedException exception = new EditNotAllowedException("测试消息");
        
        // 验证是 RuntimeException 的子类
        assertTrue(exception instanceof RuntimeException);
    }

    @Test
    void testGettersReturnCorrectValues() {
        String resourceType = "TestResource";
        String resourceId = "123";
        String currentStatus = "INVALID_STATUS";
        String message = "测试异常消息";

        EditNotAllowedException exception = new EditNotAllowedException(
                message, resourceType, resourceId, currentStatus);

        // 验证所有getter方法返回正确的值
        assertEquals(message, exception.getMessage());
        assertEquals(resourceType, exception.getResourceType());
        assertEquals(resourceId, exception.getResourceId());
        assertEquals(currentStatus, exception.getCurrentStatus());
    }

    @Test
    void testNullValuesHandling() {
        EditNotAllowedException exception = new EditNotAllowedException(
                "测试消息", null, null, null);

        assertEquals("测试消息", exception.getMessage());
        assertNull(exception.getResourceType());
        assertNull(exception.getResourceId());
        assertNull(exception.getCurrentStatus());
    }
} 