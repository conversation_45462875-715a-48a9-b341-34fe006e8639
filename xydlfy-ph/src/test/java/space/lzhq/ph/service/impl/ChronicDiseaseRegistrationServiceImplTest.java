package space.lzhq.ph.service.impl;

import org.apache.commons.beanutils.BeanUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
import space.lzhq.ph.dto.request.ChronicDiseaseAgentUpdateDto;
import space.lzhq.ph.dto.request.ChronicDiseasePatientFormDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.InsuranceType;
import space.lzhq.ph.enums.Relationship;
import space.lzhq.ph.exception.AttachmentValidationException;
import space.lzhq.ph.exception.EditNotAllowedException;
import space.lzhq.ph.exception.ResourceNotFoundException;
import space.lzhq.ph.exception.UnauthorizedResourceAccessException;
import space.lzhq.ph.mapper.ChronicDiseaseRegistrationMapper;
import space.lzhq.ph.service.AttachmentFileChecker;
import space.lzhq.ph.service.ChronicDiseaseRegistrationAttachmentService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 慢病申报实现类测试
 * <p/>
 * 注意：测试方法使用下划线命名以提高可读性，符合现代测试最佳实践
 */
@ExtendWith(MockitoExtension.class)
@SuppressWarnings("java:S100") // 抑制方法命名规范警告，测试方法使用描述性命名
@DisplayName("慢病申报服务单元测试")
class ChronicDiseaseRegistrationServiceImplTest {

    @Mock
    private ChronicDiseaseRegistrationAttachmentService attachmentService;

    @Mock
    private AttachmentFileChecker fileChecker;

    @Spy
    @InjectMocks
    private ChronicDiseaseRegistrationServiceImpl registrationService;

    @Mock
    private ChronicDiseaseRegistrationMapper registrationMapper;

    private ChronicDiseasePatientFormDto validDto;
    private ChronicDiseaseAgentUpdateDto validAgentDto;
    private ChronicDiseaseRegistrationAttachment idCardAttachment;
    private ChronicDiseaseRegistrationAttachment insuranceCardAttachment;
    private ChronicDiseaseRegistrationAttachment agentIdCardAttachment;
    private static final String OPEN_ID = "test-open-id-123456789012345";
    private static final String ID_CARD_ATTACHMENT_ID = "id-card-attachment-id";
    private static final String INSURANCE_CARD_ATTACHMENT_ID = "insurance-card-attachment-id";
    private static final String AGENT_ID_CARD_ATTACHMENT_ID = "agent-id-card-attachment-id";
    private static final String GENERATED_REGISTRATION_ID = "generated-registration-id";
    private static final String ALREADY_USED_REGISTRATION_ID = "already-used-registration-id";
    private static final String TEST_REGISTRATION_ID = "test-registration-id";
    private static final String RESOURCE_TYPE = "ChronicDiseaseRegistration";

    @BeforeEach
    void setUp() {
        // 创建有效的DTO
        validDto = new ChronicDiseasePatientFormDto();
        validDto.setPatientIdCardAttachmentId(ID_CARD_ATTACHMENT_ID);
        validDto.setPatientIdCardNo("520524199001010157");
        validDto.setPatientName("张三");
        validDto.setPatientInsuranceCardAttachmentId(INSURANCE_CARD_ATTACHMENT_ID);
        validDto.setPatientInsuranceCardNo("1101011990010112345");
        validDto.setInsuranceType(InsuranceType.CITY_WORKER);
        validDto.setPatientMobile("13800138000");

        // 创建有效的代办人更新DTO
        validAgentDto = new ChronicDiseaseAgentUpdateDto();
        validAgentDto.setAgentName("李四");
        validAgentDto.setAgentIdCardNo("520524199001010158");
        validAgentDto.setAgentIdCardAttachmentId(AGENT_ID_CARD_ATTACHMENT_ID);
        validAgentDto.setAgentMobile("13800138001");
        validAgentDto.setAgentRelation(Relationship.WIFE);

        // 创建身份证附件
        idCardAttachment = ChronicDiseaseRegistrationAttachment.builder()
                .id(ID_CARD_ATTACHMENT_ID)
                .openId(OPEN_ID)
                .registrationId(null) // 未被使用
                .type(ChronicDiseaseRegistrationAttachmentType.ID_CARD)
                .fileName("身份证.jpg")
                .fileUrl("http://localhost:8080/profile/upload/2025/05/26/idcard.jpg")
                .build();

        // 创建医保卡附件
        insuranceCardAttachment = ChronicDiseaseRegistrationAttachment.builder()
                .id(INSURANCE_CARD_ATTACHMENT_ID)
                .openId(OPEN_ID)
                .registrationId(null) // 未被使用
                .type(ChronicDiseaseRegistrationAttachmentType.INSURANCE_CARD)
                .fileName("医保卡.jpg")
                .fileUrl("http://localhost:8080/profile/upload/2025/05/26/insurance-card.jpg")
                .build();

        // 创建代办人身份证附件
        agentIdCardAttachment = ChronicDiseaseRegistrationAttachment.builder()
                .id(AGENT_ID_CARD_ATTACHMENT_ID)
                .openId(OPEN_ID)
                .registrationId(null) // 未被使用
                .type(ChronicDiseaseRegistrationAttachmentType.ID_CARD)
                .fileName("代办人身份证.jpg")
                .fileUrl("http://localhost:8080/profile/upload/2025/05/26/agent-idcard.jpg")
                .build();
    }

    @Test
    void save_WithValidData_ShouldReturnRegistration() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);
        when(attachmentService.updateRegistrationId(anyString(), anyString())).thenReturn(true);

        // Mock FileChecker.isFileExists()
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // Mock this.save() 方法，并设置ID以模拟真实保存后的状态
        doAnswer(invocation -> {
            ChronicDiseaseRegistration registration = invocation.getArgument(0);
            // 模拟数据库保存后自动生成的ID
            registration.setId(GENERATED_REGISTRATION_ID);
            return true;
        }).when(registrationService).save(any(ChronicDiseaseRegistration.class));

        // When
        Optional<ChronicDiseaseRegistration> result = registrationService.save(validDto, OPEN_ID);

        // Then
        assertTrue(result.isPresent());
        ChronicDiseaseRegistration registration = result.get();

        assertEquals(OPEN_ID, registration.getOpenId());
        assertEquals(validDto.getPatientIdCardNo(), registration.getPatientIdCardNo());
        assertEquals(validDto.getPatientName(), registration.getPatientName());
        assertEquals("男", registration.getPatientGender()); // 从身份证号计算得出
        assertEquals(LocalDate.of(1990, 1, 1), registration.getPatientBirthDate()); // 从身份证号计算得出
        assertEquals(validDto.getPatientInsuranceCardNo(), registration.getPatientInsuranceCardNo());
        assertEquals(validDto.getInsuranceType(), registration.getInsuranceType());
        assertEquals(validDto.getPatientMobile(), registration.getPatientMobile());
        assertNull(registration.getAgentFlag());
        assertEquals(ChronicDiseaseRegistrationStatus.DRAFT, registration.getStatus());
        assertNotNull(registration.getCreateTime());
        assertNotNull(registration.getUpdateTime());
        assertEquals(GENERATED_REGISTRATION_ID, registration.getId()); // 验证ID被正确设置

        // 验证附件服务调用
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        InOrder inOrder = inOrder(attachmentService);
        inOrder.verify(attachmentService).updateRegistrationId(eq(ID_CARD_ATTACHMENT_ID), eq(GENERATED_REGISTRATION_ID));
        inOrder.verify(attachmentService).updateRegistrationId(eq(INSURANCE_CARD_ATTACHMENT_ID), eq(GENERATED_REGISTRATION_ID));
        verify(fileChecker, times(2)).isFileExists(anyString());
    }

    @Test
    void save_WithNonExistentIdCardAttachment_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(null);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件不存在"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).getById(INSURANCE_CARD_ATTACHMENT_ID);
    }

    @Test
    void save_WithAttachmentBelongingToOtherUser_ShouldThrowException() {
        // Given
        idCardAttachment.setOpenId("other-user-id");
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件不属于当前用户"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).getById(INSURANCE_CARD_ATTACHMENT_ID);
    }

    @Test
    void save_WithWrongAttachmentType_ShouldThrowException() {
        // Given
        idCardAttachment.setType(ChronicDiseaseRegistrationAttachmentType.INSURANCE_CARD); // 错误的类型
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件类型不正确"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).getById(INSURANCE_CARD_ATTACHMENT_ID);
    }

    @Test
    void save_WithAlreadyUsedAttachment_ShouldThrowException() {
        // Given
        idCardAttachment.setRegistrationId(ALREADY_USED_REGISTRATION_ID);
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件已被使用"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).getById(INSURANCE_CARD_ATTACHMENT_ID);
    }

    @Test
    void save_WithNonExistentFile_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);

        // Mock FileChecker.isFileExists() 返回文件不存在
        when(fileChecker.isFileExists(anyString())).thenReturn(false);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件对应的文件不存在"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(anyString());
    }

    @Test
    void save_WithFileCheckException_ShouldPropagateException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);

        // Mock FileChecker.isFileExists() 抛出异常
        when(fileChecker.isFileExists(anyString()))
                .thenThrow(new RuntimeException("File access error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertEquals("File access error", exception.getMessage());
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(anyString());
    }

    @Test
    void save_WithDatabaseSaveFailure_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);

        // Mock FileChecker.isFileExists()
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // Mock this.save() 方法返回失败
        doReturn(false).when(registrationService).save(any(ChronicDiseaseRegistration.class));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertEquals("保存慢病申报记录失败", exception.getMessage());
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(attachmentService, never()).updateRegistrationId(anyString(), anyString());
        verify(fileChecker, times(2)).isFileExists(anyString());
    }

    @Test
    void save_WithAttachmentUpdateFailure_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);
        when(attachmentService.updateRegistrationId(anyString(), anyString())).thenReturn(false);

        // Mock FileChecker.isFileExists()
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // Mock this.save() 方法，并设置ID以模拟真实保存后的状态
        doAnswer(invocation -> {
            ChronicDiseaseRegistration registration = invocation.getArgument(0);
            // 模拟数据库保存后自动生成的ID
            registration.setId(GENERATED_REGISTRATION_ID);
            return true;
        }).when(registrationService).save(any(ChronicDiseaseRegistration.class));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertEquals("更新附件关联的申报ID失败", exception.getMessage());
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(attachmentService).updateRegistrationId(eq(ID_CARD_ATTACHMENT_ID), eq(GENERATED_REGISTRATION_ID));
        verify(fileChecker, times(2)).isFileExists(anyString());
    }

    @Test
    void save_WithNonExistentInsuranceCardAttachment_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(null);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("医保卡附件不存在"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(anyString());
    }

    @Test
    void save_WithInsuranceCardAttachmentBelongingToOtherUser_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        insuranceCardAttachment.setOpenId("other-user-id");
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("医保卡附件不属于当前用户"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(anyString());
    }

    @Test
    void save_WithWrongInsuranceCardAttachmentType_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        insuranceCardAttachment.setType(ChronicDiseaseRegistrationAttachmentType.ID_CARD); // 错误的类型
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("医保卡附件类型不正确"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(anyString());
    }

    @Test
    void save_WithAlreadyUsedInsuranceCardAttachment_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        insuranceCardAttachment.setRegistrationId(ALREADY_USED_REGISTRATION_ID);
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("医保卡附件已被使用"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(anyString());
    }

    @Test
    void save_WithNonExistentInsuranceCardFile_ShouldThrowException() {
        // Given
        when(attachmentService.getById(ID_CARD_ATTACHMENT_ID)).thenReturn(idCardAttachment);
        when(attachmentService.getById(INSURANCE_CARD_ATTACHMENT_ID)).thenReturn(insuranceCardAttachment);

        // 身份证文件存在，但医保卡文件不存在
        when(fileChecker.isFileExists(idCardAttachment.getFileUrl())).thenReturn(true);
        when(fileChecker.isFileExists(insuranceCardAttachment.getFileUrl())).thenReturn(false);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.save(validDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("医保卡附件对应的文件不存在"));
        verify(attachmentService).getById(ID_CARD_ATTACHMENT_ID);
        verify(attachmentService).getById(INSURANCE_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(idCardAttachment.getFileUrl());
        verify(fileChecker).isFileExists(insuranceCardAttachment.getFileUrl());
    }

    // ==================== clearAgentFlag 方法测试 ====================

    @Test
    @DisplayName("清除代办标志 - 成功场景")
    void clearAgentFlag_WithValidData_ShouldClearAgentFlagAndReturnUpdatedRegistration() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);

        ChronicDiseaseRegistration updatedRegistration = assertDoesNotThrow(() -> (ChronicDiseaseRegistration) BeanUtils.cloneBean(registration));
        // 设置更新后的状态 - 已清除代办信息
        updatedRegistration.setAgentFlag(0);
        updatedRegistration.setAgentName(null);
        updatedRegistration.setAgentIdCardNo(null);
        updatedRegistration.setAgentIdCardAttachmentId(null);
        updatedRegistration.setAgentMobile(null);
        updatedRegistration.setAgentRelation(null);
        updatedRegistration.setUpdateTime(LocalDateTime.now());

        doReturn(1).when(registrationMapper).clearAgent(registrationId);
        doReturn(registrationMapper).when(registrationService).getBaseMapper();
        doReturn(registration).doReturn(updatedRegistration).when(registrationService).getById(registrationId);

        // When
        ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(0), result.getAgentFlag());
        assertNull(result.getAgentName());
        assertNull(result.getAgentIdCardNo());
        assertNull(result.getAgentIdCardAttachmentId());
        assertNull(result.getAgentMobile());
        assertNull(result.getAgentRelation());
        assertNotNull(result.getUpdateTime());

        verify(registrationMapper).clearAgent(registrationId);
        verify(registrationService, times(2)).getById(registrationId);
    }

    @Test
    @DisplayName("清除代办标志 - 申报记录不存在")
    void clearAgentFlag_WithNonExistentRegistration_ShouldThrowResourceNotFoundException() {
        // Given
        String registrationId = "non-existent-id";
        doReturn(null).when(registrationService).getById(registrationId);

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录不存在"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());

        verify(registrationService).getById(registrationId);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("清除代办标志 - 申报记录不属于当前用户")
    void clearAgentFlag_WithUnauthorizedAccess_ShouldThrowUnauthorizedResourceAccessException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        String otherUserOpenId = "other-user-open-id";
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, otherUserOpenId);

        doReturn(registration).when(registrationService).getById(registrationId);

        // When & Then
        UnauthorizedResourceAccessException exception = assertThrows(UnauthorizedResourceAccessException.class,
                () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录不属于当前用户"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());
        assertEquals(OPEN_ID, exception.getUserId());

        verify(registrationService).getById(registrationId);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("清除代办标志 - 申报记录状态不允许修改（已提交）")
    void clearAgentFlag_WithSubmittedStatus_ShouldThrowEditNotAllowedException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.SUBMITTED); // 已提交状态不允许修改

        doReturn(registration).when(registrationService).getById(registrationId);

        // When & Then
        EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
                () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());

        verify(registrationService).getById(registrationId);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("清除代办标志 - 申报记录状态不允许修改（已通过）")
    void clearAgentFlag_WithApprovedStatus_ShouldThrowEditNotAllowedException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.APPROVED); // 已通过状态不允许修改

        doReturn(registration).when(registrationService).getById(registrationId);

        // When & Then
        EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
                () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());

        verify(registrationService).getById(registrationId);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("清除代办标志 - 草稿状态允许修改")
    void clearAgentFlag_WithDraftStatus_ShouldSucceed() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT); // 草稿状态允许修改

        ChronicDiseaseRegistration updatedRegistration = assertDoesNotThrow(() -> (ChronicDiseaseRegistration) BeanUtils.cloneBean(registration));
        // 设置更新后的状态 - 已清除代办信息
        updatedRegistration.setAgentFlag(0);
        updatedRegistration.setAgentName(null);
        updatedRegistration.setAgentIdCardNo(null);
        updatedRegistration.setAgentIdCardAttachmentId(null);
        updatedRegistration.setAgentMobile(null);
        updatedRegistration.setAgentRelation(null);
        updatedRegistration.setUpdateTime(LocalDateTime.now());

        doReturn(1).when(registrationMapper).clearAgent(registrationId);
        doReturn(registrationMapper).when(registrationService).getBaseMapper();
        doReturn(registration).doReturn(updatedRegistration).when(registrationService).getById(registrationId);

        // When
        ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(0), result.getAgentFlag());

        verify(registrationMapper).clearAgent(registrationId);
        verify(registrationService, times(2)).getById(registrationId);
    }

    @Test
    @DisplayName("清除代办标志 - 退回状态允许修改")
    void clearAgentFlag_WithRejectedStatus_ShouldSucceed() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.REJECTED); // 退回状态允许修改

        ChronicDiseaseRegistration updatedRegistration = assertDoesNotThrow(() -> (ChronicDiseaseRegistration) BeanUtils.cloneBean(registration));
        // 设置更新后的状态 - 已清除代办信息
        updatedRegistration.setAgentFlag(0);
        updatedRegistration.setAgentName(null);
        updatedRegistration.setAgentIdCardNo(null);
        updatedRegistration.setAgentIdCardAttachmentId(null);
        updatedRegistration.setAgentMobile(null);
        updatedRegistration.setAgentRelation(null);
        updatedRegistration.setUpdateTime(LocalDateTime.now());

        doReturn(1).when(registrationMapper).clearAgent(registrationId);
        doReturn(registrationMapper).when(registrationService).getBaseMapper();
        doReturn(registration).doReturn(updatedRegistration).when(registrationService).getById(registrationId);

        // When
        ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(0), result.getAgentFlag());

        verify(registrationMapper).clearAgent(registrationId);
        verify(registrationService, times(2)).getById(registrationId);
    }

    @Test
    @DisplayName("清除代办标志 - 验证所有代办相关字段都被清空")
    void clearAgentFlag_ShouldClearAllAgentRelatedFields() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
        LocalDateTime originalUpdateTime = registration.getUpdateTime();

        ChronicDiseaseRegistration updatedRegistration = assertDoesNotThrow(() -> (ChronicDiseaseRegistration) BeanUtils.cloneBean(registration));
        // 设置更新后的状态 - 已清除代办信息
        updatedRegistration.setAgentFlag(0);
        updatedRegistration.setAgentName(null);
        updatedRegistration.setAgentIdCardNo(null);
        updatedRegistration.setAgentIdCardAttachmentId(null);
        updatedRegistration.setAgentMobile(null);
        updatedRegistration.setAgentRelation(null);
        updatedRegistration.setUpdateTime(LocalDateTime.now());

        doReturn(1).when(registrationMapper).clearAgent(registrationId);
        doReturn(registrationMapper).when(registrationService).getBaseMapper();
        doReturn(registration).doReturn(updatedRegistration).when(registrationService).getById(registrationId);

        // When
        ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);

        // Then
        assertEquals(Integer.valueOf(0), result.getAgentFlag());
        assertNull(result.getAgentName());
        assertNull(result.getAgentIdCardNo());
        assertNull(result.getAgentIdCardAttachmentId());
        assertNull(result.getAgentMobile());
        assertNull(result.getAgentRelation());
        assertTrue(result.getUpdateTime().isAfter(originalUpdateTime));

        // 验证其他字段未被修改
        assertEquals(OPEN_ID, result.getOpenId());
        assertEquals(validDto.getPatientName(), result.getPatientName());
        assertEquals(validDto.getPatientIdCardNo(), result.getPatientIdCardNo());
    }

    /**
     * 创建包含代办信息的申报记录
     */
    private ChronicDiseaseRegistration createRegistrationWithAgentFlag(String registrationId, String openId) {
        ChronicDiseaseRegistration registration = new ChronicDiseaseRegistration();
        registration.setId(registrationId);
        registration.setOpenId(openId);
        registration.setPatientName(validDto.getPatientName());
        registration.setPatientIdCardNo(validDto.getPatientIdCardNo());
        registration.setPatientInsuranceCardNo(validDto.getPatientInsuranceCardNo());
        registration.setInsuranceType(validDto.getInsuranceType());
        registration.setPatientMobile(validDto.getPatientMobile());
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT);
        registration.setCreateTime(LocalDateTime.now().minusDays(1));
        registration.setUpdateTime(LocalDateTime.now().minusHours(1));

        // 设置代办信息
        registration.setAgentFlag(1);
        registration.setAgentName("代办人姓名");
        registration.setAgentIdCardNo("520524199001010158");
        registration.setAgentIdCardAttachmentId("agent-id-card-attachment-id");
        registration.setAgentMobile("13800138001");
        registration.setAgentRelation(Relationship.WIFE);

        return registration;
    }

    // ==================== updateAgent 方法测试 ====================

    @Test
    @DisplayName("更新代办人信息 - 成功场景")
    void updateAgent_WithValidData_ShouldUpdateAgentInfoAndReturnUpdatedRegistration() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);
        when(attachmentService.updateRegistrationId(anyString(), anyString())).thenReturn(true);
        doReturn(true).when(registrationService).updateById(any(ChronicDiseaseRegistration.class));

        // When
        ChronicDiseaseRegistration result = registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getAgentFlag());
        assertEquals(validAgentDto.getAgentName(), result.getAgentName());
        assertEquals(validAgentDto.getAgentIdCardNo(), result.getAgentIdCardNo());
        assertEquals(validAgentDto.getAgentIdCardAttachmentId(), result.getAgentIdCardAttachmentId());
        assertEquals(validAgentDto.getAgentMobile(), result.getAgentMobile());
        assertEquals(validAgentDto.getAgentRelation(), result.getAgentRelation());
        assertNotNull(result.getUpdateTime());

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(agentIdCardAttachment.getFileUrl());
        verify(registrationService).updateById(registration);
        verify(attachmentService).updateRegistrationId(AGENT_ID_CARD_ATTACHMENT_ID, registrationId);
    }

    @Test
    @DisplayName("更新代办人信息 - 申报记录不存在")
    void updateAgent_WithNonExistentRegistration_ShouldThrowResourceNotFoundException() {
        // Given
        String registrationId = "non-existent-id";
        doReturn(null).when(registrationService).getById(registrationId);

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录不存在"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());

        verify(registrationService).getById(registrationId);
        verify(attachmentService, never()).getById(anyString());
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 申报记录不属于当前用户")
    void updateAgent_WithUnauthorizedAccess_ShouldThrowUnauthorizedResourceAccessException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        String otherUserOpenId = "other-user-open-id";
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, otherUserOpenId);

        doReturn(registration).when(registrationService).getById(registrationId);

        // When & Then
        UnauthorizedResourceAccessException exception = assertThrows(UnauthorizedResourceAccessException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录不属于当前用户"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());
        assertEquals(OPEN_ID, exception.getUserId());

        verify(registrationService).getById(registrationId);
        verify(attachmentService, never()).getById(anyString());
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 申报记录状态不允许修改（已提交）")
    void updateAgent_WithSubmittedStatus_ShouldThrowEditNotAllowedException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.SUBMITTED); // 已提交状态不允许修改

        doReturn(registration).when(registrationService).getById(registrationId);

        // When & Then
        EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
        assertEquals(RESOURCE_TYPE, exception.getResourceType());
        assertEquals(registrationId, exception.getResourceId());

        verify(registrationService).getById(registrationId);
        verify(attachmentService, never()).getById(anyString());
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 代办人身份证附件不存在")
    void updateAgent_WithNonExistentAgentIdCardAttachment_ShouldThrowAttachmentValidationException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(null);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件不存在"));

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 代办人身份证附件不属于当前用户")
    void updateAgent_WithAgentAttachmentBelongingToOtherUser_ShouldThrowAttachmentValidationException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);
        agentIdCardAttachment.setOpenId("other-user-id");

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件不属于当前用户"));

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 代办人身份证附件类型不正确")
    void updateAgent_WithWrongAgentAttachmentType_ShouldThrowAttachmentValidationException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);
        agentIdCardAttachment.setType(ChronicDiseaseRegistrationAttachmentType.INSURANCE_CARD); // 错误的类型

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件类型不正确"));

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 代办人身份证附件已被使用")
    void updateAgent_WithAlreadyUsedAgentAttachment_ShouldThrowAttachmentValidationException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);
        agentIdCardAttachment.setRegistrationId(ALREADY_USED_REGISTRATION_ID);

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件已被使用"));

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 代办人身份证附件对应的文件不存在")
    void updateAgent_WithNonExistentAgentFile_ShouldThrowAttachmentValidationException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);
        when(fileChecker.isFileExists(agentIdCardAttachment.getFileUrl())).thenReturn(false);

        // When & Then
        AttachmentValidationException exception = assertThrows(AttachmentValidationException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertTrue(exception.getMessage().contains("身份证附件对应的文件不存在"));

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(agentIdCardAttachment.getFileUrl());
        verify(registrationService, never()).updateById(any());
    }

    @Test
    @DisplayName("更新代办人信息 - 数据库更新失败")
    void updateAgent_WithDatabaseUpdateFailure_ShouldThrowRuntimeException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);
        doReturn(false).when(registrationService).updateById(any(ChronicDiseaseRegistration.class));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertEquals("更新代办人信息失败，请重试", exception.getMessage());

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(agentIdCardAttachment.getFileUrl());
        verify(registrationService).updateById(registration);
        verify(attachmentService, never()).updateRegistrationId(anyString(), anyString());
    }

    @Test
    @DisplayName("更新代办人信息 - 附件更新失败")
    void updateAgent_WithAttachmentUpdateFailure_ShouldThrowRuntimeException() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);
        when(attachmentService.updateRegistrationId(anyString(), anyString())).thenReturn(false);
        doReturn(true).when(registrationService).updateById(any(ChronicDiseaseRegistration.class));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID));

        assertEquals("更新附件关联的申报ID失败", exception.getMessage());

        verify(registrationService).getById(registrationId);
        verify(attachmentService).getById(AGENT_ID_CARD_ATTACHMENT_ID);
        verify(fileChecker).isFileExists(agentIdCardAttachment.getFileUrl());
        verify(registrationService).updateById(registration);
        verify(attachmentService).updateRegistrationId(AGENT_ID_CARD_ATTACHMENT_ID, registrationId);
    }

    @Test
    @DisplayName("更新代办人信息 - 草稿状态允许修改")
    void updateAgent_WithDraftStatus_ShouldSucceed() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT); // 草稿状态允许修改

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);
        when(attachmentService.updateRegistrationId(anyString(), anyString())).thenReturn(true);
        doReturn(true).when(registrationService).updateById(any(ChronicDiseaseRegistration.class));

        // When
        ChronicDiseaseRegistration result = registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getAgentFlag());
        assertEquals(validAgentDto.getAgentName(), result.getAgentName());

        verify(registrationService).getById(registrationId);
        verify(registrationService).updateById(registration);
    }

    @Test
    @DisplayName("更新代办人信息 - 退回状态允许修改")
    void updateAgent_WithRejectedStatus_ShouldSucceed() {
        // Given
        String registrationId = TEST_REGISTRATION_ID;
        ChronicDiseaseRegistration registration = createRegistrationWithoutAgent(registrationId, OPEN_ID);
        registration.setStatus(ChronicDiseaseRegistrationStatus.REJECTED); // 退回状态允许修改

        doReturn(registration).when(registrationService).getById(registrationId);
        when(attachmentService.getById(AGENT_ID_CARD_ATTACHMENT_ID)).thenReturn(agentIdCardAttachment);
        when(fileChecker.isFileExists(anyString())).thenReturn(true);
        when(attachmentService.updateRegistrationId(anyString(), anyString())).thenReturn(true);
        doReturn(true).when(registrationService).updateById(any(ChronicDiseaseRegistration.class));

        // When
        ChronicDiseaseRegistration result = registrationService.updateAgent(registrationId, validAgentDto, OPEN_ID);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getAgentFlag());
        assertEquals(validAgentDto.getAgentName(), result.getAgentName());

        verify(registrationService).getById(registrationId);
        verify(registrationService).updateById(registration);
    }

    /**
     * 创建不包含代办信息的申报记录
     */
    private ChronicDiseaseRegistration createRegistrationWithoutAgent(String registrationId, String openId) {
        ChronicDiseaseRegistration registration = new ChronicDiseaseRegistration();
        registration.setId(registrationId);
        registration.setOpenId(openId);
        registration.setPatientName(validDto.getPatientName());
        registration.setPatientIdCardNo(validDto.getPatientIdCardNo());
        registration.setPatientInsuranceCardNo(validDto.getPatientInsuranceCardNo());
        registration.setInsuranceType(validDto.getInsuranceType());
        registration.setPatientMobile(validDto.getPatientMobile());
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT);
        registration.setCreateTime(LocalDateTime.now().minusDays(1));
        registration.setUpdateTime(LocalDateTime.now().minusHours(1));

        // 不设置代办信息
        registration.setAgentFlag(0);
        registration.setAgentName(null);
        registration.setAgentIdCardNo(null);
        registration.setAgentIdCardAttachmentId(null);
        registration.setAgentMobile(null);
        registration.setAgentRelation(null);

        return registration;
    }
} 