package space.lzhq.ph.dto.mapper;

import org.junit.jupiter.api.Test;
import space.lzhq.ph.domain.ChronicDiseaseRegistration;
import space.lzhq.ph.dto.request.ChronicDiseasePatientFormDto;
import space.lzhq.ph.dto.response.ChronicDiseaseRegistrationListResponseDto;
import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
import space.lzhq.ph.enums.InsuranceType;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 慢病申报映射器测试类
 */
class ChronicDiseaseRegistrationMapperTest {

    private static final ChronicDiseaseRegistrationMapper MAPPER = ChronicDiseaseRegistrationMapper.INSTANCE;

    private static final String MALE_PATIENT_ID_CARD_NO = "520524199001010157";
    private static final String FEMALE_PATIENT_ID_CARD_NO = "43052219900101572X";
    private static final LocalDate BIRTH_DATE = LocalDate.of(1990, 1, 1);
    private static final String PATIENT_ID_CARD_ATTACHMENT_ID = "123e4567-e89b-12d3-a456-426614174000";
    private static final String PATIENT_INSURANCE_CARD_ATTACHMENT_ID = "456e7890-e89b-12d3-a456-426614174001";
    private static final String PATIENT_INSURANCE_CARD_NO = "1101011990010112345";
    private static final InsuranceType INSURANCE_TYPE = InsuranceType.CITY_WORKER;
    private static final String PATIENT_MOBILE = "13800138000";
    private static final String PATIENT_NAME = "张三";
    private static final String MALE = "男";
    private static final String FEMALE = "女";

    @Test
    void testFromPatientFormDto() {
        // 准备测试数据
        ChronicDiseasePatientFormDto dto = new ChronicDiseasePatientFormDto();
        dto.setPatientIdCardAttachmentId(PATIENT_ID_CARD_ATTACHMENT_ID);
        dto.setPatientIdCardNo(MALE_PATIENT_ID_CARD_NO);
        dto.setPatientName(PATIENT_NAME);
        dto.setPatientInsuranceCardAttachmentId(PATIENT_INSURANCE_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardNo(PATIENT_INSURANCE_CARD_NO);
        dto.setInsuranceType(INSURANCE_TYPE);
        dto.setPatientMobile(PATIENT_MOBILE);

        // 执行映射
        ChronicDiseaseRegistration registration = MAPPER.fromPatientFormDto(dto);

        // 验证结果
        assertNotNull(registration);
        assertEquals(dto.getPatientIdCardAttachmentId(), registration.getPatientIdCardAttachmentId());
        assertEquals(dto.getPatientIdCardNo(), registration.getPatientIdCardNo());
        assertEquals(dto.getPatientName(), registration.getPatientName());
        assertEquals(dto.getPatientInsuranceCardAttachmentId(), registration.getPatientInsuranceCardAttachmentId());
        assertEquals(dto.getPatientInsuranceCardNo(), registration.getPatientInsuranceCardNo());
        assertEquals(dto.getInsuranceType(), registration.getInsuranceType());
        assertEquals(dto.getPatientMobile(), registration.getPatientMobile());

        // 验证从身份证号提取的信息
        assertEquals(MALE, registration.getPatientGender());
        assertEquals(BIRTH_DATE, registration.getPatientBirthDate());
    }

    @Test
    void testToListResponseDto() {
        // 准备测试数据
        ChronicDiseaseRegistration registration = new ChronicDiseaseRegistration();
        registration.setId("abd2a91e6ff9f835a76f801e6ba9b3aa");
        registration.setOpenId("omPh85Rdf1boaNRrC_AmUN0E62kU");
        registration.setPatientIdCardAttachmentId(PATIENT_ID_CARD_ATTACHMENT_ID);
        registration.setPatientIdCardNo(MALE_PATIENT_ID_CARD_NO);
        registration.setPatientName(PATIENT_NAME);
        registration.setPatientGender(MALE);
        registration.setPatientBirthDate(BIRTH_DATE);
        registration.setPatientInsuranceCardAttachmentId(PATIENT_INSURANCE_CARD_ATTACHMENT_ID);
        registration.setPatientInsuranceCardNo(PATIENT_INSURANCE_CARD_NO);
        registration.setInsuranceType(INSURANCE_TYPE);
        registration.setPatientMobile(PATIENT_MOBILE);
        registration.setAgentFlag(0);
        registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT);
        registration.setCreateTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        // 执行映射
        ChronicDiseaseRegistrationListResponseDto responseDto = MAPPER.toListResponseDto(registration);

        // 验证结果
        assertNotNull(responseDto);
        assertEquals(registration.getId(), responseDto.getId());
        assertEquals(registration.getOpenId(), responseDto.getOpenId());
        assertEquals(registration.getPatientIdCardAttachmentId(), responseDto.getPatientIdCardAttachmentId());
        assertEquals(registration.getPatientIdCardNo(), responseDto.getPatientIdCardNo());
        assertEquals(registration.getPatientName(), responseDto.getPatientName());
        assertEquals(registration.getPatientGender(), responseDto.getPatientGender());
        assertEquals(registration.getPatientBirthDate(), responseDto.getPatientBirthDate());
        assertEquals(registration.getPatientInsuranceCardAttachmentId(), responseDto.getPatientInsuranceCardAttachmentId());
        assertEquals(registration.getPatientInsuranceCardNo(), responseDto.getPatientInsuranceCardNo());
        assertEquals(registration.getInsuranceType(), responseDto.getInsuranceType());
        assertEquals(registration.getPatientMobile(), responseDto.getPatientMobile());
        assertEquals(registration.getAgentFlag(), responseDto.getAgentFlag());
        assertEquals(registration.getStatus(), responseDto.getStatus());
        assertEquals(registration.getCreateTime(), responseDto.getCreateTime());
        assertEquals(registration.getUpdateTime(), responseDto.getUpdateTime());
    }

    @Test
    void testExtractGenderFromIdCard() {
        // 测试男性身份证号（倒数第二位为奇数）
        ChronicDiseasePatientFormDto dto = new ChronicDiseasePatientFormDto();
        dto.setPatientIdCardNo(MALE_PATIENT_ID_CARD_NO);
        dto.setPatientName(PATIENT_NAME);
        dto.setPatientIdCardAttachmentId(PATIENT_ID_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardAttachmentId(PATIENT_INSURANCE_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardNo(PATIENT_INSURANCE_CARD_NO);
        dto.setInsuranceType(INSURANCE_TYPE);
        dto.setPatientMobile(PATIENT_MOBILE);

        ChronicDiseaseRegistration registration = MAPPER.fromPatientFormDto(dto);
        assertEquals(MALE, registration.getPatientGender());

        // 测试女性身份证号（倒数第二位为偶数
        dto.setPatientIdCardNo(FEMALE_PATIENT_ID_CARD_NO);
        registration = MAPPER.fromPatientFormDto(dto);
        assertEquals(FEMALE, registration.getPatientGender());
    }

    @Test
    void testExtractBirthDateFromIdCard() {
        // 测试从身份证号提取出生日期
        ChronicDiseasePatientFormDto dto = new ChronicDiseasePatientFormDto();
        dto.setPatientIdCardNo(MALE_PATIENT_ID_CARD_NO);
        dto.setPatientName(PATIENT_NAME);
        dto.setPatientIdCardAttachmentId(PATIENT_ID_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardAttachmentId(PATIENT_INSURANCE_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardNo(PATIENT_INSURANCE_CARD_NO);
        dto.setInsuranceType(INSURANCE_TYPE);
        dto.setPatientMobile(PATIENT_MOBILE);

        ChronicDiseaseRegistration registration = MAPPER.fromPatientFormDto(dto);
        assertEquals(BIRTH_DATE, registration.getPatientBirthDate());
    }

    @Test
    void testInvalidIdCard() {
        // 测试无效身份证号
        String invalidIdCard = "invalid";
        ChronicDiseasePatientFormDto dto = new ChronicDiseasePatientFormDto();
        dto.setPatientIdCardNo(invalidIdCard);
        dto.setPatientName(PATIENT_NAME);
        dto.setPatientIdCardAttachmentId(PATIENT_ID_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardAttachmentId(PATIENT_INSURANCE_CARD_ATTACHMENT_ID);
        dto.setPatientInsuranceCardNo(PATIENT_INSURANCE_CARD_NO);
        dto.setInsuranceType(INSURANCE_TYPE);
        dto.setPatientMobile(PATIENT_MOBILE);

        ChronicDiseaseRegistration registration = MAPPER.fromPatientFormDto(dto);
        assertNull(registration.getPatientGender());
        assertNull(registration.getPatientBirthDate());
    }
} 