package space.lzhq.ph.reconciliation.matcher.impl;

import cn.yqtl.pay.ccb.model.BillDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import space.lzhq.ph.domain.HisSelfPayOrder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * 国光自助机对账匹配策略测试
 */
class GuoguangAtmMatcherTest {

    private GuoguangAtmMatcher guoguangAtmMatcher;
    private BillDetail appOrderPay;
    private BillDetail appOrderRefund;
    private HisSelfPayOrder hisOrderPay;
    private HisSelfPayOrder hisOrderRefund;

    @BeforeEach
    void setUp() {
        guoguangAtmMatcher = new GuoguangAtmMatcher();

        // 模拟自助机支付账单明细
        appOrderPay = Mockito.mock(BillDetail.class);
        when(appOrderPay.getOutTradeNo()).thenReturn("PAY123456");
        when(appOrderPay.getBankSerial()).thenReturn("BANK123456");
        when(appOrderPay.getTradeAmount()).thenReturn(new BigDecimal("100.00"));
        when(appOrderPay.isPay()).thenReturn(true);

        // 模拟自助机退款账单明细
        appOrderRefund = Mockito.mock(BillDetail.class);
        when(appOrderRefund.getOutTradeNo()).thenReturn("REF123456");
        when(appOrderRefund.getBankSerial()).thenReturn("BANK654321");
        when(appOrderRefund.getTradeAmount()).thenReturn(new BigDecimal("50.00"));
        when(appOrderRefund.isPay()).thenReturn(false);

        // 模拟HIS支付订单
        hisOrderPay = new HisSelfPayOrder();
        hisOrderPay.setBankOrderNo("PAY123456");
        hisOrderPay.setTradeType("支付");
        hisOrderPay.setTradeAmount(new BigDecimal("100.00"));
        hisOrderPay.setTradeTime(LocalDateTime.of(2023, 10, 15, 10, 30));
        hisOrderPay.setPatientId("P001");
        hisOrderPay.setPatientName("张三");
        hisOrderPay.setRemark("门诊缴费");
        hisOrderPay.setCashier("收费员1");

        // 模拟HIS退款订单
        hisOrderRefund = new HisSelfPayOrder();
        hisOrderRefund.setBankOrderNo("REF123456");
        hisOrderRefund.setTradeType("退款");
        hisOrderRefund.setTradeAmount(new BigDecimal("-50.00"));
        hisOrderRefund.setTradeTime(LocalDateTime.of(2023, 10, 15, 14, 30));
        hisOrderRefund.setPatientId("P002");
        hisOrderRefund.setPatientName("李四");
        hisOrderRefund.setRemark("门诊退费");
        hisOrderRefund.setCashier("收费员2");
    }

    @Test
    void testGetAppPaymentMatchKey() {
        assertEquals("PAY123456", guoguangAtmMatcher.getAppPaymentMatchKey(appOrderPay));
    }

    @Test
    void testGetAppRefundMatchKey() {
        assertEquals("REF123456XX", guoguangAtmMatcher.getAppRefundMatchKey(appOrderRefund));
    }

    @Test
    void testGetHisPaymentMatchKey() {
        assertEquals("PAY123456", guoguangAtmMatcher.getHisPaymentMatchKey(hisOrderPay));
    }

    @Test
    void testGetHisRefundMatchKey() {
        assertEquals("REF123456", guoguangAtmMatcher.getHisRefundMatchKey(hisOrderRefund));
    }

} 