package space.lzhq.ph.util;

import org.junit.jupiter.api.Disabled;

import java.io.File;
import java.nio.file.Path;

import static org.assertj.core.api.Assertions.assertThatCode;

class FTPUtilTest {

    @Disabled
    void testDownloadFile() {
        String host = "************";
        String fileName = "113650180620010-20250101.txt";
        int port = 21;
        String username = "tomcat";
        String password = "?tacmoT@025!";
        String remotePath = "/data";
        File localFile = Path.of("txt", fileName).toFile();
        assertThatCode(() -> FTPUtil.downloadFile(host, port, username, password, remotePath, fileName, localFile))
                .doesNotThrowAnyException();
        System.out.println("下载成功: " + localFile.getAbsolutePath());
    }

}
