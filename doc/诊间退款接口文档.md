# 诊间退款接口文档

## 接口基本信息

- **接口描述**: 诊间支付退款接口，支持微信和支付宝退款
- **接口地址**: `http://150.101.94.20:8899/api/zhenjian/refund`
- **请求方式**: POST
- **数据格式**: application/json

## 请求参数

### 请求体

| 参数名         | 类型         | 必填 | 描述     | 示例值                  |
|-------------|------------|----|--------|----------------------|
| amount      | BigDecimal | 是  | 退款金额   | 100.00               |
| orderNumber | String     | 是  | 原支付订单号 | ZJ202503181722047191 |

### 请求示例

```json
{
  "amount": 100.00,
  "orderNumber": "ZJ202503181722047191"
}
```

## 响应参数

### 成功响应

| 参数名             | 类型         | 描述      | 示例值                            |
|-----------------|------------|---------|--------------------------------|
| code            | Integer    | 状态码     | 200                            |
| msg             | String     | 响应消息    | "操作成功"                         |
| data            | Object     | 响应数据    | -                              |
| └─ patientId    | String     | 患者ID    | "2113971"                      |
| └─ totalAmount  | BigDecimal | 原订单总金额  | 200.00                         |
| └─ refundAmount | BigDecimal | 退款金额    | 100.00                         |
| └─ zyPayNo      | String     | 医院支付单号  | "ZY202503181722047191"         |
| └─ zyRefundNo   | String     | 医院退款单号  | "ZJ202503182034523674"         |
| └─ tradeNo      | String     | 第三方支付单号 | "2025031822001470211435886521" |

### 成功响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "patientId": "2113971",
    "totalAmount": 200.00,
    "refundAmount": 100.00,
    "zyPayNo": "ZY202503181722047191",
    "zyRefundNo": "ZJ202503182034523674",
    "tradeNo": "2025031822001470211435886521"
  }
}
```

### 错误响应

| 错误码 | 错误描述        |
|-----|-------------|
| 500 | 退款金额必须大于0   |
| 500 | 订单不存在       |
| 500 | 此订单不是诊间支付订单 |
| 500 | 结算单号不匹配     |
| 500 | 退款金额超出订单限制  |
| 500 | 商户余额不足      |

### 错误响应示例

```json
{
  "code": 500,
  "msg": "退款金额超出订单限制"
}
```

## 注意事项

1. 退款金额不能超过原订单未退款金额
2. 微信退款时会校验商户余额，余额不足时建议2小时后重试
3. 订单号必须是有效的诊间支付订单号
