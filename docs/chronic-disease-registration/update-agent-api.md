# 慢病申报更新代办人信息API

## 概述

本文档描述了新实现的慢病申报更新代办人信息功能，该功能允许用户更新现有慢病申报记录的代办人信息。

## API端点

### PUT /api/chronic-disease-registration/{id}/agent

**功能描述：** 更新指定慢病申报记录的代办人信息

**请求方式：** PUT

**URL路径：** `/api/chronic-disease-registration/{id}/agent`

**路径参数：**
- `id` (String, 必需): 慢病申报记录ID，例如：`abd2a91e-6ff9-f835-a76f-801e6ba9b3aa`

**请求头：**
- `Authorization` (String, 必需): 有效的会话ID，例如：`7cb73c8e-4328-4c32-a89c-1f5e8e2f027b`
- `Content-Type`: `application/json`

**请求体：**
```json
{
  "agentName": "李四",
  "agentIdCardNo": "520524199001010158",
  "agentIdCardAttachmentId": "789e0123-e89b-12d3-a456-426614174002",
  "agentMobile": "13800138001",
  "agentRelation": "WIFE"
}
```

## 请求参数说明

| 字段名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| agentName | String | 是 | 代办人姓名，不能包含特殊字符 | "李四" |
| agentIdCardNo | String | 是 | 代办人身份证号，必须是有效的身份证号格式 | "520524199001010158" |
| agentIdCardAttachmentId | String | 是 | 代办人身份证附件ID，必须是已上传的有效附件 | "789e0123-e89b-12d3-a456-426614174002" |
| agentMobile | String | 是 | 代办人手机号，必须是有效的手机号格式 | "13800138001" |
| agentRelation | String | 是 | 代办人与患者关系，可选值见下表 | "WIFE" |

### 代办人关系枚举值

| 枚举值 | 描述 |
|--------|------|
| FATHER | 父亲 |
| MOTHER | 母亲 |
| HUSBAND | 丈夫 |
| WIFE | 妻子 |
| SON | 儿子 |
| DAUGHTER | 女儿 |
| BROTHER | 兄弟 |
| SISTER | 姐妹 |
| FRIEND | 朋友 |
| COLLEAGUE | 同事 |
| CLASSMATE | 同学 |
| OTHER | 其他 |

## 前置条件验证

API会自动验证以下前置条件，不满足时会抛出相应的异常：

### 1. 资源存在性检查
- **验证规则：** 申报记录必须存在于数据库中
- **异常类型：** `ResourceNotFoundException`
- **错误响应：** HTTP 200，消息："申报记录不存在"

### 2. 用户授权检查
- **验证规则：** 申报记录的 `openId` 必须与当前用户的 `openId` 一致
- **异常类型：** `UnauthorizedResourceAccessException`
- **错误响应：** HTTP 200，消息："申报记录不属于当前用户"

### 3. 编辑权限检查
- **验证规则：** 申报记录状态必须允许用户编辑（通过 `canBeModifiedByUser()` 方法验证）
- **异常类型：** `EditNotAllowedException`
- **错误响应：** HTTP 200，消息："申报记录当前状态不允许修改：{状态描述}"

#### 允许编辑的状态：
- `DRAFT`（草稿（待提交））
- `REJECTED`（审核驳回（待整改））

#### 不允许编辑的状态：
- `SUBMITTED`（已提交（待审核））
- `APPROVED`（审核通过）

### 4. 附件验证检查
- **验证规则：** 代办人身份证附件必须满足以下条件：
  - 附件存在于数据库中
  - 附件属于当前用户
  - 附件类型为身份证类型
  - 附件未被其他申报记录使用（或已关联到当前申报记录）
  - 附件对应的文件在文件系统中存在
- **异常类型：** `AttachmentValidationException`
- **错误响应：** HTTP 200，消息："身份证附件{具体错误原因}"

## 执行操作

当所有前置条件满足时，系统将在单个事务中执行以下操作：

1. **设置代办标志：** 将 `agentFlag` 设置为 `1`
2. **更新代办人信息：**
   - `agentName` → 请求中的代办人姓名
   - `agentIdCardNo` → 请求中的代办人身份证号
   - `agentIdCardAttachmentId` → 请求中的代办人身份证附件ID
   - `agentMobile` → 请求中的代办人手机号
   - `agentRelation` → 请求中的代办人与患者关系
3. **更新时间戳：** 设置 `updateTime` 为当前时间
4. **持久化：** 将更新后的记录保存到数据库
5. **关联附件：** 如果附件尚未关联到申报记录，则更新附件的申报记录ID

## 响应格式

### 成功响应 (HTTP 200)

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "abd2a91e-6ff9-f835-a76f-801e6ba9b3aa",
    "patientName": "张三",
    "patientIdCardNo": "520524199001010157",
    "agentFlag": 1,
    "agentName": "李四",
    "agentIdCardNo": "520524199001010158",
    "agentIdCardAttachmentId": "789e0123-e89b-12d3-a456-426614174002",
    "agentMobile": "13800138001",
    "agentRelation": {
      "code": "WIFE",
      "description": "妻子"
    },
    "status": "DRAFT",
    "createTime": "2023-12-01T10:00:00",
    "updateTime": "2023-12-01T10:30:00"
  }
}
```

### 错误响应

#### 会话无效 (HTTP 200)
```json
{
  "code": 500,
  "msg": "会话无效",
  "traceId": "abc123def456"
}
```

#### 申报记录不存在 (HTTP 200)
```json
{
  "code": 500,
  "msg": "申报记录不存在",
  "traceId": "abc123def456"
}
```

#### 未授权访问 (HTTP 200)
```json
{
  "code": 500,
  "msg": "申报记录不属于当前用户",
  "traceId": "abc123def456"
}
```

#### 编辑不被允许 (HTTP 200)
```json
{
  "code": 500,
  "msg": "申报记录当前状态不允许修改：已提交（待审核）",
  "traceId": "abc123def456"
}
```

#### 附件验证失败 (HTTP 200)
```json
{
  "code": 500,
  "msg": "身份证附件不存在，附件ID: 789e0123-e89b-12d3-a456-426614174002",
  "traceId": "abc123def456"
}
```

#### 参数验证失败 (HTTP 200)
```json
{
  "code": 500,
  "msg": "代办人姓名不能为空",
  "traceId": "abc123def456"
}
```

## 请求示例

### cURL 示例

```bash
curl -X PUT \
  "https://api.example.com/api/chronic-disease-registration/abd2a91e-6ff9-f835-a76f-801e6ba9b3aa/agent" \
  -H "Authorization: 7cb73c8e-4328-4c32-a89c-1f5e8e2f027b" \
  -H "Content-Type: application/json" \
  -d '{
    "agentName": "李四",
    "agentIdCardNo": "520524199001010158",
    "agentIdCardAttachmentId": "789e0123-e89b-12d3-a456-426614174002",
    "agentMobile": "13800138001",
    "agentRelation": "WIFE"
  }'
```

### JavaScript 示例

```javascript
const response = await fetch('/api/chronic-disease-registration/abd2a91e-6ff9-f835-a76f-801e6ba9b3aa/agent', {
  method: 'PUT',
  headers: {
    'Authorization': '7cb73c8e-4328-4c32-a89c-1f5e8e2f027b',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    agentName: '李四',
    agentIdCardNo: '520524199001010158',
    agentIdCardAttachmentId: '789e0123-e89b-12d3-a456-426614174002',
    agentMobile: '13800138001',
    agentRelation: 'WIFE'
  })
});

const result = await response.json();
console.log(result);
```

## 业务逻辑说明

### 1. 代办标志自动设置
当成功更新代办人信息后，系统会自动将 `agentFlag` 设置为 `1`，表示该申报记录启用代办模式。

### 2. 附件关联管理
- 如果代办人身份证附件尚未关联到任何申报记录，系统会自动将其关联到当前申报记录
- 如果附件已经关联到当前申报记录，则不会重复更新关联关系
- 如果附件已经被其他申报记录使用，则会抛出验证异常

### 3. 数据验证
- 所有输入数据都会进行格式验证（身份证号、手机号等）
- 代办人姓名会进行XSS防护检查
- 代办人与患者关系必须是预定义的枚举值之一

### 4. 事务管理
整个更新操作在单个数据库事务中执行，确保数据一致性。如果任何步骤失败，所有更改都会回滚。

## 最佳实践

1. **错误处理：** 前端应根据 `code` 字段判断操作结果，并为用户提供友好的错误提示
2. **状态检查：** 在显示编辑按钮前，可以通过其他API检查记录的可编辑状态
3. **附件预检查：** 在提交更新前，确保代办人身份证附件已成功上传
4. **重试机制：** 对于系统异常（非业务异常），可以实现适当的重试机制
5. **日志追踪：** 出现错误时，记录 `traceId` 便于运维团队排查问题

## 安全考虑

1. **身份验证：** 必须提供有效的会话ID
2. **授权检查：** 用户只能操作属于自己的申报记录
3. **状态验证：** 只有特定状态的记录才允许编辑
4. **附件安全：** 确保附件属于当前用户且未被恶意使用
5. **事务完整性：** 所有数据修改都在单个事务中完成，确保数据一致性
6. **输入验证：** 所有输入都经过严格的格式和内容验证

## 性能特点

- **单次数据库查询：** 验证和数据获取合并
- **原子操作：** 所有更新在单个事务中完成
- **最小化字段更新：** 只更新必要的字段
- **索引优化：** 通过主键查询，性能最优
- **附件验证缓存：** 文件存在性检查可以利用文件系统缓存

## 实现状态

✅ **已完成实现**

该API端点已完全实现并包含以下组件：

### 1. 控制器层 (Controller)
- **文件位置：** `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt`
- **端点：** `PUT /api/chronic-disease-registration/{id}/agent`
- **功能：** 
  - 会话验证 (`@RequireSession`)
  - 参数验证 (`@Valid`)
  - 事务管理 (`@Transactional`)
  - 完整的Swagger文档注解

### 2. 服务层 (Service)
- **接口：** `space.lzhq.ph.service.ChronicDiseaseRegistrationService.updateAgent()`
- **实现：** `space.lzhq.ph.service.impl.ChronicDiseaseRegistrationServiceImpl.updateAgent()`
- **功能：**
  - 申报记录存在性验证
  - 用户权限验证
  - 编辑状态验证
  - 代办人身份证附件验证
  - 原子性数据更新
  - 附件关联更新

### 3. 数据传输对象 (DTO)
- **文件：** `space.lzhq.ph.dto.request.ChronicDiseaseAgentUpdateDto`
- **功能：**
  - 完整的参数验证注解
  - 身份证号格式验证
  - 手机号格式验证
  - XSS防护

### 4. 测试覆盖
- **文件：** `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java`
- **测试场景：**
  - ✅ 正常更新流程
  - ✅ 会话验证
  - ✅ 权限验证
  - ✅ 状态验证
  - ✅ 参数验证（所有必填字段）
  - ✅ 格式验证（身份证号、手机号）
  - ✅ 附件验证（存在性、权限、类型、使用状态）
  - ✅ 不同代办人关系类型测试
  - ✅ 边界条件测试

### 5. 异常处理
- **全局异常处理：** 通过 `GlobalExceptionHandler` 统一处理
- **自定义异常：**
  - `ResourceNotFoundException` - 申报记录不存在
  - `UnauthorizedResourceAccessException` - 权限不足
  - `EditNotAllowedException` - 状态不允许编辑
  - `AttachmentValidationException` - 附件验证失败

### 6. 安全特性
- **会话验证：** 必须提供有效的Authorization头
- **权限控制：** 只能操作属于自己的申报记录
- **状态检查：** 只有特定状态才允许编辑
- **附件安全：** 验证附件所有权和使用状态
- **输入验证：** 全面的参数格式和内容验证
- **XSS防护：** 对用户输入进行安全检查

### 7. 数据库事务
- **事务边界：** 整个更新操作在单个事务中执行
- **回滚策略：** 任何步骤失败都会回滚所有更改
- **数据一致性：** 确保申报记录和附件关联的一致性

该实现完全符合项目的编码规范和最佳实践，包括完整的测试覆盖、错误处理和安全验证。 