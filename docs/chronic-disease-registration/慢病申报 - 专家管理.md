# 慢病申报 - 专家管理

## 功能概述

慢病申报审核流程中，患者或其亲友通过微信小程序提交申报后，需要慢病专家在管理后台进行专业审核。为确保审核工作的规范性和专业性，系统需要为慢病专家提供专门的管理功能。

## 技术架构设计

### 用户模型设计
慢病专家采用系统用户模型进行管理，基于现有的 `SysUser` 实体类（`ruoyi-common/src/main/java/com/ruoyi/common/core/domain/entity/SysUser.java`），通过以下属性进行标识：

- **所属部门**: 医保办（部门ID: 4301）
- **用户角色**: 慢病申报-专家（角色ID: 17）  
- **用户类型**: 慢病专家（类型标识: 01）

### 数据存储策略
采用复用现有系统用户表的设计方案，无需创建独立的数据表，简化系统架构并保持数据一致性。

## 功能模块设计

### 慢病专家管理模块
开发专门的慢病专家管理功能模块，提供完整的专家生命周期管理。

#### 核心功能列表
- **专家列表展示**: 显示专家姓名、手机号、登录名等关键信息
- **专家信息管理**: 支持添加、编辑、删除等基础操作
- **密码管理**: 提供密码重置功能

#### 列表页面功能
- **信息展示**: 专家姓名、手机号、登录名
- **搜索功能**: 支持按姓名、手机号、登录名进行模糊搜索
- **操作按钮**: 添加、编辑、删除、重置密码

### 专家添加流程

#### 输入信息
- **专家姓名**（必填）：专家的真实姓名，用于系统识别和登录名生成
- **手机号码**（可选）：11位手机号码，可选填写
- **登录密码**（必填）：由管理员设定的登录密码
- **确认密码**（必填）：再次输入登录密码进行确认

#### 密码安全要求
- **密码长度**: 6-20个字符
- **复杂度要求**: 必须同时包含英文字母和数字
- **可选字符**: 可以包含特殊字符（如：!@#$%^&*_-等）
- **验证机制**: 前后端双重验证，确保密码安全性

#### 自动生成规则
- **登录名生成**: 专家姓名拼音首字母 + 手机号后4位（若无手机号则为4位随机数字）
- **密码处理**: 使用BCrypt算法进行加密存储，确保安全性

#### 验证与安全控制
- **唯一性校验**: 确保手机号码在系统中的唯一性
- **登录名冲突**: 自动处理登录名重复情况
- **密码确认**: 强制要求确认密码输入，防止录入错误
- **表单验证**: 前端JavaScript和后端Bean Validation双重验证

### 专家编辑功能

#### 可编辑字段
- 专家姓名
- 手机号码

#### 限制规则
- 登录名不允许修改（保持系统一致性）

### 专家删除功能

#### 删除策略
- 执行物理删除操作
- 确保数据彻底清理

### 密码重置功能

#### 重置流程
- 允许管理员为专家重置密码
- **密码要求**: 与新增时相同的安全规则
  - 密码长度6-20个字符
  - 必须同时包含英文字母和数字
  - 可包含特殊字符
- **确认机制**: 需要输入新密码和确认密码
- **安全加密**: 使用相同的加密算法确保安全性

#### 密码验证规则
- **前端验证**: JavaScript实时验证密码复杂度
- **后端验证**: 使用JSR-303注解进行服务端验证
- **一致性校验**: 确保新密码与确认密码一致

## 安全特性

### 密码安全
- **加密存储**: 所有密码使用BCrypt算法加密存储
- **盐值处理**: 每个密码使用独立的随机盐值
- **复杂度要求**: 强制执行密码复杂度规则
- **防止暴力破解**: 通过复杂度要求提高破解难度

### 表单安全
- **自动完成控制**: 
  - 用户名和手机号设置 `autocomplete="off"`
  - 密码字段设置 `autocomplete="new-password"`
- **前后端验证**: 双重验证机制确保数据安全性
- **唯一性校验**: 防止重复数据录入

## 业务价值

通过专业的慢病专家管理模块，系统能够：
- 确保审核人员的专业性和权威性
- 提供便捷的专家信息管理
- 保障系统安全性和数据完整性
- 支持慢病申报审核流程的规范化运作
- **增强密码安全性**: 通过手动密码设置和严格验证规则，提高账户安全性
- **优化用户体验**: 管理员可以根据实际需要设置合适的登录密码