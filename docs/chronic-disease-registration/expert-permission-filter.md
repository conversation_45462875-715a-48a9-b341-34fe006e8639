# 慢病申报专家权限过滤功能

## 功能概述

慢病专家能够对慢病申报记录进行查询、审核等操作。为了确保数据安全和权限分离，系统实现了专家权限过滤功能，慢病专家只能操作与其关联病种的申报记录。

## 功能特性

### 权限判断
- **专家角色识别**：系统通过用户角色列表判断当前用户是否为慢病专家（角色ID=17）
- **病种关联查询**：通过 `ChronicDiseaseExpertRelation` 表查询专家关联的病种ID列表
- **权限验证**：在所有操作前验证专家是否有权限操作指定病种的申报记录

### 适用范围

权限过滤应用于以下操作：

1. **查询操作**
   - 申报记录列表查询
   - 申报记录详情查看
   - 统计信息查询
   - 数据导出

2. **审核操作**
   - 单个申报记录审核
   - 批量申报记录审核

3. **管理操作**
   - 申报记录删除

## 实现逻辑

### 核心方法

#### `isChronicDiseaseExpert()`
```java
private boolean isChronicDiseaseExpert() {
    SysUser currentUser = ShiroUtils.getSysUser();
    if (currentUser == null || currentUser.getRoles() == null) {
        return false;
    }
    
    return currentUser.getRoles().stream()
            .anyMatch(role -> EXPERT_ROLE_ID.equals(role.getRoleId()));
}
```

#### `getExpertDiseaseIds(Long expertId)`
```java
private List<Integer> getExpertDiseaseIds(Long expertId) {
    List<Integer> diseaseIds = expertRelationMapper.selectDiseaseIdsByExpertId(expertId);
    return diseaseIds != null ? diseaseIds : Collections.emptyList();
}
```

### 权限过滤流程

1. **查询过滤**：在 `buildSearchQueryWrapper()` 方法中添加权限条件，考虑查询性能优化
   ```java
   // 优先处理有索引的条件（如提交时间、状态）
   if (searchForm.getSubmitDateStart() != null) {
       queryWrapper.ge(ChronicDiseaseRegistration::getSubmitTime, searchForm.getSubmitDateStart().atStartOfDay());
   }
   if (searchForm.getStatus() != null) {
       queryWrapper.eq(ChronicDiseaseRegistration::getStatus, searchForm.getStatus());
   }
   
   // 权限过滤和其他无索引条件放在后面
   if (isChronicDiseaseExpert()) {
       List<Integer> expertDiseaseIds = getExpertDiseaseIds(currentUser.getUserId());
       if (expertDiseaseIds.isEmpty()) {
           // 返回空结果
           queryWrapper.eq(ChronicDiseaseRegistration::getId, "IMPOSSIBLE_ID");
       } else {
           // 只查询关联病种的记录
           queryWrapper.in(ChronicDiseaseRegistration::getDiseaseId, expertDiseaseIds);
       }
   }
   
   // 无索引的基础条件放在最后
   queryWrapper.eq(ChronicDiseaseRegistration::getDeleteFlag, 0);
   ```

2. **操作权限检查**：在审核、删除等操作前验证权限
   ```java
   if (isChronicDiseaseExpert()) {
       if (!expertDiseaseIds.contains(registration.getDiseaseId())) {
           throw new UnauthorizedResourceAccessException("无权操作该病种的申报记录");
       }
   }
   ```

## 数据库设计

### 专家病种关联表
```sql
CREATE TABLE chronic_disease_expert_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    disease_id INT NOT NULL COMMENT '病种ID',
    expert_id BIGINT NOT NULL COMMENT '专家ID（用户ID）',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    INDEX idx_expert_id (expert_id),
    INDEX idx_disease_id (disease_id)
);
```

## 性能优化

### 查询性能优化
1. **索引优先原则**：优先使用有索引的查询条件（如 `submitTime`、`status`）
2. **条件顺序优化**：按索引有效性排序查询条件
   - 有索引的条件优先（`submitTime` 等）
   - 无索引的条件放后（`diseaseId`、`deleteFlag` 等）
3. **结果集缩小**：让有索引的条件先缩小结果集，再进行无索引的过滤
4. **避免索引失效**：确保主要查询能有效利用现有索引

### 查询条件优先级
```java
// 查询条件按索引有效性排序：
// 1. 有索引的条件优先
if (submitTime != null) {
    queryWrapper.ge(ChronicDiseaseRegistration::getSubmitTime, submitTime);
}
if (status != null) {
    queryWrapper.eq(ChronicDiseaseRegistration::getStatus, status);
}

// 2. 无索引的过滤条件放后
if (isExpert) {
    queryWrapper.in(ChronicDiseaseRegistration::getDiseaseId, expertDiseaseIds);
}
queryWrapper.eq(ChronicDiseaseRegistration::getDeleteFlag, 0);
```

### 查询优化策略
```sql
-- 优化前：无索引条件在前，索引失效
WHERE delete_flag = 0 AND disease_id IN (1,2,3) AND submit_time >= '2024-01-01'

-- 优化后：有索引条件优先，无索引条件靠后
WHERE submit_time >= '2024-01-01' AND status = 'SUBMITTED' AND disease_id IN (1,2,3) AND delete_flag = 0
```

## 安全考虑

1. **防止权限绕过**：所有涉及数据查询的方法都统一使用 `buildSearchQueryWrapper()` 进行过滤
2. **异常处理**：权限验证失败时抛出 `UnauthorizedResourceAccessException` 异常
3. **日志记录**：记录专家权限过滤的操作日志，便于审计
4. **边界情况**：处理专家无关联病种、用户为空等边界情况
5. **查询性能**：权限过滤不影响主要查询的性能，避免索引失效

## 测试验证

- 创建了 `ChronicDiseaseRegistrationExpertFilterTest` 测试类
- 覆盖专家角色判断、病种关联查询、权限过滤等核心功能
- 验证边界情况和异常处理逻辑

## 配置要求

1. **角色配置**：确保慢病专家用户具有角色ID=17的角色
2. **关联配置**：在 `chronic_disease_expert_relation` 表中配置专家与病种的关联关系
3. **权限配置**：确保专家用户具有相应的功能权限（查询、审核等）

## 注意事项

1. **权限范围**：权限过滤仅影响数据范围，不影响功能权限验证
2. **角色影响**：管理员和其他非专家角色不受此权限过滤影响
3. **实时生效**：专家关联病种发生变化时，权限过滤会立即生效
4. **关联审核**：建议定期审核专家病种关联关系的准确性
5. **性能监控**：关注查询性能，确保索引使用效果良好
6. **数据库优化**：如果 `diseaseId` 查询频繁，可考虑添加复合索引 