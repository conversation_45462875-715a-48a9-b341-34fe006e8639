# 产品路线图 (Roadmap)

## 1. 路线图概述

本路线图旨在规划“慢病申报”功能的开发迭代计划，明确各阶段的目标、核心功能和时间节点，确保项目有序推进并按时交付价值。

## 2. 版本规划策略

采用敏捷迭代的方式，优先交付核心价值功能（MVP），后续根据用户反馈和业务需求逐步完善和扩展。

- **MVP (Minimum Viable Product) 阶段**: 重点实现核心申报流程，满足用户最基本的在线申报需求。
- **V2.0 阶段**: 优化用户体验，增强辅助功能，提升系统稳定性和安全性。
- **未来迭代**: 根据运营数据和用户反馈，持续优化和探索新功能。

## 3. 详细版本规划

### 3.1 MVP 版本 (预计 X 周完成)

- **目标**: 实现用户能够在线完成慢病申报的核心流程，并能查询申报状态。
- **核心功能**:
    - 用户身份认证（患者本人/代办人信息录入，OCR识别身份证、医保卡）。
    - 慢病申报申请（选择申报人、选择医保类型 - 初步列表、选择慢病病种 - 初步列表、附件材料上传 - 基础版）。
    - 申报信息确认与提交。
    - 申报记录列表与基本状态查看（审核中、通过、不通过）。
    - 查看驳回原因（文本展示）。
- **关键里程碑**:
    - **第一周**: 详细设计完成，前后端接口定义完毕。
    - **第二至三周**: 核心功能开发与单元测试。
    - **第四周**: 集成测试，Bug修复，准备上线。

### 3.2 V2.0 版本 (MVP上线后 Y 周完成)

- **目标**: 提升用户体验，完善申报流程细节，增加消息通知和辅助说明。
- **核心功能**:
    - 优化OCR识别交互，提高识别成功率和用户体验。
    - 完善医保类型与病种、病种与附件的动态关联逻辑（依赖医院提供完整规则）。
    - 实现修改已驳回申报功能。
    - 增加申报状态变更的消息通知（微信模板消息/小程序内部消息）。
    - 建立帮助中心/FAQ模块。
    - 性能优化和安全加固。
- **关键里程碑**:
    - **需求细化与设计**: (时间待定)
    - **开发与测试**: (时间待定)
    - **上线发布**: (时间待定)

### 3.3 未来版本 (V2.0 上线后，根据实际情况规划)

- **潜在功能**:
    - 电子签名集成。
    - 与线下窗口数据同步。
    - 年度复审提醒。
    - 基于用户行为的数据分析与智能推荐。

## 4. 功能优先级矩阵

| 优先级 | 功能点                                                     | 所属版本 |
| ------ | ---------------------------------------------------------- | -------- |
| **P0** | 患者本人身份认证 (OCR识别身份证、医保卡)                     | MVP      |
| **P0** | 代办人信息录入 (OCR识别申报人身份证、医保卡)                 | MVP      |
| **P0** | 选择申报人                                                 | MVP      |
| **P0** | 选择医保类型 (初步列表)                                    | MVP      |
| **P0** | 选择慢病病种 (初步列表)                                    | MVP      |
| **P0** | 附件材料上传 (基础版，支持图片)                            | MVP      |
| **P0** | 申报信息确认与提交                                         | MVP      |
| **P0** | 申报记录列表展示 (申报人、病种、提交时间、状态)              | MVP      |
| **P0** | 查看申报详情 (基本信息、附件预览)                          | MVP      |
| **P0** | 查看审核状态 (审核中、通过、不通过)                        | MVP      |
| **P0** | 查看驳回原因 (文本展示)                                    | MVP      |
| **P1** | 修改已驳回申报                                             | V2.0     |
| **P1** | 申报状态变更的消息通知                                     | V2.0     |
| **P1** | 完善医保类型、病种、附件的动态关联规则 (需医院提供规则)      | V2.0     |
| **P1** | 优化OCR识别交互和准确率                                    | V2.0     |
| **P2** | 帮助中心/FAQ模块                                           | V2.0     |
| **P2** | 性能优化和安全加固                                         | V2.0     |
| **P3** | 电子签名集成                                               | 未来     |
| **P3** | 与线下窗口数据同步                                         | 未来     |
| **P3** | 年度复审提醒                                               | 未来     |

## 5. 详细时间线计划 (里程碑)

(此部分将在项目启动后，根据团队资源和实际情况进一步细化。以下为初步预估)

- **[日期]**: 项目启动会，需求评审确认。
- **[日期 + X周]**: MVP版本设计稿完成，技术方案评审通过。
- **[日期 + Y周]**: MVP版本核心功能开发完成，进入测试阶段。
- **[日期 + Z周]**: MVP版本测试完成，正式上线。
- **[MVP上线后A月]**: V2.0版本规划与设计启动。

## 6. 资源规划 (初步建议)

- **产品团队**: 1名产品经理。
- **设计团队**: 1名UI/UX设计师 (可兼任)。
- **开发团队**:
    - 前端工程师 (微信小程序): 1-2名。
    - 后端工程师 (Kotlin): 1名。
- **测试团队**: 1名测试工程师。
- **运维支持**: (依托现有运维团队)

## 7. 风险管理

| 风险描述                                       | 可能性 | 影响程度 | 应对措施                                                                 |
| ---------------------------------------------- | ------ | -------- | ------------------------------------------------------------------------ |
| 医院未能及时提供医保类型、病种、附件关联规则     | 中     | 高       | 提前沟通，明确时间节点；MVP版本使用初步通用规则，V2.0再完善。                |
| 腾讯OCR服务识别准确率未达预期                  | 低     | 中       | 允许用户手动修改；与腾讯云沟通优化；考虑备选OCR方案。                        |
| 与HIS/医保系统对接复杂，耗时过长               | 中     | 高       | 尽早启动接口调研和技术对接；MVP阶段可考虑部分信息手动处理，后续版本再深度集成。 |
| 用户（特别是老年患者）对线上操作接受度不高       | 中     | 中       | 加强用户引导和帮助说明；简化操作流程；鼓励家属代办。                         |
| 需求变更频繁                                   | 中     | 中       | 建立清晰的需求变更管理流程；加强前期需求沟通确认。                           |
| 开发资源不足或进度延期                         | 中     | 高       | 合理规划版本功能范围；加强项目进度跟踪；及时沟通调整计划。                   |