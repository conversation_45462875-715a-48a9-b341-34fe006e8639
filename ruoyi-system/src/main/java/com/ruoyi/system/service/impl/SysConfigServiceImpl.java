package com.ruoyi.system.service.impl;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.CacheUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.mapper.SysConfigMapper;
import com.ruoyi.system.service.ISysConfigService;
import jakarta.annotation.PostConstruct;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImpl implements ISysConfigService {
    private final SysConfigMapper configMapper;

    public SysConfigServiceImpl(SysConfigMapper configMapper) {
        this.configMapper = configMapper;
    }

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return configMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String configValue = Convert.toStr(CacheUtils.get(getCacheName(), getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = configMapper.selectConfig(config);
        if (StringUtils.isNotNull(retConfig)) {
            CacheUtils.put(getCacheName(), getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        return configMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(SysConfig config) {
        int row = configMapper.insertConfig(config);
        if (row > 0) {
            CacheUtils.put(getCacheName(), getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(SysConfig config) {
        SysConfig temp = configMapper.selectConfigById(config.getConfigId());
        if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey())) {
            CacheUtils.remove(getCacheName(), getCacheKey(temp.getConfigKey()));
        }

        int row = configMapper.updateConfig(config);
        if (row > 0) {
            CacheUtils.put(getCacheName(), getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 批量删除参数配置对象
     *
     * @param ids 需要删除的数据ID
     */
    @Override
    public void deleteConfigByIds(String ids) {
        Long[] configIds = Convert.toLongArray(ids);
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException("内置参数【%1$s】不能删除 ".formatted(config.getConfigKey()));
            }
            configMapper.deleteConfigById(configId);
            CacheUtils.remove(getCacheName(), getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
        for (SysConfig config : configsList) {
            CacheUtils.put(getCacheName(), getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        CacheUtils.removeAll(getCacheName());
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfig config) {
        long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId() != configId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 获取cache name
     *
     * @return 缓存名
     */
    private String getCacheName() {
        return Constants.SYS_CONFIG_CACHE;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return Constants.SYS_CONFIG_KEY + configKey;
    }

    /**
     * 每个用户最多绑定几个就诊人
     *
     * @return 最多绑定几个就诊人
     */
    @Override
    public int getMaxPatientPerClient() {
        int defaultValue = 5;
        String value = selectConfigByKey("ph.patient.maxCountPerUser");
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 预约挂号是否被禁用了
     */
    @Override
    public boolean isYyghDisabled() {
        return getBoolean("ph.yygh.disabled", false);
    }

    /**
     * 核酸检测预约是否被禁用了
     */
    @Override
    public boolean isCovidDisabled() {
        return getBoolean("ph.covid.disabled", false);
    }

    @Override
    public boolean isNatPushLisEnabled() {
        return getBoolean("ph.natPushLis.enabled", true);
    }

    @Override
    public String getAppHost() {
        return selectConfigByKey("sys.appHost");
    }

    @Override
    public String getNatAuthCode() {
        return selectConfigByKey("ph.natAuthCode");
    }

    @Override
    public int getNatMixCodeLength() {
        return Integer.parseInt(selectConfigByKey("ph.natMixCodeLength"));
    }

    @Override
    public int getMaxTijianYuyuePerDay() {
        return Integer.parseInt(selectConfigByKey("ph.maxTijianYuyuePerDay"));
    }

    @Override
    public int getMaxTijianYuyueDays() {
        return Integer.parseInt(selectConfigByKey("ph.maxTijianYuyueDays"));
    }

    @Override
    public List<String> getTijianTimeRanges() {
        String value = selectConfigByKey("ph.tijianTimeRanges");
        if (StrUtil.isBlank(value)) {
            value = "";
        }
        return Arrays.stream(value.split(",，")).map(String::trim).collect(Collectors.toList());
    }

    private static final List<String> aliasOfTrue = Arrays.asList("true", "1", "yes", "y", "on");

    @Override
    public boolean getBoolean(String attrName, boolean defaultValue) {
        String value = selectConfigByKey(attrName);
        return value == null ? defaultValue : aliasOfTrue.stream().anyMatch(it -> it.equalsIgnoreCase(value));
    }

    public void updateBoolean(String attrName, boolean value) {
        SysConfig configParams = new SysConfig();
        configParams.setConfigKey(attrName);
        SysConfig config = configMapper.selectConfig(configParams);
        config.setConfigValue(String.valueOf(value));
        updateConfig(config);
    }
}
