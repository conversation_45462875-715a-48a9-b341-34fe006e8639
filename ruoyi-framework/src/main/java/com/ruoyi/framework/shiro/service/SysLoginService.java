package com.ruoyi.framework.shiro.service;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.ShiroConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.UserStatus;
import com.ruoyi.common.exception.user.*;
import com.ruoyi.common.utils.*;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.service.*;
import org.dromara.hutool.core.text.StrUtil;
import org.mospital.bsoft.BSoftService;
import org.mospital.bsoft.Nurse;
import org.mospital.bsoft.Result;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    private final SysPasswordService passwordService;

    private final ISysDeptService deptService;

    private final ISysUserService userService;

    private final ISysRoleService roleService;

    private final ISysMenuService menuService;

    private final ISysConfigService configService;

    public SysLoginService(SysPasswordService passwordService, ISysDeptService deptService, ISysUserService userService, ISysRoleService roleService, ISysMenuService menuService, ISysConfigService configService) {
        this.passwordService = passwordService;
        this.deptService = deptService;
        this.userService = userService;
        this.roleService = roleService;
        this.menuService = menuService;
        this.configService = configService;
    }

    /**
     * 登录
     */
    public SysUser login(String username, String password) {
        // 验证码校验
        if (ShiroConstants.CAPTCHA_ERROR.equals(ServletUtils.getRequest().getAttribute(ShiroConstants.CURRENT_CAPTCHA))) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
        // 用户名或密码为空 错误
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }

        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }

        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, ShiroUtils.getIp())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }

        // 查询用户信息
        SysUser user = userService.selectUserByLoginName(username);

        if (user == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.not.exists")));
            throw new UserNotExistsException();
        }

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.delete")));
            throw new UserDeleteException();
        }

        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.blocked", user.getRemark())));
            throw new UserBlockedException();
        }

        passwordService.validate(user, password);

        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS,
                MessageUtils.message("user.login.success")));
        setRolePermission(user);
        recordLoginInfo(user.getUserId());
        return user;
    }

    public SysUser nurseLogin(String empno, String name) {
        if (!StringUtils.hasText(empno) || !StringUtils.hasText(name)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(empno, Constants.LOGIN_FAIL,
                    MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }

        Result<List<Nurse>> nursesResult = BSoftService.Companion.getNurses();
        if (!nursesResult.isOk()) {
            throw new UserNotExistsException();
        }

        List<Nurse> nurses = nursesResult.getData();
        assert nurses != null;
        Optional<Nurse> nurseOptional = nurses.stream().filter(it -> it.getUserId().equals(empno)).findFirst();
        if (nurseOptional.isEmpty() || !nurseOptional.get().getUserName().equals(name)) {
            throw new UserNotExistsException();
        }

        Long deptId = Long.parseLong(StrUtil.removePrefix(nurseOptional.get().getDeptId(), "0"));
        String deptName = nurseOptional.get().getDeptName();
        String loginName = "N" + empno;
        SysUser user = userService.selectUserByLoginName(loginName);
        if (user == null) {
            SysDept dept = deptService.selectDeptById(deptId);
            if (dept == null) {
                dept = new SysDept();
                dept.setDeptId(deptId);
                dept.setParentId(1L);
                dept.setAncestors("0,1");
                dept.setDeptName(deptName);
                dept.setOrderNum(0);
                dept.setStatus("0");
                dept.setDelFlag("0");
                dept.setCreateBy("admin");
                dept.setCreateTime(new Date());
                dept.setUpdateBy("admin");
                dept.setUpdateTime(new Date());
                deptService.insertDept(dept);
            }

            user = new SysUser();
            user.setUserName(name);
            user.setDeptId(deptId);
            user.setLoginName(loginName);
            user.setSalt(ShiroUtils.randomSalt());
            user.setPassword(passwordService.encryptPassword(loginName, PinyinUtils.getFullPinyin(name, "", true),
                    user.getSalt()));
            user.setCreateBy("admin");
            userService.insertUser(user);
            userService.insertUserAuth(user.getUserId(), new Long[]{8L});
            user.setRoles(roleService.selectRolesByUserId(user.getUserId()));
        }

        AsyncManager.me().execute(
                AsyncFactory.recordLogininfor(
                        loginName,
                        Constants.LOGIN_SUCCESS,
                        MessageUtils.message("user.login.success")
                )
        );
        setRolePermission(user);
        recordLoginInfo(user.getUserId());
        return user;
    }

    /**
     * 设置角色权限
     *
     * @param user 用户信息
     */
    public void setRolePermission(SysUser user) {
        List<SysRole> roles = user.getRoles();
        if (!roles.isEmpty() && roles.size() > 1) {
            // 多角色设置permissions属性，以便数据权限匹配权限
            for (SysRole role : roles) {
                Set<String> rolePerms = menuService.selectPermsByRoleId(role.getRoleId());
                role.setPermissions(rolePerms);
            }
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setLoginIp(ShiroUtils.getIp());
        user.setLoginDate(DateUtils.getNowDate());
        userService.updateUserInfo(user);
    }
}
