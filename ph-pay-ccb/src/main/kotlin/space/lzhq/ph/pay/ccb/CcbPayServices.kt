package space.lzhq.ph.pay.ccb

import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import com.ruoyi.common.core.domain.AjaxResult
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.log.Log
import org.dromara.hutool.poi.csv.CsvUtil
import space.lzhq.ph.common.*
import space.lzhq.ph.pay.ccb.bean.*
import java.io.BufferedReader
import java.io.File
import java.io.InputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.nio.charset.StandardCharsets
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.zip.ZipFile

object CcbPayServices {
    private val log: Log = Log.get()

    /**
     * 统一下单
     */
    fun createUnifiedOrder(
        tradeType: TradeType = TradeType.MINIPRO,
        openid: String,
        clientIp: String,
        amount: BigDecimal,
        serviceType: ServiceType = ServiceType.MZ,
        callback: (UnifiedOrderRequest) -> Unit
    ): AjaxResult {
        return try {
            val unifiedOrderRequest = UnifiedOrderRequest(
                tradeType = tradeType,
                openid = openid,
                clientIp = clientIp,
                orderId = PaymentKit.newOrderId(serviceType),
                amount = amount
            )

            val ajaxResult: AjaxResult = unifiedOrderRequest.execute()
            if (ajaxResult.isOk) {
                callback(unifiedOrderRequest)
            }

            ajaxResult
        } catch (e: Exception) {
            log.debug(e.message, e)
            AjaxResult.error(e.message ?: "未知错误")
        }
    }

    /**
     * 创建线下扫码付订单
     */
    fun createMicroPay(
        qrCode: String,
        amount: Int,
        cardNo: String = "",
        serviceType: ServiceType = ServiceType.MZ,
        callback: (MicropayRequest) -> Unit
    ): AjaxResult {
        return try {
            val micropayRequest = MicropayRequest(
                orderId = PaymentKit.newOrderId(serviceType),
                qrCode = qrCode,
                amount = PaymentKit.fenToYuan(amount.toLong()),
                remark1 = cardNo
            )
            val ajaxResult = micropayRequest.execute()
            callback(micropayRequest)
            ajaxResult
        } catch (e: Exception) {
            log.debug(e.message, e)
            AjaxResult.error(e.message ?: "未知错误")
        }
    }

    fun refund(refundParams: RefundParams): RefundResponse {
        val param = refundParams.toParam()
        log.debug("建行退款请求：${param}")
        val res = Client.send(param)
        log.debug("建行退款响应：${res}")
        return RefundResponse.from(res)
    }

    /**
     * 生成下载账单，并获取本地账单文件路径
     */
    fun generateBill(date: LocalDate): File {
        // 生成账单
        val dateStr = date.format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN))
        val generateBillParams = GenerateBillParams(dateStr).toParam()
        log.debug("建行生成账单请求：${generateBillParams}")
        val generateBillResponse = GenerateBillResponse.from(Client.send(generateBillParams))
        check(generateBillResponse.isSuccess) { "[${generateBillResponse.code}]生成账单出错：${generateBillResponse.msg}" }

        // 下载账单
        val downBillParam = DownloadBillParams(generateBillResponse.fileName).toParam()
        val downBillResponse = Response.fromResponseXml(Client.send(downBillParam))
        check(downBillResponse.isSuccess) { "[${downBillResponse.code}]下载账单出错：${downBillResponse.msg}" }
        return File("${Configuration.billDownloadPath}${File.separatorChar}${generateBillResponse.fileName}")
    }

    private fun isBillDate(date: LocalDate, file: File): Boolean {
        val nameArray = file.name.split(".")
        if (nameArray.size < 3) {
            return false
        }
        val expectDate = date.format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN))
        if (expectDate == nameArray[2]) {
            return true
        }
        return false
    }

    /**
     * 尝试读取本地的账单
     */
    fun findLocalBill(date: LocalDate): File {
        val file = File(Configuration.billDownloadPath).walk().find { isBillDate(date, it) }
        check(file != null) { "找不到账单文件！" }
        log.debug("找到账单文件：${file.name}")
        return file
    }

    /**
     * 解析BufferedReader为WxPayBillResult
     */
    private fun parseLocalBill(billFile: File, csvFilePath: String): WxPayBillResult? {
        val zipFile = ZipFile(billFile)
        zipFile.use {
            val stream: InputStream = zipFile.getInputStream(zipFile.entries().nextElement())
            val reader: BufferedReader = stream.bufferedReader(charset = StandardCharsets.UTF_8)

            val csvLines: MutableList<Array<String>> = mutableListOf()

            // 第一行：合计行
            reader.readLine()

            // 第二行：标题行，交易时间 记账日期 银行流水号 商户流水号 订单号 订单状态 付款方账号/客户号 付款方户名 订单金额 交易金额 手续费 结算金额 柜台代码 发卡行/通道 支付卡种 交易类型 期数 授权号 项目号 基本户 备注一 备注二
            val headerLine: String = reader.readLine()
            csvLines.add(headerLine.split(Regex("\\s+")).toTypedArray())

            val infoList = mutableListOf<WxPayBillInfo>()
            // 开始添加订单明细
            var payAmount: BigDecimal = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)
            var refundAmount: BigDecimal = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)

            reader.forEachLine {
                val wxPayBillInfo = CcbBillUtil.parserItem(it)
                if (wxPayBillInfo.isMenzhen() || wxPayBillInfo.isZhuyuan()) {
                    infoList.add(CcbBillUtil.parserItem(it))
                    if (wxPayBillInfo.isPay()) {
                        payAmount += wxPayBillInfo.totalFee.toBigDecimal()
                    } else {
                        refundAmount += wxPayBillInfo.settlementRefundFee.toBigDecimal()
                    }

                    csvLines.add(it.split("\t").map { s -> "`$s" }.toTypedArray())
                }
            }

            CsvUtil.getWriter(csvFilePath, StandardCharsets.UTF_8).use { it.write(csvLines) }

            return WxPayBillResult().apply {
                billInfoList = infoList
                totalRecord = infoList.size.toString()
                this.setPayAmount(payAmount)
                this.setRefundAmount(refundAmount)
            }
        }
    }

    /**
     * 生成建行支付账单，解析成 bean
     */
    fun downloadAndSaveBill(billDate: LocalDate): WxPayBillResult? {
        return try {
            val billFile: File = try {
                findLocalBill(billDate)
            } catch (e: Exception) {
                generateBill(billDate)
            }

            parseLocalBill(billFile, buildCsvBillPath(billDate))
            // 如果报错，则返回空账单，数据都为0
        } catch (e: IllegalStateException) {
            log.debug(e.message)
            WxPayBillResult().apply {
                totalFee = ""
                totalRecord = ""
                totalRefundFee = ""
                totalAmount = ""
                totalAppliedRefundFee = ""
            }
        }
    }

    /**
     * 查询订单号
     */
    fun queryOrder(orderNo: String): QueryOrderResponse {
        val param = QueryOrderParams(orderNo).toParam()
        log.debug("建行查订单请求：${param}")
        val res = Client.send(param)
        log.debug("建行查订单响应：${res}")
        return QueryOrderResponse.from(res)
    }

    private fun formatBillDate(billDate: LocalDate): String =
        billDate.format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN))

    fun buildCsvBillPath(billDate: LocalDate): String =
        Configuration.billDownloadPath + File.separatorChar + "${formatBillDate(billDate)}_wxbill.csv"

    fun getOrDownloadCsvBillFile(billDate: LocalDate): File {
        val filePath = buildCsvBillPath(billDate)
        val file = File(filePath)
        if (file.exists()) {
            return file
        }

        downloadAndSaveBill(billDate)
        return File(filePath)
    }
}
