package space.lzhq.ph.pay.ccb

import org.dromara.hutool.setting.Setting

/**
 * @param merchantId 商户代码
 * @param posId 商户柜台代码
 * @param branchId 分行代码
 * @param txCode 交易码
 * @param type 接口类型
 * @param pub 商户公钥
 * @param gateway 网关类型
 * @param regInfo 客户注册信息
 * @param subAppId 公众号或小程序的appId
 */
object Configuration {
    private val setting = Setting("ccb.setting")
    val url: String = setting["url"] ?: "https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain?CCB_IBSVersion=V6"
    val merchantId = setting["merchantId"]!!
    val posId = setting["posId"]!!
    val branchId = setting["branchId"]!!
    val type = setting["type"] ?: "1"
    val pub = setting["pub"]!!
    val gateway = setting["gateway"] ?: "0"
    val regInfo = setting["regInfo"] ?: ""
    val subAppId = setting["subAppId"]!!
    val timeoutMinutes = setting.getInt("timeoutMinutes", 30)
    val userId = setting["userId"]!!
    val password = setting["password"]!!
    val billDownloadPath = setting["billDownloadPath"]!!
    val localClientUrl = setting["localClientUrl"]!!
}