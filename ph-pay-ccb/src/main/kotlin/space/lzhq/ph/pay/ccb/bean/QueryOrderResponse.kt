package space.lzhq.ph.pay.ccb.bean

import com.ruoyi.common.constant.Constants
import org.joox.JOOX

class QueryOrderResponse(code: String, msg: String) : Response(code = code, msg = msg) {

    /**
     * 交易日期
     */
    var tranDate: String = ""

    /**
     * 记账日期
     */
    var accDate: String = ""

    /**
     * 订单号
     */
    var orderNo: String = ""

    /**
     * 银行流水号
     */
    var trackNo: String = ""

    /**
     * 付款方账号
     */
    var account: String = ""

    /**
     * 支付金额
     */
    var paymentMoney: String = ""

    /**
     * 退款金额
     */
    var refundMoney: String = ""

    /**
     * 柜台号
     */
    var posId: String = ""

    /**
     * 备注1
     */
    var remark1: String = ""

    /**
     * 备注2
     */
    var remark2: String = ""

    /**
     * 订单状态
     */
    var orderStatus: Int = 0

    val orderStatusDesc: String
        get() = when (orderStatus) {
            0 -> Constants.PAY_FAIL
            1 -> Constants.PAY_OK
            2 -> "待银行确认"
            3 -> "已退部分资金"
            4 -> "已全额退款"
            5 -> "待银行确认"
            else -> "未知"
        }

    companion object {

        /**
         * 创建退款响应
         */
        fun from(xml: String): QueryOrderResponse {
            val dom = JOOX.`$`(xml)
            val code = dom.find("RETURN_CODE").text()
            val msg = dom.find("RETURN_MSG").text()
            val response = QueryOrderResponse(code = code, msg = msg)
            if (response.isSuccess) {
                response.apply {
                    tranDate = dom.find("TRAN_DATE").text()
                    accDate = dom.find("ACC_DATE").text()
                    orderNo = dom.find("ORDER").text()
                    trackNo = dom.find("OriOvrlsttnEV_Trck_No").text()
                    account = dom.find("ACCOUNT").text()
                    paymentMoney = dom.find("PAYMENT_MONEY").text()
                    refundMoney = dom.find("REFUND_MONEY").text()
                    posId = dom.find("POS_ID").text()
                    remark1 = dom.find("REM1").text()
                    remark2 = dom.find("REM2").text()
                    orderStatus = dom.find("ORDER_STATUS").text().toInt()
                }
            }
            return response
        }
    }

}