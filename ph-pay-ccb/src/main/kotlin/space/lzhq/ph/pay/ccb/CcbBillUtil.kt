package space.lzhq.ph.pay.ccb

import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import org.apache.commons.lang3.StringUtils
import java.io.BufferedReader
import java.io.InputStream
import java.util.zip.ZipFile

/**
 * CCB 账单封装工具类
 */
object CcbBillUtil {

    /**
     * 读取账单头，返回一个字符串数组，按索引分别是
     * 0. 出单日期
     * 1. 交易日期
     * 2. 交易笔数
     * 3. 交易金额
     * 4. 手续费
     * 5. 结算金额
     *
     */
    fun getHeader(source: String): Array<String> {
        return StringUtils.substringsBetween(source, "[", "]")
    }

    /**
     * 如果是负数（退款），则去掉符号
     */
    private fun hideMinus(num: String): String {
        if (num.startsWith("-")) {
            return num.substring(1)
        }
        return num
    }

    /**
     * 解析项目
     */
    fun parserItem(itemStr: String): WxPayBillInfo {
        val itemArray = itemStr.split("\t")
        check(itemArray.size == 23)
        return WxPayBillInfo().apply {
            // 商户柜台代码
            this.appId = itemArray[12]
            this.tradeTime = itemArray[0]
            this.transactionId = itemArray[2]
            this.outTradeNo = itemArray[4]
            this.tradeType = itemArray[15]
            this.tradeState = itemArray[5]
            this.bankType = itemArray[14]
            this.openId = itemArray[7]
            this.totalFee = itemArray[8]
            this.outRefundNo = itemArray[3]
            this.refundId = itemArray[3]
            // 退款金额
            this.settlementRefundFee = hideMinus(itemArray[11])
            this.appliedRefundAmount = hideMinus(itemArray[11])
            this.refundState = itemArray[5]
            this.totalAmount = itemArray[8]
        }
    }

    fun readTextFromZip(zipPath: String): BufferedReader {
        val zipFile = ZipFile(zipPath)
        val entries = zipFile.entries()
        val entry = entries.nextElement()
        val stream: InputStream = zipFile.getInputStream(entry)
        return BufferedReader(stream.reader())
    }

}