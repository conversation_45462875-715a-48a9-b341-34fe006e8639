package space.lzhq.ph.pay.ccb.bean

import org.joox.JOOX

/**
 * 退款响应
 */
class RefundResponse(code: String, msg: String) : Response(code = code, msg = msg) {

    /**
     * 退款金额
     */
    var payAmount: Double = 0.0

    /**
     * 订单总金额
     */
    var amount: Double = 0.0

    companion object {

        /**
         * 创建退款响应
         */
        fun from(xml: String): RefundResponse {
            val dom = JOOX.`$`(xml)
            val code = dom.find("RETURN_CODE").text()
            val msg = dom.find("RETURN_MSG").text()
            val response: RefundResponse = RefundResponse(code = code, msg = msg)
            if (response.isSuccess) {
                response.apply {
                    payAmount = dom.find("PAY_AMOUNT").text().toDouble()
                    amount = dom.find("AMOUNT").text().toDouble()
                }
            }
            return response
        }
    }

}