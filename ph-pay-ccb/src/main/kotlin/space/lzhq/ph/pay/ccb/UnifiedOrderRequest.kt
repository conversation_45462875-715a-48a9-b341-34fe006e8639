package space.lzhq.ph.pay.ccb

import com.ruoyi.common.core.domain.AjaxResult
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.text.escape.EscapeUtil
import org.dromara.hutool.crypto.digest.DigestUtil
import org.dromara.hutool.http.HttpUtil
import org.dromara.hutool.http.client.body.StringBody
import org.dromara.hutool.http.client.engine.ClientEngineFactory
import org.dromara.hutool.json.JSONUtil
import org.dromara.hutool.log.Log
import org.dromara.hutool.log.LogFactory
import java.math.BigDecimal
import java.nio.charset.StandardCharsets
import java.util.*

/**
 * @param orderId 订单号，最长30位
 * @param amount 金额，以元为单位
 * @param currencyCode 币种编码，01=人民币
 * @param remark1 备注1
 * @param remark2 备注2
 */
data class UnifiedOrderRequest(
    val tradeType: TradeType,
    val openid: String,
    val clientIp: String,
    val orderId: String,
    val amount: BigDecimal,
    val currencyCode: String = "01",
    val remark1: String = "",
    val remark2: String = "",
    val productInfo: String = "",
    val referer: String = ""
) {

    companion object {
        fun escape(s: String): String =
            EscapeUtil.escape(s).uppercase(Locale.getDefault()).replace(Regex.fromLiteral("%U"), "%u")

        val log: Log = LogFactory.getLog(UnifiedOrderRequest::class.java)

        val okHttpEngine = ClientEngineFactory.createEngine("OkHttp")
    }

    private val params: Array<Pair<String, String>> = arrayOf(
        "MERCHANTID" to Configuration.merchantId,
        "POSID" to Configuration.posId,
        "BRANCHID" to Configuration.branchId,
        "ORDERID" to orderId,
        "PAYMENT" to amount.toString(),
        "CURCODE" to currencyCode,
        "TXCODE" to "530590",
        "REMARK1" to remark1,
        "REMARK2" to remark2,
        "TYPE" to Configuration.type,
        // 商户公钥取后30位
        "PUB" to Configuration.pub.takeLast(30),
        "GATEWAY" to Configuration.gateway,
        "CLIENTIP" to clientIp,
        "REGINFO" to escape(Configuration.regInfo),
        "PROINFO" to escape(productInfo),
        "REFERER" to referer,
        "TIMEOUT" to DateUtil.format(
            DateUtil.offsetMinute(Date(), Configuration.timeoutMinutes),
            DatePattern.PURE_DATETIME_FORMAT
        ),
        "TRADE_TYPE" to tradeType.name,
        "SUB_APPID" to Configuration.subAppId,
        "SUB_OPENID" to openid
    )

    private fun mac(): String {
        val raw: String = params.joinToString(separator = "&") { "${it.first}=${it.second}" }
        return DigestUtil.md5Hex(raw, Charsets.UTF_8)
    }

    private fun params(): Map<String, String> = mutableMapOf(*params).apply { put("MAC", mac()) }

    private fun exchangeUrl(): String {
        val params: Map<String, String> = params()
        log.debug("***** CCBPAY EXCHANGE URL *****")
        log.debug("URL: ${Configuration.url}")
        log.debug("Request: ${JSONUtil.toJsonPrettyStr(params)}")

        val respText: String = HttpUtil.createPost(Configuration.url).form(params).send(okHttpEngine).bodyStr()
        log.debug("Response: $respText")

        return JSONUtil.parseObj(respText).getStr("PAYURL")
    }

    fun execute(): AjaxResult {
        log.debug("***** CCBPAY Unified Order *****")

        val url: String = exchangeUrl()
        log.debug("URL: $url")

        val respText: String =
            HttpUtil.createPost(url).body(StringBody("", StandardCharsets.UTF_8)).send(okHttpEngine).bodyStr()
        log.debug("Response: $respText")

        val json: com.alibaba.fastjson.JSONObject = com.alibaba.fastjson.JSONObject.parseObject(respText)

        return if (json.getString("ERRCODE") == "000000") {
            json.remove("SUCCESS")
            json.remove("ERRCODE")
            json.remove("ERRMSG")
            json.remove("TXCODE")
            json.put("zyOrderNo", this.orderId)
            AjaxResult.success(json)
        } else {
            AjaxResult.error(json.getString("ERRCODE") + ": " + json.getString("ERRMSG"))
        }
    }
}
