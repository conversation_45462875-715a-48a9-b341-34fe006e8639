package space.lzhq.ph.pay.ccb

import COM.CCB.EnDecryptAlgorithm.MCipherEncryptor
import com.ruoyi.common.core.domain.AjaxResult
import org.dromara.hutool.core.text.escape.EscapeUtil
import org.dromara.hutool.http.HttpUtil
import org.dromara.hutool.json.JSONObject
import org.dromara.hutool.json.JSONUtil
import org.dromara.hutool.log.Log
import java.math.BigDecimal
import java.util.*

/**
 * 线下扫码付请求
 * @param orderId 订单号，最长30位
 * @param amount 金额
 * @param currencyCode 币种编码，01=人民币
 * @param remark1 备注1
 * @param remark2 备注2
 * @param merflag 1为线上用户，2为线下用户
 */
data class MicropayRequest(
    val orderId: String,
    val qrCode: String,
    val amount: BigDecimal,
    val remark1: String = "",
    val remark2: String = "",
    val productInfo: String = "",
    val merflag: String = "1"
) {

    companion object {
        private val encryptor = MCipherEncryptor(Configuration.pub.takeLast(30))
        fun escape(s: String): String =
            EscapeUtil.escape(s).uppercase(Locale.getDefault()).replace(Regex.fromLiteral("%U"), "%u")
    }

    private val log: Log = Log.get()

    private val params: Array<Pair<String, String>> = arrayOf(
        "MERFLAG" to merflag,
        "MERCHANTID" to Configuration.merchantId,
        "POSID" to Configuration.posId,
        "BRANCHID" to Configuration.branchId,
        "ORDERID" to orderId,
        "QRCODE" to qrCode,
        "AMOUNT" to amount.toString(),
        "TXCODE" to "PAY100",
        "PROINFO" to escape(productInfo),
        "REMARK1" to remark1,
        "REMARK2" to remark2,
        "TYPE" to Configuration.type
    )

    private fun mac(): String {
        val raw: String = params.sliceArray(0..7).joinToString(separator = "&") { "${it.first}=${it.second}" }
        return encryptor.doEncrypt(raw)
    }

    private fun params(): Map<String, String> {
        val map = params.sliceArray(1..3).toMap().toMutableMap()
        map["ccbParam"] = mac()
        return map
    }

    private fun post(): JSONObject {
        val params: Map<String, String> = params()
        log.debug("***** CCBPAY EXCHANGE URL *****")
        log.debug("URL: ${Configuration.url}")
        log.debug("Request: ${JSONUtil.toJsonPrettyStr(params)}")

        val respText: String = HttpUtil.post("https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_ENPAY", params)
        log.debug("Response: $respText")

        return JSONUtil.parseObj(respText)
    }

    fun execute(): AjaxResult {
        val json: JSONObject = post()
        val result = json.getStr("RESULT")
        val ajaxResult: AjaxResult = AjaxResult().apply {
            this[AjaxResult.MSG_TAG] = json.getStr("ERRCODE") + ": " + json.getStr("ERRMSG", "未知错误")
            this["ORDERID"] = json.getStr("ORDERID")
            this["CODE"] = result
        }
        return if (result == "Y") {
            ajaxResult.ok()
        } else if (result == "N") {
            ajaxResult.fail()
        } else if (result == "U") {
            ajaxResult.fail()
        } else {
            ajaxResult.fail()
        }
    }

}