# 用户故事地图 (User Story Map)

## 1. 用户故事地图概述

本文档旨在通过用户故事地图的形式，清晰地展现慢病申报功能的用户旅程、核心活动、用户任务以及对应的版本规划，确保产品迭代聚焦于核心用户价值。

## 2. 用户活动流 (横向)

| 用户活动 (Activities) | 首次使用与身份认证 | 填写与提交申报资料 | 查看申报进度与结果 | 资料管理与复用 |
|---|---|---|---|---|

## 3. 用户任务分解 (纵向) 与版本映射

| 用户活动 (Activities) | 用户任务 (Tasks) | 用户故事 (User Stories) | 优先级 | 版本 (Release) | 备注 |
|---|---|---|---|---|---|
| **首次使用与身份认证** | 微信授权登录 | 作为用户，我想要通过微信授权快速登录小程序，以便捷地使用服务。 | P0 | MVP | |
| | 申报人认证 | 作为用户，我想要通过上传申报人身份证和医保卡照片进行OCR识别，并填写申报人手机号，以完成申报人身份认证和获取医保信息。 | P0 | MVP | 依赖腾讯OCR |
| | 选择与申报人关系 | 作为用户，我想要选择我与申报人的关系（本人、配偶、子女、父母、亲友等），以确定是否需要进行代办人认证。 | P0 | MVP | |
| | 代办人认证 | 作为代办人，当我不是申报人本人时，我想要上传我的身份证并填写手机号，以便完成代办人身份认证。 | P0 | MVP | 依赖腾讯OCR |
| | 管理家庭成员 | 作为用户，我想要添加和管理家庭成员信息（如父母、子女），以便为他们代办慢病申报。 | P1 | V2.0 | 需要考虑代办人与患者关系证明 |
| **填写与提交申报资料** | 选择慢病病种 | 作为用户，我想要根据已识别的医保类型选择符合条件的慢病病种进行申报。 | P0 | MVP | 需医院提供医保类型与病种关联规则 |
| | 填写申报表单 | 作为用户，我想要系统能根据我选择的病种，动态展示需要填写的表单项，并能部分预填已知信息（如姓名、身份证号）。 | P0 | MVP | |
| | 上传附件材料 | 作为用户，我想要根据病种要求，方便地上传所需的附件材料（如诊断证明、病历复印件等），支持拍照上传和从相册选择。 | P0 | MVP | 需医院提供病种与附件关联规则 |
| | 确认并提交申报 | 作为用户，我想要在提交前预览所有填写的信息和上传的附件，确认无误后提交申报。 | P0 | MVP | |
| **查看申报进度与结果** | 查看申报记录列表 | 作为用户，我想要查看我所有提交的慢病申报记录列表，包含申报人、病种、提交时间、当前状态。 | P0 | MVP | |
| | 查看申报详情与状态 | 作为用户，我想要点击某条申报记录，查看详细的申报信息以及当前的审批状态（审核中、通过、不通过）。 | P0 | MVP | |
| | 查看驳回原因与整改意见 | 作为用户，如果我的申报被驳回，我想要清晰地看到驳回原因和具体的整改意见，以便我修改后重新提交。 | P0 | MVP | |
| | 接收状态变更通知 | 作为用户，我想要在申报状态发生变更时（如审核通过、驳回），能收到微信消息通知。 | P1 | V2.0 | 依赖微信消息推送 |
| **资料管理与复用** | 查看/修改个人信息 | 作为用户，我想要查看和修改我的个人基本信息（如联系方式）。 | P2 | V2.0 | |
| | 查看/修改家庭成员信息 | 作为用户，我想要查看和修改已添加的家庭成员信息。 | P2 | V2.0 | |
| | 复用历史附件 | 作为用户，在多次申报或为不同家庭成员申报时，我希望能复用已上传过的通用附件（如身份证照片），减少重复上传。 | P2 | 未来版本 | 提升体验 |
| | 年度复审提醒 | 作为用户，我想要在慢病资格临近到期前收到复审提醒，并能便捷地进行复审申报。 | P2 | 未来版本 | 提升用户粘性 | 