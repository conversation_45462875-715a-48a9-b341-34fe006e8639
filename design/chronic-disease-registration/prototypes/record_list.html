<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申报记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            line-height: 1.5;
        }
        .safe-area {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
        }
        .header {
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .back-button {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tab-bar {
            display: flex;
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 50px;
            z-index: 9;
        }
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            font-size: 14px;
            color: #666;
            position: relative;
        }
        .tab-item.active {
            color: #07c160;
            font-weight: 500;
        }
        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background-color: #07c160;
            border-radius: 1.5px;
        }
        .record-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            overflow: hidden;
        }
        .record-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .record-title {
            font-size: 15px;
            font-weight: 500;
        }
        .record-status {
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 4px;
        }
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        .status-rejected {
            background-color: #fff2f0;
            color: #ff4d4f;
        }
        .record-body {
            padding: 16px;
        }
        .record-info {
            display: flex;
            margin-bottom: 8px;
        }
        .record-info:last-child {
            margin-bottom: 0;
        }
        .record-label {
            width: 70px;
            font-size: 13px;
            color: #999;
            flex-shrink: 0;
        }
        .record-value {
            flex: 1;
            font-size: 13px;
            color: #333;
        }
        .record-footer {
            padding: 12px 16px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        .record-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
        }
        .btn-primary {
            background-color: #07c160;
            color: #fff;
            border: none;
        }
        .btn-outline {
            background-color: #fff;
            color: #07c160;
            border: 1px solid #07c160;
        }
        .btn-text {
            background-color: transparent;
            color: #666;
            border: none;
        }
        .empty-state {
            padding: 40px 0;
            text-align: center;
        }
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border-radius: 50%;
            color: #bbb;
            font-size: 32px;
        }
        .empty-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 16px;
        }
        .empty-btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #07c160;
            color: #fff;
            border-radius: 4px;
            font-size: 14px;
        }
        .loading-state {
            padding: 20px 0;
            text-align: center;
            color: #999;
            font-size: 14px;
        }
        .loading-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #07c160;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="safe-area">
        <!-- 顶部导航 -->
        <div class="header px-4 py-3 flex items-center">
            <div class="back-button mr-4">
                <i class="fas fa-chevron-left"></i>
            </div>
            <h1 class="text-lg font-medium flex-1 text-center">申报记录</h1>
            <div class="w-6"></div>
        </div>

        <!-- 标签栏 -->
        <div class="tab-bar">
            <div class="tab-item active">全部</div>
            <div class="tab-item">审核中</div>
            <div class="tab-item">已通过</div>
            <div class="tab-item">未通过</div>
        </div>

        <!-- 内容区域 -->
        <div class="p-4">
            <!-- 申报记录列表 -->
            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">张三 - 高血压</div>
                    <div class="record-status status-pending">审核中</div>
                </div>
                <div class="record-body">
                    <div class="record-info">
                        <div class="record-label">申报时间</div>
                        <div class="record-value">2023-06-15 10:23:45</div>
                    </div>
                    <div class="record-info">
                        <div class="record-label">医保类型</div>
                        <div class="record-value">城镇职工医保</div>
                    </div>
                    <div class="record-info">
                        <div class="record-label">预计完成</div>
                        <div class="record-value">2023-06-20前</div>
                    </div>
                </div>
                <div class="record-footer">
                    <button class="record-btn btn-text">取消申报</button>
                    <button class="record-btn btn-primary" onclick="location.href='record_detail.html'">查看详情</button>
                </div>
            </div>

            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">李四（代办） - 糖尿病</div>
                    <div class="record-status status-approved">已通过</div>
                </div>
                <div class="record-body">
                    <div class="record-info">
                        <div class="record-label">申报时间</div>
                        <div class="record-value">2023-05-20 14:30:12</div>
                    </div>
                    <div class="record-info">
                        <div class="record-label">审核时间</div>
                        <div class="record-value">2023-05-23 09:15:30</div>
                    </div>
                    <div class="record-info">
                        <div class="record-label">医保类型</div>
                        <div class="record-value">城镇居民医保</div>
                    </div>
                </div>
                <div class="record-footer">
                    <button class="record-btn btn-primary" onclick="location.href='record_detail.html'">查看详情</button>
                </div>
            </div>

            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">王五 - 冠心病</div>
                    <div class="record-status status-rejected">未通过</div>
                </div>
                <div class="record-body">
                    <div class="record-info">
                        <div class="record-label">申报时间</div>
                        <div class="record-value">2023-04-10 16:45:22</div>
                    </div>
                    <div class="record-info">
                        <div class="record-label">驳回时间</div>
                        <div class="record-value">2023-04-13 11:20:15</div>
                    </div>
                    <div class="record-info">
                        <div class="record-label">驳回原因</div>
                        <div class="record-value text-red-500">诊断证明不清晰，请重新上传</div>
                    </div>
                </div>
                <div class="record-footer">
                    <button class="record-btn btn-text">删除记录</button>
                    <button class="record-btn btn-primary">修改重新提交</button>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="loading-state">
                <span class="loading-icon"></span>
                <span>加载更多...</span>
            </div>

            <!-- 空状态（当没有记录时显示） -->
            <div class="empty-state hidden">
                <div class="empty-icon">
                    <i class="fas fa-file-medical"></i>
                </div>
                <div class="empty-text">暂无申报记录</div>
                <a href="#" class="empty-btn">立即申报</a>
            </div>
        </div>
    </div>
</body>
</html>