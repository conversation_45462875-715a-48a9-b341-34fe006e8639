<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择慢病病种</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            line-height: 1.5;
        }
        .safe-area {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
        }
        .header {
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .back-button {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        .step-indicator:before {
            content: '';
            position: absolute;
            top: 15px;
            left: 30px;
            right: 30px;
            height: 1px;
            background-color: #e0e0e0;
            z-index: 1;
        }
        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        .step.active {
            background-color: #07c160;
            border-color: #07c160;
            color: #fff;
        }
        .step.completed {
            background-color: #07c160;
            border-color: #07c160;
            color: #fff;
        }
        .step-label {
            font-size: 12px;
            color: #999;
            text-align: center;
            margin-top: 8px;
        }
        .step.active + .step-label {
            color: #07c160;
            font-weight: 500;
        }
        .step.completed + .step-label {
            color: #07c160;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            overflow: hidden;
        }
        .card-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
        }
        .card-body {
            padding: 16px;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
        }
        .btn-primary {
            background-color: #07c160;
            color: #fff;
        }
        .btn-primary:disabled {
            background-color: #9be7b4;
            color: #fff;
        }
        .btn-outline {
            background-color: #fff;
            color: #07c160;
            border: 1px solid #07c160;
        }
        .disease-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }
        .disease-item:last-child {
            border-bottom: none;
        }
        .disease-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
        }
        .disease-info {
            flex: 1;
        }
        .disease-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        .disease-meta {
            font-size: 13px;
            color: #999;
        }
        .disease-select {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .disease-select.selected {
            background-color: #07c160;
            border-color: #07c160;
        }
        .person-summary {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        .person-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: #666;
        }
        .person-info {
            flex: 1;
        }
        .person-name {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        .person-id {
            font-size: 12px;
            color: #999;
        }
        .person-change {
            color: #07c160;
            font-size: 13px;
        }
        .search-bar {
            display: flex;
            align-items: center;
            background-color: #f0f0f0;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }
        .search-icon {
            color: #999;
            margin-right: 8px;
        }
        .search-input {
            flex: 1;
            border: none;
            background-color: transparent;
            font-size: 14px;
            color: #333;
        }
        .search-input:focus {
            outline: none;
        }
        .category-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 16px;
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
        }
        .category-tab {
            padding: 12px 16px;
            font-size: 14px;
            color: #666;
            position: relative;
        }
        .category-tab.active {
            color: #07c160;
            font-weight: 500;
        }
        .category-tab.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 16px;
            right: 16px;
            height: 2px;
            background-color: #07c160;
        }
    </style>
</head>
<body>
    <div class="safe-area">
        <!-- 顶部导航 -->
        <div class="header px-4 py-3 flex items-center">
            <div class="back-button mr-4">
                <i class="fas fa-chevron-left"></i>
            </div>
            <h1 class="text-lg font-medium flex-1 text-center">选择慢病病种</h1>
            <div class="w-6"></div>
        </div>

        <!-- 步骤指示器 -->
        <div class="px-8 py-4">
            <div class="step-indicator">
                <div class="flex flex-col items-center">
                    <div class="step completed">1</div>
                    <div class="step-label">身份认证</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step active">2</div>
                    <div class="step-label">填写信息</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step">3</div>
                    <div class="step-label">上传材料</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step">4</div>
                    <div class="step-label">提交申报</div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="p-4 pb-24">
            <!-- 申报人信息摘要 -->
            <div class="person-summary">
                <div class="person-avatar bg-blue-100 text-blue-500">
                    <i class="fas fa-user"></i>
                </div>
                <div class="person-info">
                    <div class="person-name">张三</div>
                    <div class="person-id">医保类型：城镇职工医保</div>
                </div>
                <div class="person-change">
                    更换
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="bg-blue-50 text-blue-700 p-3 rounded-md text-sm mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                <span>请选择需要申报的慢病病种，每次只能申报一种慢病</span>
            </div>

            <!-- 搜索栏 -->
            <div class="search-bar">
                <div class="search-icon">
                    <i class="fas fa-search"></i>
                </div>
                <input type="text" class="search-input" placeholder="搜索病种名称">
            </div>

            <!-- 分类标签 -->
            <div class="category-tabs">
                <div class="category-tab active">全部</div>
                <div class="category-tab">心脑血管</div>
                <div class="category-tab">内分泌系统</div>
                <div class="category-tab">呼吸系统</div>
                <div class="category-tab">消化系统</div>
                <div class="category-tab">其他</div>
            </div>

            <!-- 病种列表 -->
            <div class="card">
                <div class="card-body p-0">
                    <!-- 高血压 -->
                    <div class="disease-item">
                        <div class="disease-icon bg-red-100 text-red-500">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <div class="disease-info">
                            <div class="disease-name">高血压</div>
                            <div class="disease-meta">需要提供近3个月内的血压记录和诊断证明</div>
                        </div>
                        <div class="disease-select selected">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <!-- 糖尿病 -->
                    <div class="disease-item">
                        <div class="disease-icon bg-blue-100 text-blue-500">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="disease-info">
                            <div class="disease-name">糖尿病</div>
                            <div class="disease-meta">需要提供近3个月内的血糖记录和诊断证明</div>
                        </div>
                        <div class="disease-select">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <!-- 冠心病 -->
                    <div class="disease-item">
                        <div class="disease-icon bg-purple-100 text-purple-500">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="disease-info">
                            <div class="disease-name">冠心病</div>
                            <div class="disease-meta">需要提供心电图、冠脉造影等检查报告</div>
                        </div>
                        <div class="disease-select">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <!-- 慢性肾病 -->
                    <div class="disease-item">
                        <div class="disease-icon bg-yellow-100 text-yellow-500">
                            <i class="fas fa-kidneys"></i>
                        </div>
                        <div class="disease-info">
                            <div class="disease-name">慢性肾病</div>
                            <div class="disease-meta">需要提供肾功能检查报告和诊断证明</div>
                        </div>
                        <div class="disease-select">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <!-- 慢性肝病 -->
                    <div class="disease-item">
                        <div class="disease-icon bg-green-100 text-green-500">
                            <i class="fas fa-lungs"></i>
                        </div>
                        <div class="disease-info">
                            <div class="disease-name">慢性肝病</div>
                            <div class="disease-meta">需要提供肝功能检查报告和诊断证明</div>
                        </div>
                        <div class="disease-select">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <!-- 慢性阻塞性肺疾病 -->
                    <div class="disease-item">
                        <div class="disease-icon bg-indigo-100 text-indigo-500">
                            <i class="fas fa-lungs-virus"></i>
                        </div>
                        <div class="disease-info">
                            <div class="disease-name">慢性阻塞性肺疾病</div>
                            <div class="disease-meta">需要提供肺功能检查报告和诊断证明</div>
                        </div>
                        <div class="disease-select">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="text-gray-500 text-sm mt-4 mb-6">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span>如需申报多种慢病，请分别提交申报</span>
            </div>

            <!-- 按钮区域 -->
            <div class="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
                <button class="btn btn-primary" onclick="location.href='upload_attachments.html'">下一步</button>
            </div>
        </div>
    </div>
</body>
</html>