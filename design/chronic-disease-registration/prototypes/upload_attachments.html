<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传附件材料</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            line-height: 1.5;
        }
        .safe-area {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
        }
        .header {
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .back-button {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        .step-indicator:before {
            content: '';
            position: absolute;
            top: 15px;
            left: 30px;
            right: 30px;
            height: 1px;
            background-color: #e0e0e0;
            z-index: 1;
        }
        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        .step.active {
            background-color: #07c160;
            border-color: #07c160;
            color: #fff;
        }
        .step.completed {
            background-color: #07c160;
            border-color: #07c160;
            color: #fff;
        }
        .step-label {
            font-size: 12px;
            color: #999;
            text-align: center;
            margin-top: 8px;
        }
        .step.active + .step-label {
            color: #07c160;
            font-weight: 500;
        }
        .step.completed + .step-label {
            color: #07c160;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            overflow: hidden;
        }
        .card-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-body {
            padding: 16px;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
        }
        .btn-primary {
            background-color: #07c160;
            color: #fff;
        }
        .btn-primary:disabled {
            background-color: #9be7b4;
            color: #fff;
        }
        .btn-outline {
            background-color: #fff;
            color: #07c160;
            border: 1px solid #07c160;
        }
        .person-summary {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        .person-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: #666;
        }
        .person-info {
            flex: 1;
        }
        .person-name {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        .person-id {
            font-size: 12px;
            color: #999;
        }
        .person-change {
            color: #07c160;
            font-size: 13px;
        }
        .attachment-item {
            margin-bottom: 16px;
        }
        .attachment-label {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .attachment-required {
            color: #ff4d4f;
            margin-left: 4px;
        }
        .attachment-desc {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        .attachment-upload {
            border: 1px dashed #e0e0e0;
            border-radius: 4px;
            padding: 16px;
            text-align: center;
            color: #999;
            font-size: 14px;
            cursor: pointer;
        }
        .attachment-upload-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .attachment-preview {
            display: flex;
            flex-wrap: wrap;
            margin-top: 8px;
            gap: 8px;
        }
        .attachment-preview-item {
            width: calc(33.333% - 6px);
            position: relative;
            border-radius: 4px;
            overflow: hidden;
            aspect-ratio: 3/4;
        }
        .attachment-preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .attachment-preview-remove {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
        }
        .attachment-preview-add {
            width: calc(33.333% - 6px);
            aspect-ratio: 3/4;
            border: 1px dashed #e0e0e0;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }
        .attachment-preview-add-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="safe-area">
        <!-- 顶部导航 -->
        <div class="header px-4 py-3 flex items-center">
            <div class="back-button mr-4">
                <i class="fas fa-chevron-left"></i>
            </div>
            <h1 class="text-lg font-medium flex-1 text-center">上传附件材料</h1>
            <div class="w-6"></div>
        </div>

        <!-- 步骤指示器 -->
        <div class="px-8 py-4">
            <div class="step-indicator">
                <div class="flex flex-col items-center">
                    <div class="step completed">1</div>
                    <div class="step-label">身份认证</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step completed">2</div>
                    <div class="step-label">填写信息</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step active">3</div>
                    <div class="step-label">上传材料</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step">4</div>
                    <div class="step-label">提交申报</div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="p-4 pb-24">
            <!-- 申报信息摘要 -->
            <div class="person-summary">
                <div class="person-avatar bg-blue-100 text-blue-500">
                    <i class="fas fa-user"></i>
                </div>
                <div class="person-info">
                    <div class="person-name">张三</div>
                    <div class="person-id">申报病种：高血压</div>
                </div>
                <div class="person-change">
                    更换
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="bg-blue-50 text-blue-700 p-3 rounded-md text-sm mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                <span>请根据要求上传相关附件材料，带<span class="text-red-500">*</span>的为必填项</span>
            </div>

            <!-- 附件列表 -->
            <div class="card mb-4">
                <div class="card-header">
                    <span>必要附件</span>
                    <span class="text-sm text-gray-500">3/4</span>
                </div>
                <div class="card-body">
                    <!-- 诊断证明 -->
                    <div class="attachment-item">
                        <div class="attachment-label">
                            <span>诊断证明</span>
                            <span class="attachment-required">*</span>
                        </div>
                        <div class="attachment-desc">请上传医院出具的高血压诊断证明，需盖有医院公章</div>
                        <div class="attachment-preview">
                            <div class="attachment-preview-item">
                                <img src="https://images.unsplash.com/photo-1583911860205-72f8ac8ddcbe?q=80&w=2576&auto=format&fit=crop" alt="诊断证明" class="attachment-preview-image">
                                <!-- 图片来源: Unsplash - 医疗文件 -->
                                <div class="attachment-preview-remove">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                            <div class="attachment-preview-add">
                                <div class="attachment-preview-add-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>添加图片</div>
                            </div>
                        </div>
                    </div>

                    <!-- 血压记录 -->
                    <div class="attachment-item">
                        <div class="attachment-label">
                            <span>血压记录</span>
                            <span class="attachment-required">*</span>
                        </div>
                        <div class="attachment-desc">请上传近3个月内的血压记录，至少包含10次测量数据</div>
                        <div class="attachment-preview">
                            <div class="attachment-preview-item">
                                <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?q=80&w=2940&auto=format&fit=crop" alt="血压记录1" class="attachment-preview-image">
                                <!-- 图片来源: Unsplash - 医疗记录 -->
                                <div class="attachment-preview-remove">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                            <div class="attachment-preview-item">
                                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2940&auto=format&fit=crop" alt="血压记录2" class="attachment-preview-image">
                                <!-- 图片来源: Unsplash - 医疗记录 -->
                                <div class="attachment-preview-remove">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                            <div class="attachment-preview-add">
                                <div class="attachment-preview-add-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>添加图片</div>
                            </div>
                        </div>
                    </div>

                    <!-- 门诊病历 -->
                    <div class="attachment-item">
                        <div class="attachment-label">
                            <span>门诊病历</span>
                            <span class="attachment-required">*</span>
                        </div>
                        <div class="attachment-desc">请上传近6个月内的门诊病历，需包含医生诊断和处方信息</div>
                        <div class="attachment-preview">
                            <div class="attachment-preview-item">
                                <img src="https://images.unsplash.com/photo-1587854692152-cbe660dbde88?q=80&w=2969&auto=format&fit=crop" alt="门诊病历" class="attachment-preview-image">
                                <!-- 图片来源: Unsplash - 医疗文件 -->
                                <div class="attachment-preview-remove">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                            <div class="attachment-preview-add">
                                <div class="attachment-preview-add-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>添加图片</div>
                            </div>
                        </div>
                    </div>

                    <!-- 检查报告 -->
                    <div class="attachment-item">
                        <div class="attachment-label">
                            <span>检查报告</span>
                            <span class="attachment-required">*</span>
                        </div>
                        <div class="attachment-desc">请上传心电图、血脂等相关检查报告</div>
                        <div class="attachment-upload">
                            <div class="attachment-upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div>点击上传图片</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 附加材料 -->
            <div class="card mb-4">
                <div class="card-header">
                    <span>附加材料（选填）</span>
                    <span class="text-sm text-gray-500">0/2</span>
                </div>
                <div class="card-body">
                    <!-- 用药记录 -->
                    <div class="attachment-item">
                        <div class="attachment-label">
                            <span>用药记录</span>
                        </div>
                        <div class="attachment-desc">如有长期服药记录，可上传药品清单或购药发票</div>
                        <div class="attachment-upload">
                            <div class="attachment-upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div>点击上传图片</div>
                        </div>
                    </div>

                    <!-- 其他材料 -->
                    <div class="attachment-item">
                        <div class="attachment-label">
                            <span>其他材料</span>
                        </div>
                        <div class="attachment-desc">其他可能有助于审核的材料</div>
                        <div class="attachment-upload">
                            <div class="attachment-upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div>点击上传图片</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="text-gray-500 text-sm mt-4 mb-6">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span>请确保上传的材料清晰可见，信息准确无误，否则可能导致审核不通过</span>
            </div>

            <!-- 按钮区域 -->
            <div class="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
                <button class="btn btn-primary" onclick="location.href='confirm_submission.html'">下一步</button>
            </div>
        </div>
    </div>
</body>
</html>