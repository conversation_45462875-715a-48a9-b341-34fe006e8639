<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申报信息确认</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            line-height: 1.5;
        }
        .safe-area {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
        }
        .header {
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .back-button {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        .step-indicator:before {
            content: '';
            position: absolute;
            top: 15px;
            left: 30px;
            right: 30px;
            height: 1px;
            background-color: #e0e0e0;
            z-index: 1;
        }
        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        .step.active {
            background-color: #07c160;
            border-color: #07c160;
            color: #fff;
        }
        .step.completed {
            background-color: #07c160;
            border-color: #07c160;
            color: #fff;
        }
        .step-label {
            font-size: 12px;
            color: #999;
            text-align: center;
            margin-top: 8px;
        }
        .step.active + .step-label {
            color: #07c160;
            font-weight: 500;
        }
        .step.completed + .step-label {
            color: #07c160;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            overflow: hidden;
        }
        .card-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-body {
            padding: 16px;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
        }
        .btn-primary {
            background-color: #07c160;
            color: #fff;
        }
        .btn-primary:disabled {
            background-color: #9be7b4;
            color: #fff;
        }
        .btn-outline {
            background-color: #fff;
            color: #07c160;
            border: 1px solid #07c160;
        }
        .info-item {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
        }
        .info-item:last-child {
            margin-bottom: 0;
        }
        .info-label {
            width: 80px;
            font-size: 14px;
            color: #999;
            flex-shrink: 0;
        }
        .info-value {
            flex: 1;
            font-size: 14px;
            color: #333;
        }
        .attachment-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }
        .attachment-preview-item {
            width: 60px;
            height: 80px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }
        .attachment-preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .attachment-count {
            position: absolute;
            top: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            font-size: 12px;
            padding: 2px 6px;
            border-bottom-left-radius: 4px;
        }
        .section-title {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        .success-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background-color: #07c160;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 16px;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none; /* 默认隐藏 */
            align-items: center;
            justify-content: center;
            z-index: 100;
        }
        .modal-content {
            width: 80%;
            max-width: 320px;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
        }
        .modal-body {
            padding: 24px;
            text-align: center;
        }
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        .modal-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
        }
        .modal-actions {
            display: flex;
            border-top: 1px solid #f0f0f0;
        }
        .modal-btn {
            flex: 1;
            padding: 12px;
            text-align: center;
            font-size: 16px;
            border: none;
            background-color: #fff;
        }
        .modal-btn-primary {
            color: #07c160;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="safe-area">
        <!-- 顶部导航 -->
        <div class="header px-4 py-3 flex items-center">
            <div class="back-button mr-4">
                <i class="fas fa-chevron-left"></i>
            </div>
            <h1 class="text-lg font-medium flex-1 text-center">申报信息确认</h1>
            <div class="w-6"></div>
        </div>

        <!-- 步骤指示器 -->
        <div class="px-8 py-4">
            <div class="step-indicator">
                <div class="flex flex-col items-center">
                    <div class="step completed">1</div>
                    <div class="step-label">身份认证</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step completed">2</div>
                    <div class="step-label">填写信息</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step completed">3</div>
                    <div class="step-label">上传材料</div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="step active">4</div>
                    <div class="step-label">提交申报</div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="p-4 pb-24">
            <!-- 提示信息 -->
            <div class="bg-blue-50 text-blue-700 p-3 rounded-md text-sm mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                <span>请仔细核对以下申报信息，确认无误后提交</span>
            </div>

            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <span>基本信息</span>
                    <span class="text-sm text-blue-500">修改</span>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <div class="info-label">申报人</div>
                        <div class="info-value">张三</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">身份证号</div>
                        <div class="info-value">110101198010100123</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">医保类型</div>
                        <div class="info-value">城镇职工医保</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">医保卡号</div>
                        <div class="info-value">1234 5678 9012 3456</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">申报病种</div>
                        <div class="info-value">高血压</div>
                    </div>
                </div>
            </div>

            <!-- 附件材料 -->
            <div class="card mb-4">
                <div class="card-header">
                    <span>附件材料</span>
                    <span class="text-sm text-blue-500">修改</span>
                </div>
                <div class="card-body">
                    <div class="section-title">必要附件</div>
                    <div class="info-item">
                        <div class="info-label">诊断证明</div>
                        <div class="info-value">
                            <div class="attachment-preview">
                                <div class="attachment-preview-item">
                                    <img src="https://images.unsplash.com/photo-1583911860205-72f8ac8ddcbe?q=80&w=2576&auto=format&fit=crop" alt="诊断证明" class="attachment-preview-image">
                                    <!-- 图片来源: Unsplash - 医疗文件 -->
                                    <div class="attachment-count">1</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">血压记录</div>
                        <div class="info-value">
                            <div class="attachment-preview">
                                <div class="attachment-preview-item">
                                    <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?q=80&w=2940&auto=format&fit=crop" alt="血压记录" class="attachment-preview-image">
                                    <!-- 图片来源: Unsplash - 医疗记录 -->
                                    <div class="attachment-count">2</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">门诊病历</div>
                        <div class="info-value">
                            <div class="attachment-preview">
                                <div class="attachment-preview-item">
                                    <img src="https://images.unsplash.com/photo-1587854692152-cbe660dbde88?q=80&w=2969&auto=format&fit=crop" alt="门诊病历" class="attachment-preview-image">
                                    <!-- 图片来源: Unsplash - 医疗文件 -->
                                    <div class="attachment-count">1</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检查报告</div>
                        <div class="info-value text-red-500">未上传</div>
                    </div>

                    <div class="section-title mt-4">附加材料</div>
                    <div class="info-item">
                        <div class="info-label">用药记录</div>
                        <div class="info-value text-gray-500">未上传（选填）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">其他材料</div>
                        <div class="info-value text-gray-500">未上传（选填）</div>
                    </div>
                </div>
            </div>

            <!-- 代办人信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <span>代办人信息</span>
                    <span class="text-sm text-blue-500">修改</span>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <div class="info-label">代办人</div>
                        <div class="info-value">李四</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">身份证号</div>
                        <div class="info-value">110101198505050123</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">手机号</div>
                        <div class="info-value">138****8000</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">与患者关系</div>
                        <div class="info-value">配偶</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">代办原因</div>
                        <div class="info-value">患者行动不便</div>
                    </div>
                </div>
            </div>

            <!-- 申报须知 -->
            <div class="card mb-4">
                <div class="card-header">申报须知</div>
                <div class="card-body">
                    <div class="text-sm text-gray-700">
                        <p class="mb-2">1. 提交申报后，工作人员将在5个工作日内完成审核；</p>
                        <p class="mb-2">2. 审核结果将通过短信通知，您也可以在小程序中查询；</p>
                        <p class="mb-2">3. 如审核不通过，您可根据驳回原因修改后重新提交；</p>
                        <p class="mb-2">4. 如有疑问，请联系医保科室：0123-4567890。</p>
                    </div>
                </div>
            </div>

            <!-- 声明与授权 -->
            <div class="mb-6">
                <label class="flex items-start">
                    <input type="checkbox" class="mt-1 mr-2" checked>
                    <span class="text-sm text-gray-700">
                        本人承诺所提供的所有信息和材料真实、准确、完整，如有虚假，愿承担相应的法律责任。同意授权医院获取本人相关医疗信息用于慢病申报审核。
                    </span>
                </label>
            </div>

            <!-- 按钮区域 -->
            <div class="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
                <button class="btn btn-primary" id="submitBtn">确认提交</button>
            </div>
        </div>
    </div>

    <!-- 提交成功弹窗 -->
    <div class="modal" id="successModal">
        <div class="modal-content">
            <div class="modal-body">
                <div class="success-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="modal-title">提交成功</div>
                <div class="modal-message">您的慢病申报已提交成功，工作人员将在5个工作日内完成审核，请耐心等待</div>
            </div>
            <div class="modal-actions">
                <button class="modal-btn" onclick="location.href='record_list.html'">查看申报记录</button>
                <button class="modal-btn modal-btn-primary" onclick="location.href='home.html'">返回首页</button>
            </div>
        </div>
    </div>

    <script>
        // 提交按钮点击事件
        document.getElementById('submitBtn').addEventListener('click', function() {
            // 显示成功弹窗
            document.getElementById('successModal').style.display = 'flex';
        });
    </script>
</body>
</html>