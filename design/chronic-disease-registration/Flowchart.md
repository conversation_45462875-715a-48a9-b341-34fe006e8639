# 慢病申报功能 - 用户操作流程图

## 1. 总体流程概览

```mermaid
graph TD
    A[首页] --> B[申报人身份认证]
    B --> C[代办人认证]
    C --> D[填写申报信息]
    D --> E[上传附件材料]
    E --> F[提交申报]
    F --> G[查看申报记录]
    G --> H[查看申报详情]
```

## 2. 身份认证流程

```mermaid
graph TD
    A[首页-慢病申报入口] --> B[申报人认证]
    B --> C[上传申报人身份证]
    C --> D[上传申报人医保卡]
    D --> E[填写申报人手机号]
    E --> F[OCR识别申报人信息]
    F --> G[确认申报人身份信息]
    G --> H[代办人认证]
    
    H --> I[选择与申报人关系]
    I -->|本人| L[认证完成]
    I -->|非本人关系| J[上传代办人身份证]
    J --> K[填写代办人手机号]
    K --> M[OCR识别代办人信息]
    M --> N[确认代办人身份信息]
    N --> L
```

## 3. 申报信息填写流程

```mermaid
graph TD
    A[身份认证完成] --> B[选择慢病病种]
    B --> C[上传附件材料]
    C --> D[申报信息确认]
    D --> E[提交申报]
```

## 4. 申报记录查看流程

```mermaid
graph TD
    A[首页-申报记录入口] --> B[申报记录列表]
    B --> C{筛选记录}
    C -->|全部| B
    C -->|审核中| D[审核中记录]
    C -->|已通过| E[已通过记录]
    C -->|未通过| F[未通过记录]
    D --> G[查看详情]
    E --> G
    F --> G
    F --> H[修改重新提交]
    H --> I[编辑申报信息]
    I --> J[重新提交]
```

## 5. 状态转换图

```mermaid
stateDiagram-v2
    [*] --> 未提交
    未提交 --> 审核中: 提交申报
    审核中 --> 已通过: 审核通过
    审核中 --> 未通过: 审核不通过
    未通过 --> 审核中: 修改重新提交
    已通过 --> [*]
```

## 6. 关键交互点说明

### 6.1 OCR识别交互

- **触发点**：用户上传身份证或医保卡图片
- **交互流程**：
  1. 用户点击上传按钮
  2. 调用手机相机或相册
  3. 用户选择/拍摄证件图片
  4. 显示上传中状态
  5. 调用OCR服务识别
  6. 显示识别结果并自动填充表单
  7. 允许用户手动修正识别错误

### 6.2 病种与附件关联交互

- **触发点**：用户选择慢病病种
- **交互流程**：
  1. 用户选择病种
  2. 系统自动关联该病种所需的必要附件列表
  3. 在上传附件页面显示必要附件和选填附件
  4. 用户根据提示上传对应附件

### 6.3 申报状态变更通知

- **触发点**：申报状态发生变化（提交成功、审核通过、审核不通过）
- **交互流程**：
  1. 状态变更后系统发送微信订阅消息
  2. 用户点击消息进入小程序
  3. 跳转至对应的申报详情页面
  4. 显示最新状态和处理建议

## 7. 异常流程处理

### 7.1 OCR识别失败

```mermaid
sequenceDiagram
    参与者 用户
    参与者 小程序
    参与者 OCR服务
    用户->>小程序: 上传证件图片
    小程序->>OCR服务: 发送图片
    OCR服务-->>小程序: 返回识别失败
    小程序-->>用户: 提示图片不清晰
    小程序-->>用户: 建议重新拍摄或手动输入
    用户->>小程序: 重新上传或手动输入
```

### 7.2 申报信息不完整

```mermaid
sequenceDiagram
    参与者 用户
    参与者 小程序
    参与者 服务端
    用户->>小程序: 点击提交申报
    小程序->>小程序: 表单验证
    小程序-->>用户: 提示信息不完整
    小程序-->>用户: 标记缺失字段
    用户->>小程序: 补充信息
    小程序->>服务端: 提交完整信息
    服务端-->>小程序: 确认接收
    小程序-->>用户: 显示提交成功
```

## 8. 多端协同流程

```mermaid
sequenceDiagram
    参与者 患者
    参与者 微信小程序
    参与者 医院窗口
    参与者 医保科室
    患者->>微信小程序: 提交慢病申报
    微信小程序->>医保科室: 传输申报数据
    医保科室->>医保科室: 审核申报材料
    医保科室-->>微信小程序: 更新审核状态
    微信小程序-->>患者: 推送审核结果通知
    患者->>微信小程序: 查看审核结果
    alt 审核通过
        患者->>医院窗口: 前往领取证明
        医院窗口-->>患者: 发放慢病证明
    else 审核不通过
        患者->>微信小程序: 修改申报信息
        微信小程序->>医保科室: 重新提交
    end
```