<!-- Generated by SpecStory -->

# Distinct Error Messages for Unlock Order Bug (2025-02-25 11:30:29)

_**User**_

Fix this bug in `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt`:
```
The `unlockOrder` method uses an identical error message "当前订单不支持解锁处方" for two distinct validation scenarios: when an order does not belong to the current patient, and when an order cannot be technically unlocked. This creates user-facing ambiguity, as different underlying issues are masked behind the same generic error message.
```

---

_**Assistant**_

I'll help fix this issue by providing distinct error messages for the different validation scenarios in the `unlockOrder` method.

Here's the suggested edit:

```kotlin:xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt
// ... existing code ...

    @PostMapping("unlockOrder") 
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun unlockOrder(@RequestParam orderId: Long): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val mipWxOrder = mipWxOrderService.getById(orderId)
            ?: return AjaxResult.error("订单不存在")
        
        // 验证订单是否属于当前患者
        if (mipWxOrder.patientIdCardNo != activePatient.idCardNo) {
            return AjaxResult.error("无权操作此订单，订单与当前就诊人信息不符")
        }

        // 验证订单是否可以解锁
        if (!mipWxOrderService.canUnlock(mipWxOrder)) {
            return AjaxResult.error("当前订单不支持解锁处方")
        }

        val unlockOrderResult = runBlocking {
            BSoftMipService.WEIXIN.unlockOrder(
                UnlockOrderForm(cfyjhm = mipWxOrder.settlementIds)
            )
        }
        // ... rest of the existing code ...
    }

// ... existing code ...
```

Changes made:
1. Changed the error message for the patient ID card validation to be more specific: "无权操作此订单，订单与当前就诊人信息不符"
2. Kept the technical validation message as is: "当前订单不支持解锁处方"

This change provides clearer feedback to users about why their unlock request failed:
- If they're trying to unlock someone else's order, they'll see a permission/ownership error
- If the order itself cannot be unlocked due to technical reasons, they'll see the technical limitation message

The error messages are now distinct and more informative, helping users better understand and potentially resolve the issue they're encountering.

中文说明：
1. 修改了患者身份验证的错误消息，使其更具体和清晰
2. 保持了技术验证消息不变
3. 这样修改后，用户可以更清楚地知道是因为没有权限还是因为订单本身不支持解锁

---

