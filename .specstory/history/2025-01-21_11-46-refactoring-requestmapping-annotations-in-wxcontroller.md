<!-- Generated by SpecStory -->

# Refactoring RequestMapping Annotations in WxController (2025-01-21 20:46:40)

_**User**_

移除类级别的`@RequestMapping`注解，在方法级别的映射注解中使用完整路径

---

_**Assistant**_

我来帮你修改 WxController 的路由映射方式。这样的修改可以让每个接口的路径更加清晰和明确。

```kotlin:xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt
// 移除类级别的 @RequestMapping
@RestController
class WxController : BaseController() {
    // ... existing code ...

    // 修改方法级别的映射注解，使用完整路径
    @PostMapping("/open/wx/login")
    fun login(@RequestParam code: String): AjaxResult {
        // ... existing code ...
    }

    @PostMapping("/open/wx/idCardOcr")
    @RequireSession(ClientType.WEIXIN)
    fun idCardOcr(file: MultipartFile): AjaxResult {
        // ... existing code ...
    }

    @GetMapping("/open/wx/phoneNumber")
    @RequireSession(ClientType.WEIXIN)
    fun phoneNumber(code: String): AjaxResult {
        // ... existing code ...
    }

    @GetMapping("/open/wx/oauthConfirmParams")
    @RequireSession(ClientType.WEIXIN)
    fun oauthConfirmParams(): AjaxResult {
        // ... existing code ...
    }

    @PostMapping("/open/wx/identifyFace")
    @RequireSession(ClientType.WEIXIN)
    fun identifyFace(verifyResult: String): AjaxResult {
        // ... existing code ...
    }

    @PostMapping("/open/wx/identifyFaceAndQueryPatient")
    @RequireSession(ClientType.WEIXIN)
    fun identifyFaceAndQueryPatient(
        // ... existing code ...
    }

    @PostMapping("/open/wx/onCCBPay")
    fun onCCBPay(): String {
        // ... existing code ...
    }

    @PostMapping("/open/wx/onWXPay")
    fun onWXPay(@RequestBody xmlData: String): String {
        // ... existing code ...
    }

    @PostMapping("/open/wx/onWXRefund")
    fun onWXRefund(@RequestBody xmlData: String): String {
        // ... existing code ...
    }

    @PostMapping("/open/wx/onWxInsurancePay")
    fun onWxInsurancePay(@RequestBody xmlData: String): String {
        // ... existing code ...
    }

    @GetMapping("/open/wx/insuranceOrder/{payOrderId}")
    fun insuranceOrder(@PathVariable payOrderId: String): String {
        // ... existing code ...
    }
}
```

主要变更说明：

1. 移除了类级别的 `@RequestMapping("/open/wx")` 注解
2. 将原来的路径前缀 "/open/wx" 添加到每个方法的映射注解中
3. 其他代码逻辑保持不变

这样修改的好处是：
- 每个接口的完整路径一目了然
- 更容易理解和维护
- 避免类级别路径修改影响所有方法
- 方便接口文档生成和查看

English version:

The main changes are:

1. Removed the class-level `@RequestMapping("/open/wx")` annotation
2. Added the original path prefix "/open/wx" to each method's mapping annotation
3. Other code logic remains unchanged

Benefits of this change:
- Complete path for each endpoint is immediately visible
- Easier to understand and maintain
- Prevents class-level path changes from affecting all methods
- Better for API documentation generation and review

---

