<!-- Generated by SpecStory -->

# Fixing Dropdown Direction Bug in JavaScript (2025-01-01 16:56:36)

_**User**_

Fix this bug in `ruoyi-admin/src/main/resources/static/ruoyi/js/common.js`:
```
The `handleDropdownDirection` function has multiple critical issues:

1. It attaches multiple event handlers to the `show.bs.dropdown` event when called repeatedly, causing redundant and incorrect dropdown direction calculations.

2. The function incorrectly handles the minimum space parameter, defaulting to `200` when `0` is passed due to JavaScript's falsy value evaluation.

3. It assumes the `.fixed-table-container` element always exists, which may not be true in all contexts.

4. The function does not prevent the dropdown from showing when it should be a `dropup`, potentially causing the dropdown to appear outside the viewport.

These issues lead to unreliable dropdown positioning and multiple event handlers being attached unnecessarily, degrading the user interface's responsiveness and visual consistency.
```

---

_**Assistant**_

我来帮你修复这些问题。以下是对 `handleDropdownDirection` 函数的改进：

```javascript:ruoyi-admin/src/main/resources/static/ruoyi/js/common.js
/** 
 * 处理下拉菜单显示方向
 * @param {string} btnGroupSelector - 按钮组选择器
 * @param {number} minSpace - 最小所需空间(px)
 */
function handleDropdownDirection(btnGroupSelector, minSpace) {
    // 移除已存在的事件处理程序，避免重复绑定
    $(document).off('show.bs.dropdown', btnGroupSelector);
    
    $(document).on('show.bs.dropdown', btnGroupSelector, function(e) {
        const $btnGroup = $(this);
        const $button = $btnGroup.find('.dropdown-toggle');
        const $dropdown = $btnGroup.find('.dropdown-menu');
        const $tableContainer = $('.fixed-table-container');
        
        // 确保最小空间参数有效
        const requiredSpace = typeof minSpace === 'number' ? minSpace : 200;
        
        // 获取按钮相对于视窗的位置
        const buttonRect = $button[0].getBoundingClientRect();
        const windowHeight = window.innerHeight;
        
        // 计算按钮底部到视窗底部的距离
        const distanceToBottom = windowHeight - buttonRect.bottom;
        
        // 如果存在表格容器，则考虑表格容器的边界
        if ($tableContainer.length) {
            const containerRect = $tableContainer[0].getBoundingClientRect();
            const distanceToTableBottom = containerRect.bottom - buttonRect.bottom;
            
            if (distanceToTableBottom < requiredSpace) {
                $btnGroup.addClass('dropup');
                return;
            }
        }
        
        // 如果距离视窗底部空间不足，切换为向上显示
        if (distanceToBottom < requiredSpace) {
            $btnGroup.addClass('dropup');
        } else {
            $btnGroup.removeClass('dropup');
        }
    });
    
    // 在下拉菜单隐藏时移除dropup类
    $(document).on('hidden.bs.dropdown', btnGroupSelector, function() {
        $(this).removeClass('dropup');
    });
}
```

主要改进包括：

1. 使用 `off()` 方法移除已存在的事件处理程序，避免重复绑定。

2. 改进了最小空间参数的处理，使用 `typeof` 检查确保参数是数字类型。

3. 使用 `getBoundingClientRect()` 获取更准确的位置信息，并考虑视窗边界。

4. 增加了对表格容器存在性的检查，避免在没有表格容器时出错。

5. 添加了 `hidden.bs.dropdown` 事件处理，在下拉菜单隐藏时清理样式类。

这些改进可以：

- 避免事件处理程序重复绑定
- 更准确地处理最小空间参数
- 提供更可靠的位置计算
- 增强代码的健壮性
- 确保下拉菜单的正确显示方向

使用方法保持不变：

```javascript
// 示例调用
handleDropdownDirection('.btn-group', 200);
```

---

