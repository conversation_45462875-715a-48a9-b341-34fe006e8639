<!-- Generated by SpecStory -->

# 新增接口获取慢病申报详情 (2025-06-07 15:35:34)

_**User**_

新增接口，获取慢病申报详情信息

---

_**Assistant**_



---

根据代码分析，我需要在`ChronicDiseaseRegistrationApiController`中新增一个获取慢病申报详情信息的接口。我会参考现有接口的模式来实现这个功能。

---



---

已成功为慢病申报API控制器新增了获取详情信息的接口。新增接口的特点如下：

## 新增接口详情

**接口路径**：`GET /api/chronic-disease-registration/{id}`

**功能说明**：
- 根据申报记录ID获取慢病申报的完整详细信息
- 包含患者信息、代办人信息、病种信息、附件信息等所有相关数据

**主要特性**：
1. **会话验证**：使用`@RequireSession`注解确保用户已登录
2. **权限控制**：只能获取属于当前用户的申报记录
3. **统一异常处理**：异常处理交给全局异常处理器统一处理
4. **完整的API文档**：包含详细的Swagger注解说明

**请求参数**：
- `id`：慢病申报记录ID（路径参数）
- `Authorization`：会话ID（请求头）

**返回数据**：
- 成功时返回`ChronicDiseaseRegistrationDetailResponseDto`类型的详情数据
- 失败时返回相应的错误信息

**前置条件**：
- 申报记录必须存在
- 申报记录必须属于当前用户

这个接口复用了现有的服务层方法`registrationService.getDetailById()`，保持了代码的一致性和可维护性。

---

