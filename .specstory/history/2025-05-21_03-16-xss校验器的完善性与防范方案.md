<!-- Generated by SpecStory -->

# XSS校验器的完善性与防范方案 (2025-05-21 12:16:19)

_**User**_

检查这个Xss校验器是否完善？如果不完善，有没有其它更成熟的防范XSS攻击的方案？

---

_**Assistant**_



---

当前的XSS校验器存在明显不足，不够完善：

1. **正则表达式缺陷**：`matcher.matches()`要求整个字符串完全匹配模式，而XSS攻击通常嵌入在正常文本中。应该使用`matcher.find()`检查是否含有匹配项。

2. **检测范围有限**：当前正则表达式只能检测简单的HTML标签，无法检测诸如：
   - JavaScript事件处理器（如`onload`、`onclick`）
   - JavaScript URL（如`javascript:alert(1)`）
   - 各种编码形式的XSS攻击
   - CSS表达式注入

更成熟的XSS防范方案：

1. **使用专业安全库**：
   - ESAPI (OWASP Enterprise Security API)
   - jsoup HTML清理器
   - OWASP Java Encoder

2. **综合防御策略**：
   - 输入验证：验证所有输入
   - 输出编码：根据不同上下文使用适当编码
   - 内容安全策略(CSP)：限制浏览器执行脚本的能力
   - X-XSS-Protection头：启用浏览器内置XSS过滤器

3. **jsoup清理方案示例**：
```java
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

public class XssValidator implements ConstraintValidator<Xss, String> {
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        // 使用jsoup进行严格清理，只允许纯文本
        String clean = Jsoup.clean(value, Safelist.none());
        return clean.equals(value);
    }
}
```

4. **Spring框架集成**：
   - 配置全局XSS过滤器
   - 使用ResponseBodyAdvice进行全局输出编码

在实际应用中，建议采用多层防御方式，结合输入验证、输出编码、内容安全策略等多种技术，而不仅仅依赖单一的验证器来防范XSS攻击。

---

_**User**_

使用 jsoup 方案重构 XssValidator

---

_**Assistant**_



---

```java
package com.ruoyi.common.xss;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

/**
 * 自定义xss校验注解实现（使用jsoup实现）
 *
 * <AUTHOR>
 */
public class XssValidator implements ConstraintValidator<Xss, String> {
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        return !containsHtml(value);
    }

    /**
     * 判断字符串中是否包含HTML标签或脚本
     * 
     * @param value 需要验证的字符串
     * @return true包含HTML标签，false不包含
     */
    public static boolean containsHtml(String value) {
        // 使用jsoup进行清理，使用none()安全列表（不允许任何HTML元素）
        String clean = Jsoup.clean(value, Safelist.none());
        // 如果清理后的字符串与原字符串不同，说明包含HTML
        return !clean.equals(value);
    }
}
```

你需要在项目的pom.xml中添加jsoup依赖：

```xml
<dependency>
    <groupId>org.jsoup</groupId>
    <artifactId>jsoup</artifactId>
    <version>1.16.1</version>
</dependency>
```

这个实现比原来的正则表达式方案更可靠，因为：
1. 解决了原正则使用`matches()`而非`find()`的问题
2. jsoup专门设计用于处理HTML，能识别各种复杂的HTML结构和属性
3. 能够检测更广泛的XSS攻击向量，包括各种事件处理属性和JavaScript URL
4. 维护成本更低，不需要不断更新复杂的正则表达式

---

_**User**_

```
curl 'http://localhost:9999/ph/chronic-disease/edit' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=fbd880d5-8ab0-4212-9201-e9ead22e4acd' \
  -H 'Origin: http://localhost:9999' \
  -H 'Referer: http://localhost:9999/ph/chronic-disease/edit/41' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'X-Requested-With: XMLHttpRequest' \
  -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  --data-raw 'id=41&name=Test&insuranceCityWorker=0&insuranceCityResident=1&insuranceDistrict=1&insuranceCorp=0&experts=%E5%BC%A0%E4%B8%89&criteria=%E6%A0%87%E5%87%86%E4%B8%80%0D%0A%E6%A0%87%E5%87%86%E4%BA%8C%0D%0A%E6%A0%87%E5%87%86%E4%B8%89&documents=%E8%B5%84%E6%96%99%E4%B8%80%0D%0A%E8%B5%84%E6%96%99%E4%BA%8C%0D%0A%E8%B5%84%E6%96%99%E4%B8%89'
```
响应：
```
{"msg":"认定资料不能包含特殊字符","code":500}
```

目前的XssValidator似乎过于严格，连换行符也不允许

---

_**Assistant**_



---

当前的XssValidator实现确实过于严格，它使用Safelist.none()会移除所有HTML标签，但jsoup的清理机制也会处理文本格式化字符。要解决换行符被错误标记为XSS的问题，我需要改进实现：

```java
package com.ruoyi.common.xss;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

/**
 * 自定义xss校验注解实现（使用jsoup实现）
 *
 * <AUTHOR>
 */
public class XssValidator implements ConstraintValidator<Xss, String> {
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        return !containsHtml(value);
    }

    /**
     * 判断字符串中是否包含HTML标签或脚本
     * 
     * @param value 需要验证的字符串
     * @return true包含HTML标签，false不包含
     */
    public static boolean containsHtml(String value) {
        // 保存原始的换行符
        String originalValue = value;
        
        // 使用jsoup进行清理，使用none()安全列表（不允许任何HTML元素）
        String clean = Jsoup.clean(value, Safelist.none());
        
        // 将清理后的字符串和原字符串中的换行符标准化处理
        String normalizedOriginal = normalizeLineBreaks(originalValue);
        String normalizedClean = normalizeLineBreaks(clean);
        
        // 比较标准化后的字符串
        return !normalizedClean.equals(normalizedOriginal);
    }
    
    /**
     * 标准化字符串中的换行符，使比较更准确
     * 
     * @param input 输入字符串
     * @return 标准化后的字符串
     */
    private static String normalizeLineBreaks(String input) {
        if (input == null) {
            return "";
        }
        // 将所有类型的换行符转换为统一格式，便于比较
        return input.replaceAll("\\r\\n|\\r|\\n", "\n");
    }
}
```

如果这种方法仍然不能解决问题，还有一个更直接的替代方案：

```java
public static boolean containsHtml(String value) {
    // 针对HTML标签的正则表达式
    String htmlPattern = "<[^>]*>";
    // 针对JavaScript事件的正则表达式
    String scriptPattern = "javascript:|on\\w+\\s*=|\\bdata-\\w+\\s*=";
    
    // 检查HTML标签
    boolean containsHtml = Pattern.compile(htmlPattern).matcher(value).find();
    // 检查JavaScript事件
    boolean containsScript = Pattern.compile(scriptPattern, Pattern.CASE_INSENSITIVE).matcher(value).find();
    
    return containsHtml || containsScript;
}
```

这个方法不使用jsoup，而是通过正则表达式直接检测常见的XSS攻击向量，同时不会对换行符等格式字符产生干扰。

选择哪种方案取决于你的安全需求和用户体验平衡。

---

_**User**_

我引入了一个 `buildSafelist` 方法，检查 safelist 是否多线程安全，能否复用？

---

_**Assistant**_



---

Safelist在jsoup中不是线程安全的，但它符合"构建后不可变"的模式。只要在初始化完成后不再修改，就可以安全地在多线程环境中共享使用。

可以通过以下改进优化性能：

```java
public class XssValidator implements ConstraintValidator<Xss, String> {
    
    /**
     * 预先构建并缓存Safelist，提高性能
     */
    private static final Safelist XSS_SAFELIST = buildSafelist();
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        // 方法内容不变
    }

    public static boolean containsHtml(String value) {
        // 保存原始的换行符
        String originalValue = value;
        
        // 使用缓存的XSS_SAFELIST，避免重复创建
        String clean = Jsoup.clean(value, XSS_SAFELIST);
        
        // 标准化处理部分不变
        String normalizedOriginal = normalizeLineBreaks(originalValue);
        String normalizedClean = normalizeLineBreaks(clean);
        
        return !normalizedClean.equals(normalizedOriginal);
    }
    
    // normalizeLineBreaks方法不变
    
    /**
     * 构建一个 Xss 清理的 Safelist 规则。
     * 此方法仅在类加载时调用一次，结果被缓存在XSS_SAFELIST常量中。
     */
    private static Safelist buildSafelist() {
        // 方法内容不变
    }
}
```

这种优化有以下好处：

1. **性能提升**：避免每次验证都创建新的Safelist对象
2. **内存优化**：减少对象创建，降低GC压力
3. **线程安全**：Safelist在构建完成后不再修改，可以安全地在多线程间共享

只要确保不会在其他地方修改这个静态Safelist实例，这种做法是完全线程安全的。

---

_**User**_

`containsHtml` 这个方法名已不符合它的行为，根据代码逻辑改为更恰当的方法名

---

_**Assistant**_



---

```java
/**
 * 检测字符串中是否包含潜在的XSS风险内容
 * 
 * @param value 需要验证的字符串
 * @return true表示包含XSS风险，false表示安全
 */
public static boolean containsXssRisk(String value) {
    // 保存原始的换行符
    String originalValue = value;
    
    // 使用缓存的XSS_SAFELIST，避免重复创建
    String clean = Jsoup.clean(value, XSS_SAFELIST);
    
    // 将清理后的字符串和原字符串中的换行符标准化处理
    String normalizedOriginal = normalizeLineBreaks(originalValue);
    String normalizedClean = normalizeLineBreaks(clean);
    
    // 比较标准化后的字符串
    return !normalizedClean.equals(normalizedOriginal);
}
```

同时，需要修改调用此方法的地方：

```java
@Override
public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
    if (StringUtils.isBlank(value)) {
        return true;
    }
    return !containsXssRisk(value);
}
```

新方法名`containsXssRisk`更准确地反映了方法的实际功能：检测输入中是否包含不在安全列表中允许的、可能导致XSS攻击的内容，而不仅仅是检测是否包含HTML标签。

---

_**User**_

为 `containsXssRisk` 方法编写单元测试

---

_**Assistant**_



---

为`containsXssRisk`方法编写的单元测试：

```java
package com.ruoyi.common.xss;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class XssValidatorTest {

    @Test
    @DisplayName("正常文本不应包含XSS风险")
    void normalTextShouldNotContainXssRisk() {
        // 给定
        String normalText = "这是一段正常的文本，不包含任何HTML标签";
        
        // 当...时
        boolean result = XssValidator.containsXssRisk(normalText);
        
        // 那么
        assertFalse(result, "正常文本不应包含XSS风险");
    }

    @Test
    @DisplayName("包含HTML标签的文本应识别为XSS风险")
    void textWithHtmlTagsShouldContainXssRisk() {
        // 给定
        String htmlText = "这是<b>粗体</b>文本";
        
        // 当...时
        boolean result = XssValidator.containsXssRisk(htmlText);
        
        // 那么
        assertTrue(result, "包含HTML标签的文本应识别为XSS风险");
    }

    @Test
    @DisplayName("包含XSS攻击脚本的文本应识别为风险")
    void textWithXssPayloadShouldContainXssRisk() {
        // 给定
        String[] xssPayloads = {
            "<script>alert('XSS')</script>",
            "<img src=\"javascript:alert('XSS')\">",
            "<a href=\"javascript:alert('XSS')\">点击我</a>",
            "<div onload=\"alert('XSS')\">内容</div>",
            "<img src=\"x\" onerror=\"alert('XSS')\">"
        };
        
        // 当...时 / 那么
        for (String payload : xssPayloads) {
            assertTrue(XssValidator.containsXssRisk(payload), 
                    "XSS载荷应被检测: " + payload);
        }
    }

    @Test
    @DisplayName("包含换行符的文本不应误判为XSS风险")
    void textWithLineBreaksShouldNotContainXssRisk() {
        // 给定
        String textWithLineBreaks = "第一行\n第二行\r\n第三行\r第四行";
        
        // 当...时
        boolean result = XssValidator.containsXssRisk(textWithLineBreaks);
        
        // 那么
        assertFalse(result, "包含换行符的文本不应误判为XSS风险");
    }

    @Test
    @DisplayName("空字符串或null不应包含XSS风险")
    void emptyOrNullStringShouldNotContainXssRisk() {
        // 给定
        String emptyString = "";
        String nullString = null;
        
        // 当...时 / 那么
        assertFalse(XssValidator.containsXssRisk(emptyString), "空字符串不应包含XSS风险");
        assertFalse(XssValidator.containsXssRisk(nullString), "null不应包含XSS风险");
    }

    @Test
    @DisplayName("在XSS安全列表中允许的HTML标签不应识别为风险")
    void allowedHtmlShouldNotContainXssRisk() {
        // 给定
        String[] allowedHtmlSamples = {
            "<p>段落文本</p>",
            "<a href='https://example.com' target='_blank'>安全链接</a>",
            "<img src='data:image/png;base64,iVBORw0=' alt='测试图片'>",
            "<span style='color:red;'>红色文本</span>",
            "<div class='container'>容器</div>"
        };
        
        // 当...时 / 那么
        for (String html : allowedHtmlSamples) {
            assertFalse(XssValidator.containsXssRisk(html), 
                    "安全列表中允许的HTML不应被标记为风险: " + html);
        }
    }
}
```

这个测试覆盖了以下场景：

1. 正常文本 - 验证普通文本不会被错误地标记为XSS风险
2. 包含HTML标签的文本 - 验证基本HTML检测能力
3. 包含常见XSS攻击载荷的文本 - 验证对各种XSS攻击向量的检测
4. 包含换行符的文本 - 确认换行符处理正确，不会误判
5. 空字符串和null - 验证边界情况处理
6. 安全列表中允许的HTML - 验证配置的XSS_SAFELIST正常工作

要运行这些测试，你需要在项目中添加JUnit 5依赖：

```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.8.2</version>
    <scope>test</scope>
</dependency>
```

---

