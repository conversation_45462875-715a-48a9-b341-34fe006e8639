<!-- Generated by SpecStory -->

# 添加菜单查询方法 (2024-12-31 19:02:16)

_**User**_

添加按照parentId和menuName获取单个菜单的方法

---

_**Assistant**_

我将帮您添加按照parentId和menuName获取单个菜单的方法。需要在以下几个文件中进行修改:

1. 首先在SysMenuMapper接口中添加新方法:

```java:ruoyi-system/src/main/java/com/ruoyi/system/mapper/SysMenuMapper.java
// ... 其他代码

/**
 * 根据父ID和菜单名称查询菜单
 * 
 * @param parentId 父菜单ID
 * @param menuName 菜单名称
 * @return 菜单对象
 */
SysMenu selectMenuByParentIdAndName(@Param("parentId") Long parentId, @Param("menuName") String menuName);
```

2. 在SysMenuMapper.xml中添加对应的SQL映射:

```xml:ruoyi-system/src/main/resources/mapper/system/SysMenuMapper.xml
<!-- 根据父ID和菜单名称查询菜单 -->
<select id="selectMenuByParentIdAndName" resultMap="SysMenuResult">
    <include refid="selectMenuVo"/>
    where parent_id = #{parentId} and menu_name = #{menuName}
</select>
```

3. 在ISysMenuService接口中添加新方法:

```java:ruoyi-system/src/main/java/com/ruoyi/system/service/ISysMenuService.java
/**
 * 根据父ID和菜单名称查询菜单
 *
 * @param parentId 父菜单ID
 * @param menuName 菜单名称
 * @return 菜单对象
 */
SysMenu selectMenuByParentIdAndName(Long parentId, String menuName);
```

4. 在SysMenuServiceImpl中实现该方法:

```java:ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysMenuServiceImpl.java
/**
 * 根据父ID和菜单名称查询菜单
 *
 * @param parentId 父菜单ID
 * @param menuName 菜单名称
 * @return 菜单对象
 */
@Override
public SysMenu selectMenuByParentIdAndName(Long parentId, String menuName) {
    return menuMapper.selectMenuByParentIdAndName(parentId, menuName);
}
```

这些修改添加了一个新的方法,可以通过父ID和菜单名称来精确查询单个菜单。主要特点:

1. 使用@Param注解标注参数,便于在XML中引用
2. 复用了现有的selectMenuVo SQL片段
3. 返回单个SysMenu对象而不是列表
4. 保持了与现有代码风格的一致性

您现在可以通过以下方式使用这个新方法:

```java
SysMenu menu = menuService.selectMenuByParentIdAndName(parentId, menuName);
```

这个方法在需要精确定位某个菜单时会很有用,比如在验证菜单唯一性或者进行菜单关系维护时。

---

