<!-- Generated by SpecStory -->

# 查找PUT请求限制的原因 (2025-05-27 19:32:31)

_**User**_

应用似乎限制了不允许PUT请求，能查出来是在哪个地方限制的吗？

---

_**Assistant**_

我来帮你查找应用中可能限制PUT请求的配置。让我先搜索一下相关的配置和代码。

---

<details>
            <summary>Searched codebase "PUT请求限制 HTTP方法限制" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/RequireSessionTestController.java` | L77-104 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L188-215 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L1-55 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L55-100 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssFilter.java` | L1-56 |
| `docs/chronic-disease-registration/clear-agent-flag-api.md` | L1-57 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L1-36 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L36-84 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/interceptor/RepeatSubmitInterceptor.java` | L1-46 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/ext/FileUploader.kt` | L24-59 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L100-133 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ServletUtils.java` | L72-141 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java` | L276-311 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/filter/captcha/CaptchaValidateFilter.java` | L1-53 |
| `ruoyi-common/src/main/java/com/ruoyi/common/annotation/RepeatSubmit.java` | L1-23 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ServletUtils.java` | L1-72 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/RequireSessionTestController.java` | L22-77 |
| `ruoyi-common/src/main/java/com/ruoyi/common/annotation/Log.java` | L1-47 |
| `docs/chronic-disease-registration/clear-agent-flag-api.md` | L143-191 |
| `ruoyi-common/src/main/java/com/ruoyi/common/constant/UserConstants.java` | L1-85 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L180-224 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt` | L39-93 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/aspectj/LogAspect.java` | L202-223 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L84-135 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L177-218 |

</details>

---

我看到了一些线索，让我进一步搜索一下具体的HTTP方法限制配置。

---

<details>
            <summary>Grep search for "PUT.*不支持|不支持.*PUT" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "HttpRequestMethodNotSupportedException PUT请求方法不支持" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L55-100 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L1-36 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L1-55 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L84-135 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L100-133 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/MapDataUtil.java` | L1-44 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/controller/common/CommonController.java` | L71-94 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/RequireSessionTestController.java` | L77-104 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L36-84 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/controller/common/CommonController.java` | L1-29 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L188-215 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ServletUtils.java` | L72-141 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/JacksonConfig.java` | L1-24 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt` | L39-93 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L133-177 |
| `xydlfy-ph/docs/chronic-disease-registration-editability.md` | L66-111 |
| `ruoyi-common/src/main/java/com/ruoyi/common/core/controller/BaseController.java` | L29-89 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/ABCPosBillService.java` | L29-74 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/aspectj/LogAspect.java` | L202-223 |
| `ruoyi-common/src/main/java/com/ruoyi/common/annotation/Log.java` | L1-47 |
| `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/BaseEntity.java` | L1-103 |
| `ruoyi-system/src/main/java/com/ruoyi/system/domain/SysOperLog.java` | L1-94 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java` | L135-180 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/FileApiController.kt` | L1-41 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiController.kt` | L89-145 |

</details>

---

让我查看一下XSS过滤器，它看起来可能是限制PUT请求的地方：

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/xss/XssFilter.java

---

找到了！问题就在这里。让我看看这个XSS过滤器是如何配置的：

---

<details>
            <summary>Searched codebase "XssFilter配置 web.xml filter-mapping" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/FilterConfig.java` | L1-43 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssFilter.java` | L1-56 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssHttpServletRequestWrapper.java` | L1-33 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssUtil.java` | L1-59 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssValidator.java` | L1-17 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/Xss.java` | L1-27 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/filter/XFrameOptionsFilter.java` | L1-22 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java` | L244-276 |
| `ruoyi-admin/src/main/resources/application.yml` | L152-181 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssUtil.java` | L59-73 |
| `ruoyi-common/src/test/java/com/ruoyi/common/xss/XssValidatorTest.java` | L1-58 |
| `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/entity/SysUser.java` | L198-284 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/CustomShiroFilterFactoryBean.java` | L22-60 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java` | L311-361 |
| `ruoyi-admin/src/main/resources/application.yml` | L96-152 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java` | L1-37 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java` | L276-311 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/CustomShiroFilterFactoryBean.java` | L1-22 |
| `xydlfy-ph/docs/swagger-guide.md` | L106-189 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/filter/online/OnlineSessionFilter.java` | L1-24 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/filter/kickout/KickoutSessionFilter.java` | L1-31 |
| `ruoyi-admin/src/main/resources/application-prod.yml` | L60-132 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/CustomShiroFilterFactoryBean.java` | L60-74 |
| `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysNoticeServiceImpl.java` | L1-85 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/filter/captcha/CaptchaValidateFilter.java` | L1-53 |

</details>

---

现在让我查看配置文件确认XSS过滤器的设置：

---

Read file: ruoyi-admin/src/main/resources/application.yml

---

现在我明白了问题所在！让我检查`XssHttpServletRequestWrapper`的具体实现：

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/xss/XssHttpServletRequestWrapper.java

---

看起来`XssHttpServletRequestWrapper`并没有覆盖`getInputStream()`或`getReader()`方法，所以应该不是这里的问题。让我看看测试中的PUT请求是否真的可以工作：

---

<details>
            <summary>Searched codebase "PUT请求 测试 实际运行" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L188-215 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/RequireSessionTestController.java` | L77-104 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/controller/demo/controller/DemoOperateController.java` | L51-83 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L739-785 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L98-128 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L657-694 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L155-188 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/ValidationBehaviorTest.java` | L516-548 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/ValidationBehaviorTest.java` | L548-590 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/ValidationBehaviorTest.java` | L679-706 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L433-453 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L181-211 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L121-149 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L71-98 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L694-739 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L211-229 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L405-433 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L368-405 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/RequireSessionBehaviorTest.java` | L128-155 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java` | L337-368 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L174-199 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L44-64 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/ValidationBehaviorTest.java` | L590-627 |
| `docs/chronic-disease-registration/clear-agent-flag-api.md` | L1-57 |
| `ruoyi-admin/src/test/java/space/lzhq/ph/ValidationBehaviorTest.java` | L653-679 |

</details>

---

现在我明白问题了！在`XssFilter`中的`shouldExcludeURL`方法中，有这样的逻辑：

```java
// GET DELETE 不过滤
if (method == null || method.matches("GET") || method.matches("DELETE")) {
    return true;
}
```

这意味着只有GET和DELETE请求会跳过XSS过滤，而PUT、POST、PATCH等请求都会被XSS过滤器处理。

但是，问题不在于XSS过滤器本身限制PUT请求，而在于：

1. PUT请求会被XSS过滤器包装成`XssHttpServletRequestWrapper`
2. 这个包装器可能在某些情况下干扰请求体的读取
3. 你的慢病申报API已经被加入到了XSS过滤器的排除列表中：

```yaml
xss:
  excludes: /system/notice/*,/ph/article/*,/ph/wikiArticle/*,/api/chronic-disease-registration-attachment/*,/api/chronic-disease-registration/*
```

让我检查一下你遇到PUT请求问题的具体接口路径：

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt

---

现在我看到问题了！你的PUT接口路径是`/api/chronic-disease-registration/{id}/clear-agent-flag`，而在XSS配置的排除列表中，虽然有`/api/chronic-disease-registration/*`，但这个模式可能不能匹配包含路径变量的URL。

让我检查一下`StringUtils.matches`方法的实现：

---

<details>
            <summary>Searched codebase "StringUtils.matches 实现 URL匹配" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/StringUtils.java` | L433-504 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/StringUtils.java` | L228-296 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ServletUtils.java` | L141-185 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/IpUtils.java` | L207-271 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/ext/HisExt.kt` | L1-29 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/StringUtils.java` | L1-80 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/IpUtils.java` | L271-314 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/sql/SqlUtil.java` | L1-53 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssFilter.java` | L1-56 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L1-55 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ServletUtils.java` | L1-72 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/IpUtils.java` | L1-57 |
| `ruoyi-admin/src/main/resources/static/ajax/libs/summernote/summernote.js` | L9495-9512 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/StringUtils.java` | L296-331 |
| `ruoyi-admin/src/main/resources/static/ajax/libs/highlight/highlight.min.js` | L467-475 |
| `ruoyi-quartz/src/main/java/com/ruoyi/quartz/util/JobInvokeUtil.java` | L62-94 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/util/FilenameDecoder.java` | L1-58 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L55-100 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/http/HttpUtils.java` | L133-177 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/StringUtils.java` | L331-392 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/StringUtils.java` | L158-228 |
| `ruoyi-admin/src/main/resources/static/ajax/libs/bootstrap-select/bootstrap-select.js` | L23-89 |
| `ruoyi-admin/src/main/resources/static/ajax/libs/typeahead/bootstrap3-typeahead.js` | L290-328 |
| `ruoyi-common/src/main/java/com/ruoyi/common/xss/XssUtil.java` | L1-59 |
| `ruoyi-admin/src/main/resources/static/ajax/libs/typeahead/bootstrap-typeahead.js` | L290-328 |

</details>

---

好了，现在我找到了关键信息！`StringUtils.matches`方法使用了Spring的`AntPathMatcher`来进行路径匹配。让我查看AntPathMatcher的匹配规则。

从代码中可以看到：

1. XSS过滤器的排除列表配置是：`/api/chronic-disease-registration/*`
2. 实际的PUT请求路径是：`/api/chronic-disease-registration/{id}/clear-agent-flag`
3. `StringUtils.matches`使用`AntPathMatcher`进行匹配，其中`*`只匹配单层路径

问题在于：**`/api/chronic-disease-registration/*`模式不能匹配`/api/chronic-disease-registration/{id}/clear-agent-flag`这样的多层路径！**

要匹配多层路径，应该使用`**`模式。

## 解决方案

你需要修改XSS过滤器的排除配置，将：

```yaml
xss:
  excludes: /system/notice/*,/ph/article/*,/ph/wikiArticle/*,/api/chronic-disease-registration-attachment/*,/api/chronic-disease-registration/*
```

修改为：

```yaml
xss:
  excludes: /system/notice/*,/ph/article/*,/ph/wikiArticle/*,/api/chronic-disease-registration-attachment/*,/api/chronic-disease-registration/**
```

注意最后一个配置项从`/api/chronic-disease-registration/*`改为`/api/chronic-disease-registration/**`。

---



---

## 问题总结

你遇到的PUT请求限制问题是由**XSS过滤器的路径匹配配置不正确**导致的。具体原因是：

1. **XSS过滤器只对特定HTTP方法进行过滤**：
   - GET和DELETE请求会跳过XSS过滤
   - POST、PUT、PATCH等请求会被XSS过滤器处理

2. **路径排除配置不够精确**：
   - 原配置：`/api/chronic-disease-registration/*`
   - 实际路径：`/api/chronic-disease-registration/{id}/clear-agent-flag`
   - Spring的AntPathMatcher中，`*`只匹配单层路径，不能匹配多层

3. **XSS过滤器可能干扰请求处理**：
   - 当PUT请求被XSS过滤器包装成`XssHttpServletRequestWrapper`时
   - 可能在某些情况下影响请求体的正确处理

## 修复方法

将配置文件中的：
```yaml
/api/chronic-disease-registration/*
```

修改为：
```yaml
/api/chronic-disease-registration/**
```

这样配置后：
- `**`可以匹配任意层级的路径
- 所有以`/api/chronic-disease-registration/`开头的请求都会跳过XSS过滤
- PUT请求不再被XSS过滤器拦截处理

现在你的PUT请求应该可以正常工作了！

---

