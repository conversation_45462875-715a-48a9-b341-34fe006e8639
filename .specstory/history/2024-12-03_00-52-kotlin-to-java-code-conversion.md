<!-- Generated by SpecStory -->

# Kotlin to Java Code Conversion (2024-12-03 09:52:08)

_**User**_

把这两个kotlin文件改写为Java

---

_**Assistant**_

我来帮你把这两个Kotlin文件转换为Java版本。

首先是 XmlRequestParams.java:

```java:ph-pay-ccb/src/main/java/space/lzhq/ph/pay/ccb/bean/XmlRequestParams.java
package space.lzhq.ph.pay.ccb.bean;

import org.dromara.hutool.core.reflect.FieldUtil;
import org.dromara.hutool.core.text.StrUtil;
import space.lzhq.ph.pay.ccb.Configuration;

import java.lang.reflect.Field;
import java.util.Locale;
import java.util.Random;

public class XmlRequestParams {
    private final String txCode;

    protected XmlRequestParams(String txCode) {
        this.txCode = txCode;
    }

    public String toXML() {
        StringBuilder builder = new StringBuilder();
        for (Field field : this.getClass().getDeclaredFields()) {
            Object fieldValue = FieldUtil.getFieldValue(this, field);
            if (fieldValue == null) {
                continue;
            }
            String name = StrUtil.toUnderlineCase(field.getName()).toUpperCase(Locale.getDefault());
            builder.append("    <").append(name).append(">")
                  .append(fieldValue)
                  .append("</").append(name).append(">\n");
        }
        return builder.toString();
    }

    public String toParam() {
        // 生成随机的序列码
        long requestSn = System.currentTimeMillis() + new Random().nextInt(89999999) + 10000000;
        
        return "<?xml version=\"1.0\" encoding=\"GB2312\" standalone=\"yes\" ?>\n" +
               "<TX>\n" +
               "  <REQUEST_SN>" + requestSn + "</REQUEST_SN>\n" +
               "  <CUST_ID>" + Configuration.getMerchantId() + "</CUST_ID>\n" +
               "  <USER_ID>" + Configuration.getUserId() + "</USER_ID>\n" +
               "  <PASSWORD>" + Configuration.getPassword() + "</PASSWORD>\n" +
               "  <TX_CODE>" + txCode + "</TX_CODE>\n" +
               "  <LANGUAGE>CN</LANGUAGE>\n" +
               "  <TX_INFO>\n" +
               toXML() +
               "  </TX_INFO>\n" +
               "</TX>";
    }
}
```

然后是 GenerateBillParams.java:

```java:ph-pay-ccb/src/main/java/space/lzhq/ph/pay/ccb/bean/GenerateBillParams.java
package space.lzhq.ph.pay.ccb.bean;

/**
 * 生成账单的参数，date参数的格式如下：20191206
 */
public class GenerateBillParams extends XmlRequestParams {
    private final String date;
    private String kind = "1";
    private String filetype = "1";

    public GenerateBillParams(String date) {
        super("5W1005");
        this.date = date;
    }

    public String getDate() {
        return date;
    }

    /**
     * 流水状态：1：已结流水（默认），0：未结流水 （POS商户送1）
     */
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    /**
     * 文件类型：1-txt（默认） 2-excel 3-txt+excel
     */
    public String getFiletype() {
        return filetype;
    }

    public void setFiletype(String filetype) {
        this.filetype = filetype;
    }
}
```

主要的转换变化说明：

1. 将 Kotlin 的扩展函数转换为 Java 类的实例方法
2. 添加了必要的 getter/setter 方法
3. 将 Kotlin 的 data class 转换为普通的 Java 类
4. 将 Kotlin 的属性转换为 Java 的字段
5. 将 Kotlin 的字符串模板转换为 Java 的字符串拼接
6. 将 Kotlin 的 null 安全操作符转换为 Java 的 null 检查
7. 将 Kotlin 的 Random.nextInt(from, to) 转换为 Java 的等效实现

需要注意的是，Java 版本需要确保 Configuration 类有相应的 getter 方法（getMerchantId、getUserId、getPassword）。

---

_**User**_

因为 `FilterRegistrationBean` 需要泛型参数，最佳实践是不是把 `getFilterRegistrationBean` 中的 filter 由 lambda 表达式改写为内部类？

---

_**Assistant**_

是的，使用内部类来实现 Filter 接口是更好的做法，这样可以明确指定泛型类型并提高代码的可读性。以下是修改建议：

```java:ruoyi-framework/src/main/java/com/ruoyi/framework/config/DruidConfig.java
// ... existing code ...

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.druid.statViewServlet.enabled", havingValue = "true")
    public FilterRegistrationBean<Filter> removeDruidFilterRegistrationBean(DruidStatProperties properties) {
        // ... existing code ...
        FilterRegistrationBean<Filter> registrationBean = getFilterRegistrationBean();
        registrationBean.addUrlPatterns(commonJsPattern);
        return registrationBean;
    }

    @NotNull
    private static FilterRegistrationBean<Filter> getFilterRegistrationBean() {
        final String filePath = "support/http/resources/js/common.js";
        
        Filter filter = new Filter() {
            @Override
            public void doFilter(jakarta.servlet.ServletRequest request, jakarta.servlet.ServletResponse response, 
                    jakarta.servlet.FilterChain chain) throws java.io.IOException, jakarta.servlet.ServletException {
                chain.doFilter(request, response);
                // 重置缓冲区，响应头不会被重置
                response.resetBuffer();
                // 获取common.js
                String text = Utils.readFromResource(filePath);
                // 正则替换banner, 除去底部的广告信息
                text = text.replaceAll("<a.*?banner\"></a><br/>", "");
                text = text.replaceAll("powered.*?shrek.wang</a>", "");
                response.getWriter().write(text);
            }
        };
        
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(filter);
        return registrationBean;
    }

// ... existing code ...
```

主要改动：
1. 为 `FilterRegistrationBean` 添加了 `Filter` 泛型参数
2. 将 lambda 表达式改写为匿名内部类实现
3. 明确指定了 ServletRequest、ServletResponse 等类型的完整包路径

这样的改动可以：
1. 提供更好的类型安全性
2. 使代码结构更清晰
3. 避免泛型相关的警告
4. 更符合 Java 的传统编码风格

---

_**User**_

检查 DruidConfig 是否存在其它问题？

---

_**Assistant**_



让我检查一下 DruidConfig 的其他潜在问题：

1. **数据源初始化顺序问题**：
```java
@Bean(name = "dynamicDataSource")
@DependsOnDatabaseInitialization
@Primary
public DynamicDataSource dataSource(DataSource masterDataSource) {
    // ...
}
```
可能需要添加 `@DependsOn` 注解来确保从数据源在主数据源之后初始化。

2. **异常处理不够完善**：
```java
public void setDataSource(Map<Object, Object> targetDataSources, String sourceName, String beanName) {
    try {
        DataSource dataSource = SpringUtils.getBean(beanName);
        targetDataSources.put(sourceName, dataSource);
    } catch (Exception e) {
        log.error("setDataSource [{}] failed: {}", sourceName, e.getMessage(), e);
    }
}
```
仅记录日志可能不够，应该考虑是否需要抛出自定义异常。

3. **配置属性缺少验证**：
应该为必要的配置属性添加验证。

4. **日志使用问题**：
可以使用 `@Slf4j` 注解替代手动声明日志对象。

5. **缺少配置属性的文档**：
应该添加更详细的配置说明。

这是改进后的版本：

```java:ruoyi-framework/src/main/java/com/ruoyi/framework/config/DruidConfig.java
package com.ruoyi.framework.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceBuilder;
import com.alibaba.druid.spring.boot3.autoconfigure.properties.DruidStatProperties;
import com.alibaba.druid.util.Utils;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.config.properties.DruidProperties;
import com.ruoyi.framework.datasource.DynamicDataSource;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.sql.init.dependency.DependsOnDatabaseInitialization;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.util.Assert;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Druid数据源配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class DruidConfig {

    @Bean
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource masterDataSource(DruidProperties druidProperties) {
        log.info("初始化主数据源");
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        return druidProperties.dataSource(dataSource);
    }

    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave")
    @ConditionalOnProperty(prefix = "spring.datasource.druid.slave", name = "enabled", havingValue = "true")
    public DataSource slaveDataSource(DruidProperties druidProperties) {
        log.info("初始化从数据源");
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        dataSource.setAsyncInit(true);
        return druidProperties.dataSource(dataSource);
    }

    @Bean
    @ConfigurationProperties("spring.datasource.druid.lis")
    @ConditionalOnProperty(prefix = "spring.datasource.druid.lis", name = "enabled", havingValue = "true")
    public DataSource lisDataSource(DruidProperties druidProperties) {
        log.info("初始化LIS数据源");
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        dataSource.setInitialSize(0);
        dataSource.setMinIdle(0);
        dataSource.setMaxWait(2000);
        dataSource.setConnectionProperties("oracle.net.CONNECT_TIMEOUT=100;oracle.jdbc.ReadTimeout=5000");
        dataSource.setAsyncInit(true);
        return druidProperties.dataSource(dataSource);
    }

    @Bean(name = "dynamicDataSource")
    @DependsOnDatabaseInitialization
    @DependsOn({"masterDataSource", "slaveDataSource", "lisDataSource"})
    @Primary
    public DynamicDataSource dataSource(DataSource masterDataSource) {
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceType.MASTER.name(), masterDataSource);
        setDataSource(targetDataSources, DataSourceType.SLAVE.name(), "slaveDataSource");
        setDataSource(targetDataSources, DataSourceType.LIS.name(), "lisDataSource");
        return new DynamicDataSource(masterDataSource, targetDataSources);
    }

    /**
     * 设置数据源
     *
     * @param targetDataSources 备选数据源集合
     * @param sourceName        数据源名称
     * @param beanName         bean名称
     * @throws ServiceException 数据源设置异常
     */
    public void setDataSource(Map<Object, Object> targetDataSources, String sourceName, String beanName) {
        try {
            DataSource dataSource = SpringUtils.getBean(beanName);
            Assert.notNull(dataSource, "数据源不能为空");
            targetDataSources.put(sourceName, dataSource);
            log.info("设置数据源[{}]成功", sourceName);
        } catch (Exception e) {
            log.error("设置数据源[{}]失败", sourceName, e);
            throw new ServiceException(String.format("设置数据源[%s]失败", sourceName), e);
        }
    }

    /**
     * 去除监控页面底部的广告
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.druid.statViewServlet.enabled", havingValue = "true")
    public FilterRegistrationBean<Filter> removeDruidFilterRegistrationBean(DruidStatProperties properties) {
        DruidStatProperties.StatViewServlet config = properties.getStatViewServlet();
        String pattern = config.getUrlPattern() != null ? config.getUrlPattern() : "/druid/*";
        String commonJsPattern = pattern.replace("*", "js/common.js");
        FilterRegistrationBean<Filter> registrationBean = getFilterRegistrationBean();
        registrationBean.addUrlPatterns(commonJsPattern);
        return registrationBean;
    }

    private static FilterRegistrationBean<Filter> getFilterRegistrationBean() {
        final String filePath = "support/http/resources/js/common.js";

        Filter filter = new Filter() {
            @Override
            public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
                    throws IOException, ServletException {
                chain.doFilter(request, response);
                response.resetBuffer();
                String text = Utils.readFromResource(filePath);
                text = text.replaceAll("<a.*?banner\"></a><br/>", "");
                text = text.replaceAll("powered.*?shrek.wang</a>", "");
                response.getWriter().write(text);
            }
        };

        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(filter);
        return registrationBean;
    }
}
```

主要改进：

1. 添加了 `@Slf4j` 注解
2. 增加了数据源初始化的日志
3. 添加了 `@DependsOn` 注解确保数据源初始化顺序
4. 完善了异常处理，添加了自定义异常抛出
5. 添加了参数校验
6. 优化了代码注释和文档
7. 使用了更明确的泛型类型
8. 规范了导入语句的顺序
9. 优化了异常日志记录格式

这些改进提高了代码的健壮性、可维护性和可读性。

---

