<!-- Generated by SpecStory -->

# Implement XssCleaner with Jsoup Safelist (2025-05-21 21:19:10)

_**User**_

添加 XssCleaner 类，使用 jsoup 清洗字符串，与 @XssValidator.java 使用相同的 Safelist

---

_**Assistant**_



---

<details>
            <summary>Listed directory ruoyi-common/src/main/java/com/ruoyi/common/xss • **4** results</summary>
        
| Name |
|-------|
| 📄 `XssFilter.java` |
| 📄 `XssValidator.java` |
| 📄 `XssHttpServletRequestWrapper.java` |
| 📄 `Xss.java` |

</details>

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/xss/Xss.java

---

```diff
- 
+ package com.ruoyi.common.xss;
+ 
+ import org.apache.commons.lang3.StringUtils;
+ import org.jsoup.Jsoup;
+ import org.jsoup.safety.Safelist;
+ 
+ /**
+  * XSS清理工具类，使用jsoup实现
+  *
+  * <AUTHOR>
+  */
+ public class XssCleaner {
+ 
+     /**
+      * 预先构建并缓存Safelist，提高性能
+      */
+     private static final Safelist XSS_SAFELIST = buildSafelist();
+ 
+     /**
+      * 清理XSS风险字符串
+      *
+      * @param html 可能含有XSS风险的字符串
+      * @return 清理后的字符串
+      */
+     public static String clean(String html) {
+         if (StringUtils.isBlank(html)) {
+             return html;
+         }
+         return Jsoup.clean(html, XSS_SAFELIST);
+     }
+ 
+     /**
+      * 判断字符串是否含有XSS风险
+      *
+      * @param html 待检测的字符串
+      * @return true表示安全，false表示含有XSS风险
+      */
+     public static boolean isValid(String html) {
+         if (StringUtils.isBlank(html)) {
+             return true;
+         }
+         return Jsoup.isValid(html, XSS_SAFELIST);
+     }
+ 
+     /**
+      * 构建一个 Xss 清理的 Safelist 规则。
+      * 基于 Safelist#relaxed() 的基础上:
+      * 1. 扩展支持了 style 和 class 属性
+      * 2. a 标签额外支持了 target 属性
+      * 3. img 标签额外支持了 data 协议，便于支持 base64
+      *
+      * @return Safelist
+      */
+     private static Safelist buildSafelist() {
+         // 使用 jsoup 提供的默认的
+         Safelist relaxedSafelist = Safelist.relaxed();
+         // 富文本编辑时一些样式是使用 style 来进行实现的
+         // 比如红色字体 style="color:red;", 所以需要给所有标签添加 style 属性
+         // 注意：style 属性会有注入风险 <img STYLE="background-image:url(javascript:alert('XSS'))">
+         relaxedSafelist.addAttributes(":all", "style", "class");
+         // 保留 a 标签的 target 属性
+         relaxedSafelist.addAttributes("a", "target");
+         // 支持img 为base64
+         relaxedSafelist.addProtocols("img", "src", "data");
+         return relaxedSafelist;
+     }
+ } 
```

---

已成功创建XssCleaner类，实现了以下功能：

1. 使用与XssValidator相同的Safelist规则，确保一致的XSS防护策略
2. 提供了两个主要方法：
   - `clean(String html)`：清理可能含有XSS风险的字符串
   - `isValid(String html)`：检测字符串是否含有XSS风险

此工具类可用于在应用程序中主动清理XSS风险，例如在接收用户输入或展示内容前进行处理。

---

_**User**_

1. 将 @XssCleaner.java 重命名为 `XssUtil`
2. @XssValidator.java 中的 isValid 直接调用 XssUtil.isValid

---

_**Assistant**_



---

```diff
- 
+ package com.ruoyi.common.xss;
+ 
+ import org.apache.commons.lang3.StringUtils;
+ import org.jsoup.Jsoup;
+ import org.jsoup.safety.Safelist;
+ 
+ /**
+  * XSS清理工具类，使用jsoup实现
+  *
+  * <AUTHOR>
+  */
+ public class XssUtil {
+ 
+     /**
+      * 预先构建并缓存Safelist，提高性能
+      */
+     private static final Safelist XSS_SAFELIST = buildSafelist();
+ 
+     /**
+      * 清理XSS风险字符串
+      *
+      * @param html 可能含有XSS风险的字符串
+      * @return 清理后的字符串
+      */
+     public static String clean(String html) {
+         if (StringUtils.isBlank(html)) {
+             return html;
+         }
+         return Jsoup.clean(html, XSS_SAFELIST);
+     }
+ 
+     /**
+      * 判断字符串是否含有XSS风险
+      *
+      * @param html 待检测的字符串
+      * @return true表示安全，false表示含有XSS风险
+      */
+     public static boolean isValid(String html) {
+         if (StringUtils.isBlank(html)) {
+             return true;
+         }
+         return Jsoup.isValid(html, XSS_SAFELIST);
+     }
+ 
+     /**
+      * 构建一个 Xss 清理的 Safelist 规则。
+      * 基于 Safelist#relaxed() 的基础上:
+      * 1. 扩展支持了 style 和 class 属性
+      * 2. a 标签额外支持了 target 属性
+      * 3. img 标签额外支持了 data 协议，便于支持 base64
+      *
+      * @return Safelist
+      */
+     private static Safelist buildSafelist() {
+         // 使用 jsoup 提供的默认的
+         Safelist relaxedSafelist = Safelist.relaxed();
+         // 富文本编辑时一些样式是使用 style 来进行实现的
+         // 比如红色字体 style="color:red;", 所以需要给所有标签添加 style 属性
+         // 注意：style 属性会有注入风险 <img STYLE="background-image:url(javascript:alert('XSS'))">
+         relaxedSafelist.addAttributes(":all", "style", "class");
+         // 保留 a 标签的 target 属性
+         relaxedSafelist.addAttributes("a", "target");
+         // 支持img 为base64
+         relaxedSafelist.addProtocols("img", "src", "data");
+         return relaxedSafelist;
+     }
+ } 
```

---

```diff
  import jakarta.validation.ConstraintValidatorContext;
  import org.apache.commons.lang3.StringUtils;
- import org.jsoup.Jsoup;
- import org.jsoup.safety.Safelist;
  
  /**
```

```diff
   */
  public class XssValidator implements ConstraintValidator<Xss, String> {
- 
-     /**
-      * 预先构建并缓存Safelist，提高性能
-      */
-     private static final Safelist XSS_SAFELIST = buildSafelist();
  
      @Override
      public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
-         if (StringUtils.isBlank(value)) {
-             return true;
-         }
-         return Jsoup.isValid(value, XSS_SAFELIST);
-     }
- 
-     /**
-      * 构建一个 Xss 清理的 Safelist 规则。
-      * 基于 Safelist#relaxed() 的基础上:
-      * 1. 扩展支持了 style 和 class 属性
-      * 2. a 标签额外支持了 target 属性
-      * 3. img 标签额外支持了 data 协议，便于支持 base64
-      *
-      * @return Safelist
-      */
-     private static Safelist buildSafelist() {
-         // 使用 jsoup 提供的默认的
-         Safelist relaxedSafelist = Safelist.relaxed();
-         // 富文本编辑时一些样式是使用 style 来进行实现的
-         // 比如红色字体 style="color:red;", 所以需要给所有标签添加 style 属性
-         // 注意：style 属性会有注入风险 <img STYLE="background-image:url(javascript:alert('XSS'))">
-         relaxedSafelist.addAttributes(":all", "style", "class");
-         // 保留 a 标签的 target 属性
-         relaxedSafelist.addAttributes("a", "target");
-         // 支持img 为base64
-         relaxedSafelist.addProtocols("img", "src", "data");
-         return relaxedSafelist;
+         return XssUtil.isValid(value);
      }
  }
```

---

删除旧的XssCleaner文件，因为已经被XssUtil替代

---

已完成所有修改：

1. 创建了新的 `XssUtil` 工具类，包含以下功能：
   - `clean(String html)` - 清理可能含有XSS风险的字符串
   - `isValid(String html)` - 判断字符串是否含有XSS风险
   - 保持了与原来相同的 `Safelist` 配置

2. 简化了 `XssValidator` 类：
   - 移除了重复的 `Safelist` 构建逻辑和相关常量
   - 改为直接调用 `XssUtil.isValid(value)` 方法
   - 移除了不再需要的导入

3. 删除了原来的 `XssCleaner.java` 文件

这样改动后，代码结构更清晰，避免了重复的 Safelist 定义和构建逻辑，提高了代码的可维护性。现在 XSS 相关功能都集中在 `XssUtil` 中管理，而验证器只负责调用相应的工具方法。

---

_**User**_

For the code present, we get this error:
```
Add a private constructor to hide the implicit public one.
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_



---

```diff
   */
  public class XssUtil {
+ 
+     /**
+      * 私有构造函数，防止实例化
+      */
+     private XssUtil() {
+     }
  
      /**
```

---

已添加私有构造函数解决问题。对于工具类，应该添加私有构造函数防止实例化，符合最佳实践。

---

_**User**_

@XssFilter.java In ruoyi-common/src/main/java/com/ruoyi/common/xss/XssFilter.java at line 38, rename the method handleExcludeURL to a name that clearly indicates it returns a boolean, such as shouldExcludeURL or isExcludedURL. This improves code readability by better reflecting the method's purpose. Also, ensure that the method name change is consistently applied wherever it is called.

---

_**Assistant**_



---

```diff
              throws IOException, ServletException {
          HttpServletRequest req = (HttpServletRequest) request;
-         if (handleExcludeURL(req)) {
+         if (shouldExcludeURL(req)) {
              chain.doFilter(request, response);
              return;
```

```diff
      }
  
-     private boolean handleExcludeURL(HttpServletRequest request) {
+     private boolean shouldExcludeURL(HttpServletRequest request) {
          String url = request.getServletPath();
          String method = request.getMethod();
```

---

已将方法名从`handleExcludeURL`修改为`shouldExcludeURL`，并更新了调用该方法的地方。新方法名更清晰地表明该方法返回一个布尔值，表示是否应该排除URL，提高了代码的可读性。

---

