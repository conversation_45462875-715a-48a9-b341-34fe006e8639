<!-- Generated by SpecStory -->

# 编写 clearAgentFlag 方法的单元测试 (2025-05-27 20:22:30)

_**User**_

为 `clearAgentFlag` 方法编写单元测试

---

_**Assistant**_

我来为 `clearAgentFlag` 方法编写全面的单元测试。基于已有的测试类结构和代码分析，我将添加测试覆盖所有场景。

---

```diff
          verify(fileChecker).isFileExists(insuranceCardAttachment.getFileUrl());
      }
+ 
+     // ==================== clearAgentFlag 方法测试 ====================
+ 
+     @Test
+     @DisplayName("清除代办标志 - 成功场景")
+     void clearAgentFlag_WithValidData_ShouldClearAgentFlagAndReturnUpdatedRegistration() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+         when(registrationService.updateById(any(ChronicDiseaseRegistration.class))).thenReturn(true);
+ 
+         // When
+         ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);
+ 
+         // Then
+         assertNotNull(result);
+         assertEquals(Integer.valueOf(0), result.getAgentFlag());
+         assertNull(result.getAgentName());
+         assertNull(result.getAgentIdCardNo());
+         assertNull(result.getAgentMobile());
+         assertNull(result.getAgentRelation());
+         assertNotNull(result.getUpdateTime());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService).updateById(registration);
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 申报记录不存在")
+     void clearAgentFlag_WithNonExistentRegistration_ShouldThrowResourceNotFoundException() {
+         // Given
+         String registrationId = "non-existent-id";
+         when(registrationService.getById(registrationId)).thenReturn(null);
+ 
+         // When & Then
+         ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertTrue(exception.getMessage().contains("申报记录不存在"));
+         assertEquals("ChronicDiseaseRegistration", exception.getResourceType());
+         assertEquals(registrationId, exception.getResourceId());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService, never()).updateById(any());
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 申报记录不属于当前用户")
+     void clearAgentFlag_WithUnauthorizedAccess_ShouldThrowUnauthorizedResourceAccessException() {
+         // Given
+         String registrationId = "test-registration-id";
+         String otherUserOpenId = "other-user-open-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, otherUserOpenId);
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+ 
+         // When & Then
+         UnauthorizedResourceAccessException exception = assertThrows(UnauthorizedResourceAccessException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertTrue(exception.getMessage().contains("申报记录不属于当前用户"));
+         assertEquals("ChronicDiseaseRegistration", exception.getResourceType());
+         assertEquals(registrationId, exception.getResourceId());
+         assertEquals(OPEN_ID, exception.getUserId());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService, never()).updateById(any());
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 申报记录状态不允许修改（已提交）")
+     void clearAgentFlag_WithSubmittedStatus_ShouldThrowEditNotAllowedException() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         registration.setStatus(ChronicDiseaseRegistrationStatus.SUBMITTED); // 已提交状态不允许修改
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+ 
+         // When & Then
+         EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
+         assertEquals("ChronicDiseaseRegistration", exception.getResourceType());
+         assertEquals(registrationId, exception.getResourceId());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService, never()).updateById(any());
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 申报记录状态不允许修改（审核中）")
+     void clearAgentFlag_WithUnderReviewStatus_ShouldThrowEditNotAllowedException() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         registration.setStatus(ChronicDiseaseRegistrationStatus.UNDER_REVIEW); // 审核中状态不允许修改
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+ 
+         // When & Then
+         EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
+         assertEquals("ChronicDiseaseRegistration", exception.getResourceType());
+         assertEquals(registrationId, exception.getResourceId());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService, never()).updateById(any());
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 申报记录状态不允许修改（已通过）")
+     void clearAgentFlag_WithApprovedStatus_ShouldThrowEditNotAllowedException() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         registration.setStatus(ChronicDiseaseRegistrationStatus.APPROVED); // 已通过状态不允许修改
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+ 
+         // When & Then
+         EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
+         assertEquals("ChronicDiseaseRegistration", exception.getResourceType());
+         assertEquals(registrationId, exception.getResourceId());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService, never()).updateById(any());
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 申报记录状态不允许修改（已拒绝）")
+     void clearAgentFlag_WithRejectedStatus_ShouldThrowEditNotAllowedException() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         registration.setStatus(ChronicDiseaseRegistrationStatus.REJECTED); // 已拒绝状态不允许修改
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+ 
+         // When & Then
+         EditNotAllowedException exception = assertThrows(EditNotAllowedException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertTrue(exception.getMessage().contains("申报记录当前状态不允许修改"));
+         assertEquals("ChronicDiseaseRegistration", exception.getResourceType());
+         assertEquals(registrationId, exception.getResourceId());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService, never()).updateById(any());
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 数据库更新失败")
+     void clearAgentFlag_WithDatabaseUpdateFailure_ShouldThrowRuntimeException() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+         when(registrationService.updateById(any(ChronicDiseaseRegistration.class))).thenReturn(false);
+ 
+         // When & Then
+         RuntimeException exception = assertThrows(RuntimeException.class,
+                 () -> registrationService.clearAgentFlag(registrationId, OPEN_ID));
+ 
+         assertEquals("清除代办标志失败，请重试", exception.getMessage());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService).updateById(registration);
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 草稿状态允许修改")
+     void clearAgentFlag_WithDraftStatus_ShouldSucceed() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT); // 草稿状态允许修改
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+         when(registrationService.updateById(any(ChronicDiseaseRegistration.class))).thenReturn(true);
+ 
+         // When
+         ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);
+ 
+         // Then
+         assertNotNull(result);
+         assertEquals(Integer.valueOf(0), result.getAgentFlag());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService).updateById(registration);
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 退回状态允许修改")
+     void clearAgentFlag_WithReturnedStatus_ShouldSucceed() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         registration.setStatus(ChronicDiseaseRegistrationStatus.RETURNED); // 退回状态允许修改
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+         when(registrationService.updateById(any(ChronicDiseaseRegistration.class))).thenReturn(true);
+ 
+         // When
+         ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);
+ 
+         // Then
+         assertNotNull(result);
+         assertEquals(Integer.valueOf(0), result.getAgentFlag());
+ 
+         verify(registrationService).getById(registrationId);
+         verify(registrationService).updateById(registration);
+     }
+ 
+     @Test
+     @DisplayName("清除代办标志 - 验证所有代办相关字段都被清空")
+     void clearAgentFlag_ShouldClearAllAgentRelatedFields() {
+         // Given
+         String registrationId = "test-registration-id";
+         ChronicDiseaseRegistration registration = createRegistrationWithAgentFlag(registrationId, OPEN_ID);
+         LocalDateTime originalUpdateTime = registration.getUpdateTime();
+         
+         when(registrationService.getById(registrationId)).thenReturn(registration);
+         when(registrationService.updateById(any(ChronicDiseaseRegistration.class))).thenReturn(true);
+ 
+         // When
+         ChronicDiseaseRegistration result = registrationService.clearAgentFlag(registrationId, OPEN_ID);
+ 
+         // Then
+         assertEquals(Integer.valueOf(0), result.getAgentFlag());
+         assertNull(result.getAgentName());
+         assertNull(result.getAgentIdCardNo());
+         assertNull(result.getAgentMobile());
+         assertNull(result.getAgentRelation());
+         assertTrue(result.getUpdateTime().isAfter(originalUpdateTime));
+ 
+         // 验证其他字段未被修改
+         assertEquals(OPEN_ID, result.getOpenId());
+         assertEquals(validDto.getPatientName(), result.getPatientName());
+         assertEquals(validDto.getPatientIdCardNo(), result.getPatientIdCardNo());
+     }
+ 
+     /**
+      * 创建包含代办信息的申报记录
+      */
+     private ChronicDiseaseRegistration createRegistrationWithAgentFlag(String registrationId, String openId) {
+         ChronicDiseaseRegistration registration = new ChronicDiseaseRegistration();
+         registration.setId(registrationId);
+         registration.setOpenId(openId);
+         registration.setPatientName(validDto.getPatientName());
+         registration.setPatientIdCardNo(validDto.getPatientIdCardNo());
+         registration.setPatientInsuranceCardNo(validDto.getPatientInsuranceCardNo());
+         registration.setInsuranceType(validDto.getInsuranceType());
+         registration.setPatientMobile(validDto.getPatientMobile());
+         registration.setStatus(ChronicDiseaseRegistrationStatus.DRAFT);
+         registration.setCreateTime(LocalDateTime.now().minusDays(1));
+         registration.setUpdateTime(LocalDateTime.now().minusHours(1));
+         
+         // 设置代办信息
+         registration.setAgentFlag(1);
+         registration.setAgentName("代办人姓名");
+         registration.setAgentIdCardNo("520524199001010158");
+         registration.setAgentMobile("13800138001");
+         registration.setAgentRelation("配偶");
+         
+         return registration;
+     }
  } 
```

---

现在我需要添加缺失的import语句来支持新增的测试中使用的异常类：

---



---

现在我还需要添加 `LocalDateTime` 的import：

---



---

现在让我运行测试来验证代码是否正确：

---

```bash
cd /Users/<USER>/IdeaProjects/zhangyi-xyd6fy && mvn test -Dtest=ChronicDiseaseRegistrationServiceImplTest#clearAgentFlag* -q
```

---

我已经为 `clearAgentFlag` 方法编写了全面的单元测试。以下是测试覆盖的场景总结：

## 测试场景覆盖

### ✅ 成功场景
1. **正常清除代办标志** - 验证所有代办相关字段被正确清空
2. **草稿状态允许修改** - 验证草稿状态下可以清除代办标志
3. **退回状态允许修改** - 验证退回状态下可以清除代办标志

### ✅ 异常场景
1. **申报记录不存在** - 抛出 `ResourceNotFoundException`
2. **申报记录不属于当前用户** - 抛出 `UnauthorizedResourceAccessException`
3. **申报记录状态不允许修改** - 针对以下状态抛出 `EditNotAllowedException`：
   - 已提交 (SUBMITTED)
   - 审核中 (UNDER_REVIEW)
   - 已通过 (APPROVED)
   - 已拒绝 (REJECTED)
4. **数据库更新失败** - 抛出 `RuntimeException`

### ✅ 边界条件验证
1. **字段完整性验证** - 确保所有代办相关字段都被清空
2. **时间戳更新** - 验证 `updateTime` 字段被正确更新
3. **其他字段不受影响** - 验证非代办相关字段保持不变

## 测试特点

1. **遵循项目约定** - 使用中文 `@DisplayName` 注解和下划线命名
2. **完整的Mock验证** - 验证所有依赖方法的调用
3. **异常信息验证** - 验证异常消息和异常属性的正确性
4. **业务逻辑覆盖** - 覆盖了所有业务规则和状态转换
5. **辅助方法** - 提供了 `createRegistrationWithAgentFlag` 辅助方法来创建测试数据

这些测试确保了 `clearAgentFlag` 方法在各种场景下都能正确工作，并且符合业务需求和异常处理规范。

---

