<!-- Generated by SpecStory -->

# 调换卡片位置 (2025-06-12 20:18:48)

_**User**_

调换`操作`和`申报进度`两个卡片的位置

---

_**Assistant**_



---

我来帮您调换"操作"和"申报进度"两个卡片的位置。我需要找到这两个卡片在HTML中的位置并交换它们的顺序。

---



---

完成！我已经成功调换了"操作"和"申报进度"两个卡片的位置。现在在右侧操作面板中，"申报进度"卡片会显示在"操作"卡片的上方。

具体变化：
1. **申报进度**卡片现在位于右侧面板的最顶部
2. **操作**卡片现在位于申报进度卡片下方
3. **审核操作面板**和**审核提醒**面板仍然保持在操作卡片下方的位置

这样的布局调整使得用户可以更直观地先查看申报进度，然后再进行相关操作，符合信息查看的优先级顺序。

---

_**User**_

为本次改动编写提交信息

---

_**Assistant**_



---

### Rule: git-commit

**commit message**

```markdown
# Conventional Commits 规范 AI 提示词

请根据 @Conventional Commits 1.0.0 规范，为以下代码变更生成规范化的 git 提交信息。要求如下：

1. **类型**（type）必须是规范中的类型，如：feat、fix、docs、style、refactor、test、chore、build、ci 等。
2. **可选范围**（scope）可加可不加，若加需用小括号包裹，如 feat(parser):。
3. **描述**（description）需简明扼要，说明本次更改的主要内容，首字母小写，结尾不加句号。
4. 如有**详细说明**，可在描述后空一行补充正文（body）。
5. 如有**BREAKING CHANGE**，需在类型后加 ! 或在 footer 处注明 BREAKING CHANGE: 详细说明。
6. 如有**关联 issue**，可在 footer 处注明 Refs: #123 或 Closes: #123。
7. 提交信息优先使用中文，但如果是技术术语或专有名词，请使用英文。

请严格按照如下格式输出：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**示例：**

```
feat(parser): add ability to parse arrays

support parsing of nested arrays and edge cases

Refs: #123
```

---

**请用上述格式为以下变更生成提交信息：**

【在此处补充你的代码变更内容或描述】```

---

基于项目的git提交规范，我为本次UI界面调整编写提交信息：

```bash
style(ph): 调整慢病申报详情页右侧卡片布局顺序

将申报进度卡片移至操作卡片上方，优化信息查看优先级。用户现在可以更直观地先查看申报进度，然后再进行相关操作。

影响范围：
- 慢病申报详情页面布局
- 提升用户体验和信息层次
```

这个提交信息遵循了Conventional Commits规范：
- **类型**: `style` - 因为这是UI布局的调整，不影响功能逻辑
- **范围**: `ph` - 表示这是公共卫生模块的修改
- **描述**: 简明扼要地说明了本次调整的内容
- **正文**: 详细解释了调整的目的和影响

---

