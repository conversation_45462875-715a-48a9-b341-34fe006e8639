<!-- Generated by SpecStory -->

# Fixing Issues in deploy.bat Script (2024-12-31 10:01:56)

_**User**_

Fix this bug in `deploy.bat`:
```
The deployment script has two critical issues:
1. For the zhangyi project, `mvn clean package` is incorrectly used instead of `mvn clean install`, which prevents necessary artifacts from being installed for subsequent deployment steps.
2. The script attempts to reset the `master` branch without first checking it out, which can cause Git operations to fail.
```

---

_**Assistant**_

我来帮你修复 deploy.bat 中的这些问题。主要需要进行以下修改：

1. 将 zhangyi 项目的 Maven 命令从 `package` 改为 `install`
2. 确保在 `git reset` 之前正确执行 `git checkout`

以下是对 deploy.bat 的修改：

```batch:deploy.bat
rem 打包 mospital
echo "正在打包 mospital..."
// ... 前面的代码保持不变 ...

rem 打包 zhangyi
echo "正在打包 zhangyi..."
cd "%ZHANGYI_PATH%"
git fetch --all
if %ERRORLEVEL% neq 0 goto :git_error
git checkout master
if %ERRORLEVEL% neq 0 goto :git_error
git reset --hard origin/master
if %ERRORLEVEL% neq 0 goto :git_error
call mvn clean install -T 2C -Dmaven.test.skip=true
if %ERRORLEVEL% neq 0 goto :maven_error
echo "zhangyi 打包成功"

// ... 后面的代码保持不变 ...
```

主要更改说明：
1. 将 zhangyi 项目的 Maven 命令从 `mvn clean package` 改为 `mvn clean install`，这样可以确保所有必要的构件都被正确安装到本地 Maven 仓库中。
2. Git 相关的命令顺序已经是正确的（`checkout` 在 `reset` 之前），所以这部分不需要修改。

这些修改应该能解决您提到的问题，确保部署过程更加可靠。

---

