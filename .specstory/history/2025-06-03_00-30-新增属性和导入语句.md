<!-- Generated by SpecStory -->

# 新增属性和导入语句 (2025-06-03 09:30:44)

_**User**_

在文件 @/xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDiseaseRegistration.java 的`ChronicDiseaseRegistration`类中新增私有属性`documentAttachmentIds`，类型为`List<String>`，并添加Javadoc注释`/** 认定资料附件ID列表 */`，同时确保类顶部已导入`java.util.List`，若未导入则添加`import java.util.List;`语句。

---

_**Assistant**_



---

我来帮您在`ChronicDiseaseRegistration`类中添加新的私有属性`documentAttachmentIds`。

---

```diff
  import java.time.LocalDate;
  import java.time.LocalDateTime;
+ import java.util.List;
  
  /**
```

```diff
      @NotBlank(message = "申报患者医保卡号不能为空")
      private String patientInsuranceCardNo;
+ 
+     /**
+      * 认定资料附件ID列表
+      */
+     private List<String> documentAttachmentIds;
  
      /**
```

---

完成！我已经在`ChronicDiseaseRegistration`类中成功添加了：

1. **导入语句**：在导入部分添加了`import java.util.List;`
2. **新属性**：在`patientInsuranceCardNo`属性之后添加了新的私有属性：
   ```java
   /**
    * 认定资料附件ID列表
    */
   private List<String> documentAttachmentIds;
   ```

新属性被放置在合适的位置，与其他附件ID相关的属性靠近，保持了代码的逻辑性和可读性。由于使用了Lombok的`@Data`注解，该属性会自动生成getter和setter方法。

---

_**User**_

Mybatis Plus 对于 `List<String>` 类型的支持如何？

---

_**Assistant**_



---

MyBatis-Plus 对于 `List<String>` 类型的支持需要通过特殊处理，因为关系型数据库通常不直接支持集合类型。以下是几种常见的处理方式：

## 1. 使用 TypeHandler 处理（推荐）

### 方式一：JSON 格式存储
最常用的方式是将 `List<String>` 转换为 JSON 字符串存储在数据库中：

```java
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;

public class ChronicDiseaseRegistration {
    /**
     * 认定资料附件ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> documentAttachmentIds;
}
```

对应的数据库字段类型应该是 `TEXT` 或 `VARCHAR`，存储格式如：
```json
["attachment1", "attachment2", "attachment3"]
```

### 方式二：逗号分隔字符串存储
如果希望在数据库中以逗号分隔的形式存储：

```java
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class StringListTypeHandler extends BaseTypeHandler<List<String>> {
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, String.join(",", parameter));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        return splitToList(columnValue);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String columnValue = rs.getString(columnIndex);
        return splitToList(columnValue);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String columnValue = cs.getString(columnIndex);
        return splitToList(columnValue);
    }

    private List<String> splitToList(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.stream(value.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
}
```

然后在实体类中使用：
```java
@TableField(typeHandler = StringListTypeHandler.class)
private List<String> documentAttachmentIds;
```

## 2. 使用关联表（推荐用于复杂关系）

如果需要更复杂的查询和关系管理，可以创建单独的关联表：

```sql
-- 主表
CREATE TABLE chronic_disease_registration (
    id VARCHAR(50) PRIMARY KEY,
    -- 其他字段...
);

-- 附件关联表
CREATE TABLE chronic_disease_attachments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    registration_id VARCHAR(50) NOT NULL,
    attachment_id VARCHAR(100) NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES chronic_disease_registration(id)
);
```

对应的实体类：
```java
public class ChronicDiseaseRegistration {
    @TableField(exist = false)
    private List<String> documentAttachmentIds;
    
    // 通过Service层手动维护这个关系
}
```

## 3. 配置说明

如果使用 JSON 存储方式，需要确保 MyBatis-Plus 配置正确：

```yaml
# application.yml
mybatis-plus:
  configuration:
    # 启用自动映射
    auto-mapping-behavior: partial
    # 启用驼峰命名转换
    map-underscore-to-camel-case: true
  # 全局配置
  global-config:
    db-config:
      # 逻辑删除配置等...
```

## 4. 推荐方案

对于您的场景（认定资料附件ID列表），我推荐使用 **JSON 格式存储**：

```java
/**
 * 认定资料附件ID列表
 */
@TableField(typeHandler = JacksonTypeHandler.class)
private List<String> documentAttachmentIds;
```

**优点：**
- 简单易用，MyBatis-Plus 内置支持
- 可以存储复杂的数据结构
- 查询时自动转换为 Java 对象

**注意事项：**
- 数据库字段类型建议使用 `TEXT` 或 `JSON`（MySQL 5.7+）
- 如果需要基于列表内容进行复杂查询，建议使用关联表方案
- JSON 方式不适合频繁的列表内容查询

您需要我帮您修改实体类以使用推荐的 JSON 存储方式吗？

---

_**User**_

修改实体类以使用推荐的 JSON 存储方式

---

_**Assistant**_



---

我来帮您修改实体类以使用推荐的 JSON 存储方式。

---

```diff
  import com.baomidou.mybatisplus.annotation.EnumValue;
  import com.baomidou.mybatisplus.annotation.IdType;
+ import com.baomidou.mybatisplus.annotation.TableField;
  import com.baomidou.mybatisplus.annotation.TableId;
  import com.baomidou.mybatisplus.annotation.TableName;
+ import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
  import com.fasterxml.jackson.annotation.JsonIgnore;
  import com.ruoyi.common.annotation.Excel;
```

```diff
       * 认定资料附件ID列表
       */
+     @TableField(typeHandler = JacksonTypeHandler.class)
      private List<String> documentAttachmentIds;
  
```

---

完成！我已经成功修改了实体类以使用推荐的 JSON 存储方式：

## 修改内容：

1. **添加了必要的导入语句：**
   - `import com.baomidou.mybatisplus.annotation.TableField;`
   - `import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;`

2. **为 `documentAttachmentIds` 字段添加了注解：**
   ```java
   /**
    * 认定资料附件ID列表
    */
   @TableField(typeHandler = JacksonTypeHandler.class)
   private List<String> documentAttachmentIds;
   ```

## 使用说明：

### 数据库字段类型
建议在数据库中将对应字段定义为：
```sql
ALTER TABLE chronic_disease_registration 
ADD COLUMN document_attachment_ids TEXT COMMENT '认定资料附件ID列表(JSON格式)';
```

### 存储格式
数据将以 JSON 数组格式存储在数据库中：
```json
["attachment_id_1", "attachment_id_2", "attachment_id_3"]
```

### 使用示例
```java
// 设置附件ID列表
ChronicDiseaseRegistration registration = new ChronicDiseaseRegistration();
List<String> attachmentIds = Arrays.asList("att001", "att002", "att003");
registration.setDocumentAttachmentIds(attachmentIds);

// MyBatis-Plus 会自动将 List<String> 转换为 JSON 字符串存储
registrationMapper.insert(registration);

// 查询时会自动将 JSON 字符串转换回 List<String>
ChronicDiseaseRegistration result = registrationMapper.selectById(id);
List<String> ids = result.getDocumentAttachmentIds(); // 自动转换回 List<String>
```

现在您的实体类已经可以正确处理 `List<String>` 类型的附件ID列表了！

---

_**User**_

为 `documentAttachmentIds` 列生成SQL语句，使用JSON类型

---

_**Assistant**_



---

我来为 `documentAttachmentIds` 字段生成SQL语句，使用JSON类型：

---

<details>
            <summary>Searched codebase "chronic_disease_registration CREATE TABLE DDL SQL" • **24** results</summary>
        
| File | Lines |
|------|-------|
| `docs/chronic-disease-registration/PRD.md` | L494-529 |
| `ddl/ph/ph_nat_patient.sql` | L1-20 |
| `ddl/ph/ph_patient.sql` | L1-34 |
| `ddl/ph/ph_doctor.sql` | L1-25 |
| `ddl/ph/ph_nat_person.sql` | L1-20 |
| `ddl/ph/ph_tijian_package.sql` | L1-13 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDiseaseRegistration.java` | L31-108 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDiseaseRegistration.java` | L1-31 |
| `ddl/ph/ph_drug.sql` | L1-17 |
| `ddl/ph/ph_nurse_card.sql` | L1-31 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDisease.java` | L1-73 |
| `ddl/ph/ph_department.sql` | L1-19 |
| `ddl/ph/ph_nat_sample1.sql` | L1-25 |
| `ddl/ph/psc_nurse.sql` | L1-12 |
| `ddl/ph/ph_tijian_package_item.sql` | L1-14 |
| `ddl/ph/ph_nat_sample.sql` | L1-25 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDiseaseRegistrationAttachment.java` | L1-27 |
| `ddl/ph/psc_order.sql` | L1-30 |
| `ddl/ph/psc_carer.sql` | L1-11 |
| `ddl/ph/alipay_payment.sql` | L1-27 |
| `ddl/ph/ph_yuyue.sql` | L1-21 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/mapper/ChronicDiseaseRegistrationMapper.java` | L1-9 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDiseaseRegistration.java` | L108-200 |
| `ddl/ph/ph_tijian_yuyue.sql` | L1-26 |

</details>

---

基于搜索结果，我现在为 `documentAttachmentIds` 字段生成相应的SQL语句。根据实体类注释，表名是 `chronic_disease_registration`，我将提供添加JSON字段的SQL语句：

## 为 `documentAttachmentIds` 字段生成SQL语句

### 1. 添加字段的 ALTER TABLE 语句（推荐 - 使用JSON类型）

```sql
-- MySQL 5.7+ 版本，推荐使用JSON类型
ALTER TABLE chronic_disease_registration 
ADD COLUMN document_attachment_ids JSON COMMENT '认定资料附件ID列表(JSON格式)';
```

### 2. 兼容性方案（使用TEXT类型）

```sql
-- 如果MySQL版本低于5.7，使用TEXT类型
ALTER TABLE chronic_disease_registration 
ADD COLUMN document_attachment_ids TEXT COMMENT '认定资料附件ID列表(JSON格式)';
```

### 3. 创建索引（可选）

如果需要对JSON内容进行查询，可以创建虚拟列索引：

```sql
-- MySQL 5.7+ 支持JSON函数和虚拟列索引
-- 为JSON数组长度创建索引
ALTER TABLE chronic_disease_registration 
ADD COLUMN document_attachment_count INT AS (JSON_LENGTH(document_attachment_ids)) VIRTUAL,
ADD INDEX idx_document_attachment_count (document_attachment_count);

-- 为特定附件ID创建搜索索引（如果需要）
-- 示例：搜索包含特定附件ID的记录
-- SELECT * FROM chronic_disease_registration 
-- WHERE JSON_CONTAINS(document_attachment_ids, '"attachment_id_123"');
```

### 4. 完整的建表语句示例（如果是新建表）

```sql
CREATE TABLE chronic_disease_registration (
    id VARCHAR(50) NOT NULL COMMENT '主键',
    open_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    patient_id_card_attachment_id VARCHAR(100) NOT NULL COMMENT '申报患者身份证附件ID',
    patient_id_card_no VARCHAR(18) NOT NULL COMMENT '申报患者身份证号',
    patient_name VARCHAR(50) NOT NULL COMMENT '申报患者姓名',
    patient_gender VARCHAR(10) COMMENT '申报患者性别',
    patient_birth_date DATE COMMENT '申报患者出生日期',
    patient_insurance_card_attachment_id VARCHAR(100) NOT NULL COMMENT '申报患者医保卡附件ID',
    patient_insurance_card_no VARCHAR(50) NOT NULL COMMENT '申报患者医保卡号',
    insurance_type VARCHAR(20) NOT NULL COMMENT '医保类型',
    patient_mobile VARCHAR(11) NOT NULL COMMENT '申报患者手机号',
    agent_flag INT NOT NULL DEFAULT 0 COMMENT '是否代办 0-否 1-是',
    agent_name VARCHAR(50) COMMENT '代办人姓名',
    agent_id_card_no VARCHAR(18) COMMENT '代办人身份证号',
    agent_id_card_attachment_id VARCHAR(100) COMMENT '代办人身份证附件ID',
    agent_mobile VARCHAR(11) COMMENT '代办人手机号',
    agent_relation VARCHAR(20) COMMENT '代办人与患者关系',
    disease_id INT NOT NULL COMMENT '慢病病种ID',
    document_attachment_ids JSON COMMENT '认定资料附件ID列表(JSON格式)',
    status VARCHAR(20) NOT NULL COMMENT '申报状态',
    submit_time DATETIME COMMENT '提交时间',
    approve_time DATETIME COMMENT '审批时间',
    reject_reason TEXT COMMENT '驳回原因',
    correction_advice TEXT COMMENT '整改意见',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_open_id (open_id),
    INDEX idx_patient_id_card_no (patient_id_card_no),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='慢病申报主表';
```

### 5. 数据示例

使用JSON类型存储后，数据格式如下：

```json
-- document_attachment_ids 字段存储示例
["attachment_001", "attachment_002", "attachment_003"]

-- 或者空数组
[]
```

### 6. 验证字段添加成功

```sql
-- 检查字段是否添加成功
DESCRIBE chronic_disease_registration;

-- 或者
SHOW CREATE TABLE chronic_disease_registration;
```

### 推荐使用方案

**推荐使用第1种方案（JSON类型）**，因为：
- 提供了原生的JSON数据类型支持
- 更好的性能和存储效率
- 支持JSON相关的查询函数
- 数据完整性和类型安全性更好

如果您的MySQL版本支持JSON类型（5.7+），建议使用第1种方案的SQL语句。

---

