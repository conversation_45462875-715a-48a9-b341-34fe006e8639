<!-- Generated by SpecStory -->

# Untitled (2025-06-10 00:28:18)

_**User**_

In xydlfy-ph/src/main/java/space/lzhq/ph/dto/request/ChronicDiseaseRegistrationApprovalDto.java around lines 40 to 45, the method isRejectReasonRequired() implements validation logic but is not integrated with the Bean Validation framework. To fix this, create a custom annotation interface named ValidRejectReason with appropriate target, retention, and constraint metadata, and implement a corresponding validator class RejectReasonValidator that enforces the rule. Then, annotate the DTO class with @ValidRejectReason to enable automatic validation during Bean Validation processing.

---

_**Assistant**_



---

我将帮您创建一个自定义的Bean Validation注解来替换当前的手动验证逻辑。让我先查看项目结构，然后创建所需的注解和验证器。

---

<details>
            <summary>Listed directory xydlfy-ph/src/main/kotlin/space/lzhq/ph/annotation • **4** results</summary>
        
| Name |
|-------|
| 📄 `RequireActivePatient.kt` |
| 📄 `RequirePscCarerSession.kt` |
| 📄 `RequirePscNurseSession.kt` |
| 📄 `RequireSession.kt` |

</details>

---

现在我将创建自定义的Bean Validation注解和验证器。首先创建注解接口：

---

```diff
- 
+ package space.lzhq.ph.annotation;
+ 
+ import jakarta.validation.Constraint;
+ import jakarta.validation.Payload;
+ 
+ import java.lang.annotation.Documented;
+ import java.lang.annotation.ElementType;
+ import java.lang.annotation.Retention;
+ import java.lang.annotation.RetentionPolicy;
+ import java.lang.annotation.Target;
+ 
+ /**
+  * 验证驳回原因的自定义注解
+  * 当审批状态为REJECTED时，驳回原因必须提供且不能为空
+  */
+ @Documented
+ @Constraint(validatedBy = RejectReasonValidator.class)
+ @Target({ElementType.TYPE})
+ @Retention(RetentionPolicy.RUNTIME)
+ public @interface ValidRejectReason {
+ 
+     /**
+      * 验证失败时的错误消息
+      */
+     String message() default "当审批状态为驳回时，必须提供驳回原因";
+ 
+     /**
+      * 验证分组
+      */
+     Class<?>[] groups() default {};
+ 
+     /**
+      * 负载
+      */
+     Class<? extends Payload>[] payload() default {};
+ } 
```

---

接下来创建验证器实现类：

---

```diff
- 
+ package space.lzhq.ph.annotation;
+ 
+ import jakarta.validation.ConstraintValidator;
+ import jakarta.validation.ConstraintValidatorContext;
+ import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationApprovalDto;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ 
+ /**
+  * 驳回原因验证器
+  * 验证当审批状态为REJECTED时，驳回原因必须提供且不能为空
+  */
+ public class RejectReasonValidator implements ConstraintValidator<ValidRejectReason, ChronicDiseaseRegistrationApprovalDto> {
+ 
+     @Override
+     public void initialize(ValidRejectReason constraintAnnotation) {
+         // 初始化方法，可以在此处获取注解参数
+     }
+ 
+     @Override
+     public boolean isValid(ChronicDiseaseRegistrationApprovalDto value, ConstraintValidatorContext context) {
+         // 如果对象为null，让其他验证器处理
+         if (value == null) {
+             return true;
+         }
+ 
+         // 如果状态为REJECTED，验证驳回原因是否提供
+         if (value.getStatus() == ChronicDiseaseRegistrationStatus.REJECTED) {
+             String rejectReason = value.getRejectReason();
+             
+             // 驳回原因不能为null且去除空白字符后不能为空
+             if (rejectReason == null || rejectReason.trim().isEmpty()) {
+                 // 自定义错误消息
+                 context.disableDefaultConstraintViolation();
+                 context.buildConstraintViolationWithTemplate("当审批状态为驳回时，驳回原因不能为空")
+                        .addPropertyNode("rejectReason")
+                        .addConstraintViolation();
+                 return false;
+             }
+         }
+ 
+         return true;
+     }
+ } 
```

---

现在修改DTO类，添加自定义注解并移除手动验证方法：

---



---

现在移除手动验证方法，因为它已经被Bean Validation注解替代：

---



---

现在让我验证修改后的DTO文件内容：

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/dto/request/ChronicDiseaseRegistrationApprovalDto.java

---

让我创建一个简单的测试来验证我们的自定义注解是否正常工作：

---

```diff
- 
+ package space.lzhq.ph.annotation;
+ 
+ import jakarta.validation.ConstraintViolation;
+ import jakarta.validation.Validation;
+ import jakarta.validation.Validator;
+ import jakarta.validation.ValidatorFactory;
+ import org.junit.jupiter.api.BeforeEach;
+ import org.junit.jupiter.api.DisplayName;
+ import org.junit.jupiter.api.Test;
+ import space.lzhq.ph.dto.request.ChronicDiseaseRegistrationApprovalDto;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ 
+ import java.util.Set;
+ 
+ import static org.junit.jupiter.api.Assertions.*;
+ 
+ /**
+  * ValidRejectReason 注解验证测试
+  */
+ @DisplayName("ValidRejectReason注解验证测试")
+ class ValidRejectReasonTest {
+ 
+     private Validator validator;
+ 
+     @BeforeEach
+     void setUp() {
+         ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
+         validator = factory.getValidator();
+     }
+ 
+     @Test
+     @DisplayName("当状态为APPROVED时，驳回原因为空应该通过验证")
+     void shouldPassValidationWhenStatusIsApprovedAndRejectReasonIsEmpty() {
+         // given
+         ChronicDiseaseRegistrationApprovalDto dto = new ChronicDiseaseRegistrationApprovalDto();
+         dto.setStatus(ChronicDiseaseRegistrationStatus.APPROVED);
+         dto.setRejectReason(null);
+ 
+         // when
+         Set<ConstraintViolation<ChronicDiseaseRegistrationApprovalDto>> violations = validator.validate(dto);
+ 
+         // then
+         assertTrue(violations.isEmpty(), "当状态为APPROVED时，驳回原因为空应该通过验证");
+     }
+ 
+     @Test
+     @DisplayName("当状态为REJECTED且驳回原因不为空时，应该通过验证")
+     void shouldPassValidationWhenStatusIsRejectedAndRejectReasonIsProvided() {
+         // given
+         ChronicDiseaseRegistrationApprovalDto dto = new ChronicDiseaseRegistrationApprovalDto();
+         dto.setStatus(ChronicDiseaseRegistrationStatus.REJECTED);
+         dto.setRejectReason("不符合申报条件");
+ 
+         // when
+         Set<ConstraintViolation<ChronicDiseaseRegistrationApprovalDto>> violations = validator.validate(dto);
+ 
+         // then
+         assertTrue(violations.isEmpty(), "当状态为REJECTED且驳回原因不为空时，应该通过验证");
+     }
+ 
+     @Test
+     @DisplayName("当状态为REJECTED但驳回原因为空时，应该验证失败")
+     void shouldFailValidationWhenStatusIsRejectedAndRejectReasonIsEmpty() {
+         // given
+         ChronicDiseaseRegistrationApprovalDto dto = new ChronicDiseaseRegistrationApprovalDto();
+         dto.setStatus(ChronicDiseaseRegistrationStatus.REJECTED);
+         dto.setRejectReason("");
+ 
+         // when
+         Set<ConstraintViolation<ChronicDiseaseRegistrationApprovalDto>> violations = validator.validate(dto);
+ 
+         // then
+         assertFalse(violations.isEmpty(), "当状态为REJECTED但驳回原因为空时，应该验证失败");
+         
+         ConstraintViolation<ChronicDiseaseRegistrationApprovalDto> violation = violations.iterator().next();
+         assertEquals("当审批状态为驳回时，驳回原因不能为空", violation.getMessage());
+         assertEquals("rejectReason", violation.getPropertyPath().toString());
+     }
+ 
+     @Test
+     @DisplayName("当状态为REJECTED但驳回原因只有空白字符时，应该验证失败")
+     void shouldFailValidationWhenStatusIsRejectedAndRejectReasonIsWhitespace() {
+         // given
+         ChronicDiseaseRegistrationApprovalDto dto = new ChronicDiseaseRegistrationApprovalDto();
+         dto.setStatus(ChronicDiseaseRegistrationStatus.REJECTED);
+         dto.setRejectReason("   ");
+ 
+         // when
+         Set<ConstraintViolation<ChronicDiseaseRegistrationApprovalDto>> violations = validator.validate(dto);
+ 
+         // then
+         assertFalse(violations.isEmpty(), "当状态为REJECTED但驳回原因只有空白字符时，应该验证失败");
+         
+         ConstraintViolation<ChronicDiseaseRegistrationApprovalDto> violation = violations.iterator().next();
+         assertEquals("当审批状态为驳回时，驳回原因不能为空", violation.getMessage());
+         assertEquals("rejectReason", violation.getPropertyPath().toString());
+     }
+ 
+     @Test
+     @DisplayName("当状态为REJECTED且驳回原因为null时，应该验证失败")
+     void shouldFailValidationWhenStatusIsRejectedAndRejectReasonIsNull() {
+         // given
+         ChronicDiseaseRegistrationApprovalDto dto = new ChronicDiseaseRegistrationApprovalDto();
+         dto.setStatus(ChronicDiseaseRegistrationStatus.REJECTED);
+         dto.setRejectReason(null);
+ 
+         // when
+         Set<ConstraintViolation<ChronicDiseaseRegistrationApprovalDto>> violations = validator.validate(dto);
+ 
+         // then
+         assertFalse(violations.isEmpty(), "当状态为REJECTED且驳回原因为null时，应该验证失败");
+         
+         ConstraintViolation<ChronicDiseaseRegistrationApprovalDto> violation = violations.iterator().next();
+         assertEquals("当审批状态为驳回时，驳回原因不能为空", violation.getMessage());
+         assertEquals("rejectReason", violation.getPropertyPath().toString());
+     }
+ } 
```

---

现在让我运行测试来验证我们的自定义注解是否正常工作：

---

Cancelled

---



---

