<!-- Generated by SpecStory -->

# 全局异常处理器中的异常处理 (2025-05-27 17:54:37)

_**User**_

在全局异常处理器中添加对 @EditNotAllowedException.java 异常的处理

---

_**Assistant**_

我将在全局异常处理器中添加对 `EditNotAllowedException` 异常的处理。根据这个异常的特点，它是一个业务逻辑异常，用于处理编辑操作不被允许的情况。

---

```diff
  import org.springframework.web.servlet.ModelAndView;
  import space.lzhq.ph.exception.AttachmentValidationException;
+ import space.lzhq.ph.exception.EditNotAllowedException;
  
  import java.util.HashMap;
```

```diff
                  .put(TRACE_ID, traceId);
      }
+ 
+     /**
+      * 编辑不允许异常处理
+      */
+     @ExceptionHandler(EditNotAllowedException.class)
+     public Object handleEditNotAllowedException(EditNotAllowedException e, HttpServletRequest request) {
+         String traceId = generateTraceId();
+         if (log.isWarnEnabled()) {
+             log.warn("编辑操作不允许 [TraceId: {}]: {}, 资源类型: {}, 资源ID: {}, 当前状态: {}",
+                     traceId, getRequestDetails(request, traceId), 
+                     e.getResourceType(), e.getResourceId(), e.getCurrentStatus(), e);
+         }
+         
+         if (ServletUtils.isAjaxRequest(request)) {
+             return AjaxResult.error(e.getMessage())
+                     .put(TRACE_ID, traceId);
+         } else {
+             return new ModelAndView("error/service", "errorMessage", e.getMessage());
+         }
+     }
  
      /**
```

---

我已经成功在全局异常处理器中添加了对 `EditNotAllowedException` 异常的处理。新增的异常处理器具有以下特点：

## 添加的功能：

1. **导入语句**：添加了对 `EditNotAllowedException` 的导入
2. **异常处理方法**：`handleEditNotAllowedException` 方法处理编辑不允许异常

## 处理特性：

1. **日志记录**：使用 `warn` 级别记录异常，包含详细的资源信息：
   - 追踪ID
   - 请求详情
   - 资源类型、资源ID、当前状态

2. **响应区分**：
   - **Ajax请求**：返回包含错误消息和追踪ID的 JSON 响应
   - **页面请求**：跳转到错误页面并显示错误消息

3. **追踪支持**：每个异常都生成唯一的追踪ID，便于问题定位和排查

4. **丰富的上下文信息**：在日志中记录异常的特有属性（资源类型、资源ID、当前状态），这对于诊断编辑权限问题非常有帮助

这样的设计遵循了项目的异常处理规范，与其他异常处理器保持一致的风格和功能，同时针对 `EditNotAllowedException` 的特殊属性提供了更详细的日志记录。

---

