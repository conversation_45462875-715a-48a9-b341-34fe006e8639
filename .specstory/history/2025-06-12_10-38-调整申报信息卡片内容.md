<!-- Generated by SpecStory -->

# 调整申报信息卡片内容 (2025-06-12 19:38:46)

_**User**_

调整`申报信息`卡片：
1. 移除申报状态、创建时间、提交时间、审核时间，因为这些信息在申报进度卡片内也有
2. 添加病种的详细信息，示例数据如下：
```
  {
    "name": "慢性肾炎",
    "insurance_city_worker": 1,
    "insurance_city_resident": 0,
    "insurance_district": 1,
    "insurance_corp": 1,
    "criteria": "1.有半年肾炎病史，病程迁延并缓慢进展；\r\n2.尿液化验异常（尿蛋白+-++++，24小时尿蛋白定量>1g，肉眼血尿或镜下血尿，多形型尿红细胞>0.8万/ml、或伴管型尿），尿酶异常（包括尿nNAG酶、尿β2－微球蛋白、尿α2－微球蛋白、尿视黄结合蛋白）；\r\n3.经肾穿刺活检术，明确肾脏病理诊断。\r\n同时具备以上第1、2条或具备以上第3条的，即符合认定标准。",
    "documents": "1.近一年病史资料；\r\n2.尿常规、尿蛋白定量检查报告单；\r\n3.肾功能检查报告单；\r\n4.肾穿刺活检病理报告单，明确诊断的无须提供病史。"
  }
```

---

_**Assistant**_



---

我来分析一下需要调整的申报信息卡片，然后按照您的要求进行修改。

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---

让我找到申报信息卡片的具体位置：

---

<details>
            <summary>Grep search for "申报信息" • **5** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html` | L894 | `<!-- 申报信息 -->` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html` | L897 | `<i class="fa fa-file-text"></i> 申报信息` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/upload_attachments.html` | L281 | `<!-- 申报信息摘要 -->` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/confirm_submission.html` | L5 | `<title>申报信息确认</title>` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/confirm_submission.html` | L252 | `<h1 class="text-lg font-medium flex-1 text-center">申报信息确认</h1>` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/confirm_submission.html` | L283 | `<span>请仔细核对以下申报信息，确认无误后提交</span>` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/index.html` | L181 | `<!-- 申报信息确认 -->` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/index.html` | L184 | `<span>申报信息确认</span>` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/index.html` | L190 | `<div class="wechat-title">申报信息确认</div>` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/design/chronic-disease-registration/prototypes/index.html` | L193 | `<iframe src="confirm_submission.html" class="prototype-frame" title="申报信息确认"></iframe>` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html` | L894 | `<!-- 申报信息 -->` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html` | L897 | `<i class="fa fa-file-text"></i> 申报信息` |

</details>

---

现在让我读取申报信息卡片的具体内容：

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---

现在我需要在CSS中为病种详细信息添加一些样式，然后修改申报信息卡片的内容。让我先添加相关样式：

---



---

现在修改申报信息卡片的内容，移除时间和状态信息，添加病种详细信息：

---



---

现在让我检查一下修改后的效果，确保代码符合要求并且语法正确：

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---



---

好的！我已经成功调整了申报信息卡片。让我总结一下所做的修改：

## 修改总结

### 1. 移除的内容
- ✅ 申报状态（现在在申报进度卡片中显示）
- ✅ 创建时间（现在在申报进度卡片中显示）
- ✅ 提交时间（现在在申报进度卡片中显示）
- ✅ 审核时间（现在在申报进度卡片中显示）

### 2. 添加的病种详细信息
- ✅ **医保支持情况**：以卡片形式展示四种医保类型的支持状态
  - 乌鲁木齐市职工医保
  - 乌鲁木齐市居民医保  
  - 区医保（含铁路医保）
  - 兵团医保
- ✅ **认定标准**：显示病种的详细认定标准
- ✅ **认定资料要求**：显示需要提供的认定资料
- ✅ **鉴定专家**：如果有的话，显示负责的鉴定专家

### 3. 新增样式特性
- 医保支持情况使用不同颜色标识（支持=蓝色，不支持=红色）
- 认定标准和资料要求使用独立的内容区域，支持换行显示
- 响应式设计，在小屏幕上优化显示效果
- 使用图标增强视觉效果

### 4. 保留的内容
- ✅ 申报病种名称（基本信息）
- ✅ 驳回信息（如果状态为已驳回）

现在申报信息卡片更加专注于病种相关的详细信息，而申报状态和时间信息则集中在右侧的申报进度卡片中显示，避免了信息重复，提升了用户体验。

---

_**User**_

为本次改动编写提交信息

---

_**Assistant**_



---

### Rule: git-commit

**commit message**

```markdown
# Conventional Commits 规范 AI 提示词

请根据 @Conventional Commits 1.0.0 规范，为以下代码变更生成规范化的 git 提交信息。要求如下：

1. **类型**（type）必须是规范中的类型，如：feat、fix、docs、style、refactor、test、chore、build、ci 等。
2. **可选范围**（scope）可加可不加，若加需用小括号包裹，如 feat(parser):。
3. **描述**（description）需简明扼要，说明本次更改的主要内容，首字母小写，结尾不加句号。
4. 如有**详细说明**，可在描述后空一行补充正文（body）。
5. 如有**BREAKING CHANGE**，需在类型后加 ! 或在 footer 处注明 BREAKING CHANGE: 详细说明。
6. 如有**关联 issue**，可在 footer 处注明 Refs: #123 或 Closes: #123。
7. 提交信息优先使用中文，但如果是技术术语或专有名词，请使用英文。

请严格按照如下格式输出：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**示例：**

```
feat(parser): add ability to parse arrays

support parsing of nested arrays and edge cases

Refs: #123
```

---

**请用上述格式为以下变更生成提交信息：**

【在此处补充你的代码变更内容或描述】```

---

根据Conventional Commits规范，为本次改动编写提交信息：

```
feat(chronic-disease): 优化申报信息卡片显示内容

- 移除申报状态、创建时间、提交时间、审核时间等重复信息
- 新增病种详细信息展示，包括医保支持情况、认定标准、认定资料要求
- 医保支持情况使用颜色标识支持状态，增强用户体验
- 添加响应式样式支持，优化移动端显示效果
- 避免信息重复，申报进度信息统一在右侧进度卡片中显示

该改动提升了申报详情页面的信息组织结构，使病种相关信息更加集中和直观。
```

---

