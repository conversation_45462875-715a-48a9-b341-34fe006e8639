<!-- Generated by SpecStory -->

# 更新 alipay.yaml 配置文件 (2025-06-26 08:39:34)

_**User**_

根据 @alipay.setting 更新 @alipay.yaml 

---

_**Assistant**_



---

我需要比较两个文件的差异，然后根据 `alipay.setting` 的内容来更新 `alipay.yaml` 文件。

---



---



---



---

已完成根据 `alipay.setting` 文件更新 `alipay.yaml` 文件的操作。主要更新了以下内容：

## 更新内容

1. **ma 配置部分**:
   - `merchantName`: 从 "新疆维吾尔自治区儿童医院" 更新为 "新疆医科大学第六附属医院"
   - `notifyUrl`: 从 "https://zy.xj-etyy.com:8888/open/alipay/onPay" 更新为 "https://ykdlfy.xjyqtl.cn:8888/open/alipay/onPay"
   - `hospitalName`: 从 "新疆维吾尔自治区儿童医院" 更新为 "新疆医科大学第六附属医院"

2. **mip 配置部分**:
   - `orgName`: 从 "新疆维吾尔自治区儿童医院" 更新为 "新疆医科大学第六附属医院"
   - `notifyUrl`: 从 "https://zy.xj-etyy.com:8888/open/alipay/onMipPay" 更新为 "https://ykdlfy.xjyqtl.cn:8888/open/alipay/onMipPay"

所有的医院名称和域名已经与 `alipay.setting` 文件保持一致。

---

_**User**_

更新 @alipay.yaml ，所有属性值都使用双引号引起来

---

_**Assistant**_



---

我需要将 `alipay.yaml` 文件中的所有属性值都用双引号引起来。让我先查看当前文件内容，然后进行更新。

---

Read file: ruoyi-admin/src/main/resources/alipay.yaml

---

现在我将更新整个文件，为所有属性值加上双引号：

---

```diff
  ma:
-   appId: 2021003144629326
-   merchantName: 新疆医科大学第六附属医院
+   appId: "2021003144629326"
+   merchantName: "新疆医科大学第六附属医院"
    # 登录https://open.alipay.com/，点击右上角头像，选择账户中心，账户管理中的账号ID即merchantId
-   merchantId: 2088341690455593
-   pid: 2088341243702554
-   encryptType: AES
-   encryptKey: x8lw+OAzbcNDOwqhiL121w==
-   notifyUrl: https://ykdlfy.xjyqtl.cn:8888/open/alipay/onPay
+   merchantId: "2088341690455593"
+   pid: "2088341243702554"
+   encryptType: "AES"
+   encryptKey: "x8lw+OAzbcNDOwqhiL121w=="
+   notifyUrl: "https://ykdlfy.xjyqtl.cn:8888/open/alipay/onPay"
    # 关于密钥和证书的文档：
    # https://opendocs.alipay.com/open/291/105971
    # https://opendocs.alipay.com/open/291/105974
    # 应用私钥（private key）
-   merchantPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCGvuUf0HVLvMu8VB9to7wJj2vO8if5jAzgTZ+qAsZmJ6INWBc6Tf14e1BWHTMJbFPa+pTZmzMJ7FdV+jUPwg5c+DPUvQaLCm3KkdYq4MNid2RtOW8NrExSdupGwkSshf3tHePFT4VwYmvI+q4JWiPGWpFKkybsE0gnhuhnZusWeNg0R/PWq5bYMYlE5ux797pFoBHZRlD0O0VPPcL9m5b0eEPoHcQ4hnj4H71g/pRLXrMTQ9iDIcaxe3A/iiw/dcL90AMwkTayLCrCHzpVTyVT9BXb0dkkCg3WMIHcS+jEUljcovJ5mXNSlJ/BxWRYGXAm791dkktgPFNOYj/0VyGJAgMBAAECggEAelbyEzuic9x7oodCbbp+rLNyL9f/YXy96B2yPJr+ZZvQb4sus7pBy9UycK1cPE8Fp8lhwl0+LuFNvntJGACaJ+EWti3VtgIHAyf8KrwdydYa8Wx3HxPzy9+5//q8fWO+1D4SIoB76DkEIdswjr8vwhVS20HV0fwDxBeiBNgV1DUOhTKzEH6vgNLq6e04rNsPHn3jGDVSBzhuH5aFgtstVKq7HVgqVvVXe0IUDmBLUEUYKofE5xOhc21MGgIm+F1HUOtLbQnw4to8+706pm2lMGNdLIMgKcEzbdSzdQrzJUCTholCtAWlAwAeESMdWKuhafLso9E+PkyevgY14vUVrQKBgQD4XibevJIXgC5z8aWTNeOp066ON2odhGPh/LAdBoLknt7pM1XCwCDqYv97BWuAOARg9MjZroelMba8+yQT9CCUSyx4xHgZpHxsOMI7/1FCLjM/V5NjUikW3468pe3PPQmj2m2vPlnMYHW978oXCuh6/syRbEFZw5mEH4kH8pHadwKBgQCK4uf33NcVMNv44QmAkhacfuFi+jTIyxh2kQzUCpDUPLkM4sJAKJyiF0SvernbPnkaFUp/fLvb0H5a9gkZxr5GsxM1t5FKQjeTzC5x+GyJHO5hNeKEYAMuHVA5JudBoR3CFBtHrKxPnEw3Fz96fm1J2o/DiO8m9CvWZ19UoSHj/wKBgQCPYXs7Fs2X943ZrWUSyQd3IRciTudZxvYKnl/lQb8K0JSLN/GIjnXyGdi3Ynee+7e7aVUSJel0+nAuRSWcx1CPi55O0c0rsIOLznCDRm/Yrucy6BHUQrgp1kGGspvJCcmo6A5uHqakkAMo9WdLSibP6oyaX06SzZKwbawkBGsjHQKBgBJp7RlmtTmBBdkkdO8P72GGTPmjdLYefzrRFmNSl0scVeiIc/qvp7yx2xBs9BFGYiX8JqlqbznAldAXiLNbL0UOXwCjOuceimHVE19W8YToSg6nZ0XnuOQ3TLiXGNgRtXFS2x8QnLPdZBl1wpvKAVFmYeMp15NRkjJV+ESvLGAfAoGBAMUOa1F3043Lil5WL++b+/DcahyLL/SAxU+qTXTaETKRxwcgdGKmle5ibaOYSSKqkyTT/8HGOIAiVTVN1+0GfjlqIn93IICBUuEoeiSK+kL7sN66lk8W0S6LJVe7pRz4s631XbBzPj9CGhAIJlumuPFGV89Fc7+83D6twz1zJHJv
+   merchantPrivateKey: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCGvuUf0HVLvMu8VB9to7wJj2vO8if5jAzgTZ+qAsZmJ6INWBc6Tf14e1BWHTMJbFPa+pTZmzMJ7FdV+jUPwg5c+DPUvQaLCm3KkdYq4MNid2RtOW8NrExSdupGwkSshf3tHePFT4VwYmvI+q4JWiPGWpFKkybsE0gnhuhnZusWeNg0R/PWq5bYMYlE5ux797pFoBHZRlD0O0VPPcL9m5b0eEPoHcQ4hnj4H71g/pRLXrMTQ9iDIcaxe3A/iiw/dcL90AMwkTayLCrCHzpVTyVT9BXb0dkkCg3WMIHcS+jEUljcovJ5mXNSlJ/BxWRYGXAm791dkktgPFNOYj/0VyGJAgMBAAECggEAelbyEzuic9x7oodCbbp+rLNyL9f/YXy96B2yPJr+ZZvQb4sus7pBy9UycK1cPE8Fp8lhwl0+LuFNvntJGACaJ+EWti3VtgIHAyf8KrwdydYa8Wx3HxPzy9+5//q8fWO+1D4SIoB76DkEIdswjr8vwhVS20HV0fwDxBeiBNgV1DUOhTKzEH6vgNLq6e04rNsPHn3jGDVSBzhuH5aFgtstVKq7HVgqVvVXe0IUDmBLUEUYKofE5xOhc21MGgIm+F1HUOtLbQnw4to8+706pm2lMGNdLIMgKcEzbdSzdQrzJUCTholCtAWlAwAeESMdWKuhafLso9E+PkyevgY14vUVrQKBgQD4XibevJIXgC5z8aWTNeOp066ON2odhGPh/LAdBoLknt7pM1XCwCDqYv97BWuAOARg9MjZroelMba8+yQT9CCUSyx4xHgZpHxsOMI7/1FCLjM/V5NjUikW3468pe3PPQmj2m2vPlnMYHW978oXCuh6/syRbEFZw5mEH4kH8pHadwKBgQCK4uf33NcVMNv44QmAkhacfuFi+jTIyxh2kQzUCpDUPLkM4sJAKJyiF0SvernbPnkaFUp/fLvb0H5a9gkZxr5GsxM1t5FKQjeTzC5x+GyJHO5hNeKEYAMuHVA5JudBoR3CFBtHrKxPnEw3Fz96fm1J2o/DiO8m9CvWZ19UoSHj/wKBgQCPYXs7Fs2X943ZrWUSyQd3IRciTudZxvYKnl/lQb8K0JSLN/GIjnXyGdi3Ynee+7e7aVUSJel0+nAuRSWcx1CPi55O0c0rsIOLznCDRm/Yrucy6BHUQrgp1kGGspvJCcmo6A5uHqakkAMo9WdLSibP6oyaX06SzZKwbawkBGsjHQKBgBJp7RlmtTmBBdkkdO8P72GGTPmjdLYefzrRFmNSl0scVeiIc/qvp7yx2xBs9BFGYiX8JqlqbznAldAXiLNbL0UOXwCjOuceimHVE19W8YToSg6nZ0XnuOQ3TLiXGNgRtXFS2x8QnLPdZBl1wpvKAVFmYeMp15NRkjJV+ESvLGAfAoGBAMUOa1F3043Lil5WL++b+/DcahyLL/SAxU+qTXTaETKRxwcgdGKmle5ibaOYSSKqkyTT/8HGOIAiVTVN1+0GfjlqIn93IICBUuEoeiSK+kL7sN66lk8W0S6LJVe7pRz4s631XbBzPj9CGhAIJlumuPFGV89Fc7+83D6twz1zJHJv"
    # 应用公钥证书（public key cert）
-   merchantCertPath: D:\\zyxcx\\cert\\appCertPublicKey_2021003144629326.cer
+   merchantCertPath: "D:\\zyxcx\\cert\\appCertPublicKey_2021003144629326.cer"
    # 支付宝公钥证书（alipay public key cert）
-   alipayCertPath: D:\\zyxcx\\cert\\alipayCertPublicKey_RSA2.cer
+   alipayCertPath: "D:\\zyxcx\\cert\\alipayCertPublicKey_RSA2.cer"
    # 支付宝根证书（alipay root cert）
-   alipayRootCertPath: D:\\zyxcx\\cert\\alipayRootCert.cer
-   charset: utf-8
-   signType: RSA2
-   format: json
-   serverUrl: https://openapi.alipay.com/gateway.do
-   protocol: https
-   billDownloadPath: D:\\zyxcx\\alipay_bill\\
+   alipayRootCertPath: "D:\\zyxcx\\cert\\alipayRootCert.cer"
+   charset: "utf-8"
+   signType: "RSA2"
+   format: "json"
+   serverUrl: "https://openapi.alipay.com/gateway.do"
+   protocol: "https"
+   billDownloadPath: "D:\\zyxcx\\alipay_bill\\"
  
    # 以下配置用于支付宝智能消息提醒
-   hospitalRegisterId: 45760785765010211A1001
-   hospitalName: 新疆医科大学第六附属医院
-   tenantAppId: 20220822555900012729
+   hospitalRegisterId: "45760785765010211A1001"
+   hospitalName: "新疆医科大学第六附属医院"
+   tenantAppId: "20220822555900012729"
  
  mip:
    # 定点医疗机构应用ID，即医药机构渠道编号，参见反馈单中的渠道编号
-   orgAppId: 1GFB051OR03H75430B0A0000BB50C065
+   orgAppId: "1GFB051OR03H75430B0A0000BB50C065"
    # 机构渠道认证编码，参见反馈单中的机构渠道认证编码
-   orgChnlCrtfCode: BqK1kMStlhVDgN2uHf4EsP9BQzovPtfTZGwRsyhqMMbq4tTekl4i8uxRHhJzeiuw
+   orgChnlCrtfCode: "BqK1kMStlhVDgN2uHf4EsP9BQzovPtfTZGwRsyhqMMbq4tTekl4i8uxRHhJzeiuw"
    # 定点医药机构编码，参见反馈单中的定点医药机构编码
-   orgCode: H65010200028
-   orgName: 新疆医科大学第六附属医院
-   orgCityCode: 650100
+   orgCode: "H65010200028"
+   orgName: "新疆医科大学第六附属医院"
+   orgCityCode: "650100"
    # 授权成功后回调医药机构前端地址
-   callUrl: alipays://platformapi/startapp?appId=2021003144629326&page=pages_yibao%2FAlipayPendingSettlement%2FAlipayPendingSettlement
-   notifyUrl: https://ykdlfy.xjyqtl.cn:8888/open/alipay/onMipPay
+   callUrl: "alipays://platformapi/startapp?appId=2021003144629326&page=pages_yibao%2FAlipayPendingSettlement%2FAlipayPendingSettlement"
+   notifyUrl: "https://ykdlfy.xjyqtl.cn:8888/open/alipay/onMipPay"
  
```

---

已成功更新 `alipay.yaml` 文件，为所有属性值都加上了双引号。

## 更新内容总结

已为以下所有属性值添加双引号：

### ma 配置节
- `appId`: "2021003144629326"
- `merchantName`: "新疆医科大学第六附属医院"
- `merchantId`: "2088341690455593"
- `pid`: "2088341243702554"
- `encryptType`: "AES"
- `encryptKey`: "x8lw+OAzbcNDOwqhiL121w=="
- `notifyUrl`: "https://ykdlfy.xjyqtl.cn:8888/open/alipay/onPay"
- `merchantPrivateKey`: (长字符串私钥)
- `merchantCertPath`: "D:\\zyxcx\\cert\\appCertPublicKey_2021003144629326.cer"
- `alipayCertPath`: "D:\\zyxcx\\cert\\alipayCertPublicKey_RSA2.cer"
- `alipayRootCertPath`: "D:\\zyxcx\\cert\\alipayRootCert.cer"
- `charset`: "utf-8"
- `signType`: "RSA2"
- `format`: "json"
- `serverUrl`: "https://openapi.alipay.com/gateway.do"
- `protocol`: "https"
- `billDownloadPath`: "D:\\zyxcx\\alipay_bill\\"
- `hospitalRegisterId`: "45760785765010211A1001"
- `hospitalName`: "新疆医科大学第六附属医院"
- `tenantAppId`: "20220822555900012729"

### mip 配置节
- `orgAppId`: "1GFB051OR03H75430B0A0000BB50C065"
- `orgChnlCrtfCode`: "BqK1kMStlhVDgN2uHf4EsP9BQzovPtfTZGwRsyhqMMbq4tTekl4i8uxRHhJzeiuw"
- `orgCode`: "H65010200028"
- `orgName`: "新疆医科大学第六附属医院"
- `orgCityCode`: "650100"
- `callUrl`: "alipays://platformapi/startapp?appId=2021003144629326&page=pages_yibao%2FAlipayPendingSettlement%2FAlipayPendingSettlement"
- `notifyUrl`: "https://ykdlfy.xjyqtl.cn:8888/open/alipay/onMipPay"

现在所有的配置值都被双引号包围，确保了YAML文件的格式一致性和字符串安全性。

---

_**User**_

根据 @weicity.setting 更新 @wecity-mip.yaml 

---

_**Assistant**_



---

