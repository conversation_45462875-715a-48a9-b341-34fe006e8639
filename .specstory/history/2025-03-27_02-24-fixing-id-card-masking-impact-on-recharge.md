<!-- Generated by SpecStory -->

# Fixing ID Card Masking Impact on Recharge (2025-03-27 11:24:53)

_**User**_

注意 `maskSensitive` 方法，它会影响 `canUseRecharge`，原因是调用这个方法后， `this.idCardNo` 必定是无效的身份证号，导致 `canUseRecharge` 必定返回 `false`。请修复这个问题。

---

_**Assistant**_

Let me analyze this issue and propose a solution.

The problem is that `maskSensitive()` method masks the ID card number using `StringKit.INSTANCE.hideIdCardNo()`, which makes it invalid for ID card validation. However, we still need to be able to check if a patient can use recharge functionality even after masking sensitive information.

Let me search for any related code first.

---

<details>
            <summary>Grep search for "hideIdCardNo" • **7** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L246 | `it.patientIdCard = StringKit.hideIdCardNo(it.patientIdCard)` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/AlipayController.kt` | L90 | `"certNo" to AlipayService.hideIdCardNo(infoShareResponse?.certNo.orEmpty())` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L190 | `"patientIdCardNo" to StringKit.hideIdCardNo(patientInfoToken.patientIdCardNo),` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L193 | `"patientCardNo" to StringKit.hideIdCardNo(patientInfoToken.patientCardNo),` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/domain/Patient.java` | L182 | `this.idCardNo = StringKit.INSTANCE.hideIdCardNo(this.idCardNo);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxTransfer.java` | L97 | `this.userIdCardNo = StringKit.INSTANCE.hideIdCardNo(this.userIdCardNo);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxTransfer.java` | L100 | `this.patientIdCardNo = StringKit.INSTANCE.hideIdCardNo(this.patientIdCardNo);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxTransfer.java` | L103 | `this.patientCardNo = StringKit.INSTANCE.hideIdCardNo(this.patientCardNo);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/AlipayUserInfoServiceImpl.java` | L109 | `if (StrUtil.equals(userInfo.getIdCardNo(), AlipayService.INSTANCE.hideIdCardNo(alipayUserInfo.getCertNo()))) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/domain/Patient.java` | L182 | `this.idCardNo = StringKit.INSTANCE.hideIdCardNo(this.idCardNo);` |

</details>

---

I see that the ID card masking is used in multiple places for security purposes. To fix this issue, we need to modify the `Patient` class to store both the masked and original ID card number, so we can use the original one for validation while still masking sensitive data in responses.

Here's how I'll modify the code:

---

```diff
  
  import com.fasterxml.jackson.annotation.JsonGetter;
+ import com.fasterxml.jackson.annotation.JsonIgnore;
  import com.ruoyi.common.annotation.Excel;
  import com.ruoyi.common.core.domain.BaseEntity;
```

```diff
      @Serial
      private static final long serialVersionUID = 1L;
+ 
+     /**
+      * 原始身份证号（用于验证），不会被序列化
+      */
+     @JsonIgnore
+     private String originalIdCardNo;
  
      /**
```

```diff
  
      public Patient maskSensitive() {
+         if (this.originalIdCardNo == null) {
+             this.originalIdCardNo = this.idCardNo;
+         }
          this.idCardNo = StringKit.INSTANCE.hideIdCardNo(this.idCardNo);
          this.mobile = StringKit.INSTANCE.hideMobile(this.mobile);
```

```diff
      @JsonGetter("canUseRecharge")
      public boolean canUseRecharge() {
+         String idCardToCheck = originalIdCardNo != null ? originalIdCardNo : idCardNo;
+         
          // 1. 检查身份证号是否有效
-         if (!IdcardUtil.isValidCard(this.idCardNo)) {
+         if (!IdcardUtil.isValidCard(idCardToCheck)) {
              return false;
          }
  
          // 2. 获取年龄
-         int age = IdcardUtil.getAge(this.idCardNo);
+         int age = IdcardUtil.getAge(idCardToCheck);
  
          // 3. 判断年龄是否符合条件（14岁以下或65岁以上）
```

---

I've made the following changes to fix the issue:

1. Added a new field `originalIdCardNo` with `@JsonIgnore` annotation to store the original ID card number without exposing it in JSON responses
2. Modified `maskSensitive()` to store the original ID card number before masking
3. Updated `canUseRecharge()` to use the original ID card number for validation if available

These changes ensure that:
1. The ID card number is still masked in responses for security
2. The `canUseRecharge()` functionality works correctly even after masking
3. The original ID card number is not exposed in JSON responses due to `@JsonIgnore`

English explanation:
This solution maintains data security while preserving the validation functionality by keeping track of both the masked and original ID card numbers internally. The original number is used only for validation purposes and is never exposed externally.

中文说明：
这个解决方案通过在内部同时保存掩码后和原始的身份证号来维护数据安全性的同时保留验证功能。原始号码仅用于验证目的，永远不会对外暴露。

主要修改包括：
1. 添加了一个新的字段 `originalIdCardNo` 来存储原始身份证号，并使用 `@JsonIgnore` 注解确保不会在 JSON 响应中暴露
2. 修改了 `maskSensitive()` 方法，在掩码之前保存原始身份证号
3. 更新了 `canUseRecharge()` 方法，优先使用原始身份证号进行验证

这样既保证了数据安全性，又确保了充值验证功能的正常工作。

---

_**User**_

编写 commit message

---

_**Assistant**_

我会帮你编写一个符合最佳实践的 commit message：

```
fix: 修复 Patient.maskSensitive 方法对 canUseRecharge 的影响

- 添加 originalIdCardNo 字段用于存储原始身份证号
- 在 maskSensitive 方法中保存原始身份证号
- 修改 canUseRecharge 方法优先使用原始身份证号进行验证
- 使用 @JsonIgnore 确保原始身份证号不会在 JSON 响应中暴露

问题描述：
maskSensitive 方法会将身份证号码进行掩码处理，导致 canUseRecharge 方法
无法正确验证身份证号的有效性和年龄计算。通过保存原始身份证号解决此问题，
同时保证了数据安全性。
```

这个 commit message 包含了：
1. 类型前缀：`fix`
2. 简短描述：说明修复了什么问题
3. 具体改动：使用列表形式列出主要改动
4. 问题描述：详细说明问题的原因和解决方案

这样的 commit message 能让其他开发者快速理解这次改动的目的和具体内容。

---

