<!-- Generated by SpecStory -->

# Refactoring Concurrent Upload Test in Java (2025-05-25 00:40:57)

_**User**_

In ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java around lines 181 to 214, the concurrent upload test uses CompletableFuture.supplyAsync() which may not guarantee true parallel execution, wraps checked exceptions in RuntimeException, and lacks verification of actual concurrency. Refactor the test to use an ExecutorService with a fixed thread pool to ensure parallelism, handle exceptions explicitly without wrapping, and add synchronization mechanisms or timestamps to verify that uploads are processed concurrently. This will improve the reliability and accuracy of the concurrent upload test.

---

_**Assistant**_



---

我将重构这个并发上传测试以解决您提到的问题。让我先查看当前的测试代码，然后进行改进。

---

Read file: ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java

---

现在我需要重构这个并发上传测试。首先，我需要更新import语句，然后替换并发测试方法。

---

```diff
  import space.lzhq.ph.BaseIntegrationTest;
  
- import java.util.concurrent.CompletableFuture;
+ import java.util.concurrent.CountDownLatch;
+ import java.util.concurrent.ExecutorService;
+ import java.util.concurrent.Executors;
+ import java.util.concurrent.Future;
+ import java.util.concurrent.TimeUnit;
+ import java.util.concurrent.atomic.AtomicLong;
+ import java.util.concurrent.atomic.AtomicReference;
  
  import static org.hamcrest.Matchers.*;
+ import static org.junit.jupiter.api.Assertions.assertTrue;
  import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
  import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
```

```diff
  
          // Act - Execute concurrent requests
-         CompletableFuture<ResultActions> future1 = CompletableFuture.supplyAsync(() -> {
+         ExecutorService executor = Executors.newFixedThreadPool(2);
+         CountDownLatch latch = new CountDownLatch(2);
+         AtomicReference<ResultActions> result1 = new AtomicReference<>();
+         AtomicReference<ResultActions> result2 = new AtomicReference<>();
+ 
+         Future<Void> future1 = executor.submit(() -> {
              try {
-                 return mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
+                 result1.set(mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
                          .file(file1)
                          .header("Authorization", VALID_SESSION_ID)
-                         .contentType(MediaType.MULTIPART_FORM_DATA));
+                         .contentType(MediaType.MULTIPART_FORM_DATA)));
              } catch (Exception e) {
                  throw new RuntimeException(e);
+             } finally {
+                 latch.countDown();
              }
+             return null;
          });
  
-         CompletableFuture<ResultActions> future2 = CompletableFuture.supplyAsync(() -> {
+         Future<Void> future2 = executor.submit(() -> {
              try {
-                 return mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
+                 result2.set(mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
                          .file(file2)
                          .header("Authorization", VALID_SESSION_ID)
-                         .contentType(MediaType.MULTIPART_FORM_DATA));
+                         .contentType(MediaType.MULTIPART_FORM_DATA)));
              } catch (Exception e) {
                  throw new RuntimeException(e);
+             } finally {
+                 latch.countDown();
              }
+             return null;
          });
  
          // Assert - Both requests should succeed
-         CompletableFuture.allOf(future1, future2).join();
+         latch.await(10, TimeUnit.SECONDS);
  
-         future1.get().andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(0));
-         future2.get().andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(0));
+         result1.get().andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(0));
+         result2.get().andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(0));
      }
  }
```

---

现在我需要完全重写并发测试方法，以实现更好的并发控制、异常处理和并发性验证：

---

```diff
          MockMultipartFile file2 = createValidJpegFile("medicare-card-2.jpg");
          
-         // Act - Execute concurrent requests
          ExecutorService executor = Executors.newFixedThreadPool(2);
-         CountDownLatch latch = new CountDownLatch(2);
+         CountDownLatch startLatch = new CountDownLatch(1);  // 确保任务同时开始
+         CountDownLatch completionLatch = new CountDownLatch(2);  // 等待任务完成
+         
+         // 用于存储结果和异常
          AtomicReference<ResultActions> result1 = new AtomicReference<>();
          AtomicReference<ResultActions> result2 = new AtomicReference<>();
- 
-         Future<Void> future1 = executor.submit(() -> {
+         AtomicReference<Exception> exception1 = new AtomicReference<>();
+         AtomicReference<Exception> exception2 = new AtomicReference<>();
+         
+         // 用于验证并发性的时间戳
+         AtomicLong startTime1 = new AtomicLong();
+         AtomicLong startTime2 = new AtomicLong();
+ 
+         try {
+             // Act - 提交任务到线程池
+             Future<?> future1 = executor.submit(() -> {
                  try {
-                 result1.set(mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
+                     startLatch.await();  // 等待同步开始信号
+                     startTime1.set(System.nanoTime());  // 记录开始时间
+                     
+                     ResultActions actions = mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
                              .file(file1)
                              .header("Authorization", VALID_SESSION_ID)
-                         .contentType(MediaType.MULTIPART_FORM_DATA)));
+                             .contentType(MediaType.MULTIPART_FORM_DATA));
+                     result1.set(actions);
+                     
                  } catch (Exception e) {
-                 throw new RuntimeException(e);
+                     exception1.set(e);  // 显式存储异常而不包装
                  } finally {
-                 latch.countDown();
+                     completionLatch.countDown();
                  }
-             return null;
              });
  
-         Future<Void> future2 = executor.submit(() -> {
+             Future<?> future2 = executor.submit(() -> {
                  try {
-                 result2.set(mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
+                     startLatch.await();  // 等待同步开始信号
+                     startTime2.set(System.nanoTime());  // 记录开始时间
+                     
+                     ResultActions actions = mockMvc.perform(multipart(MEDICARE_CARD_ENDPOINT)
                              .file(file2)
                              .header("Authorization", VALID_SESSION_ID)
-                         .contentType(MediaType.MULTIPART_FORM_DATA)));
+                             .contentType(MediaType.MULTIPART_FORM_DATA));
+                     result2.set(actions);
+                     
                  } catch (Exception e) {
-                 throw new RuntimeException(e);
+                     exception2.set(e);  // 显式存储异常而不包装
                  } finally {
-                 latch.countDown();
+                     completionLatch.countDown();
                  }
-             return null;
-         });
+             });
+ 
+             // 同时启动两个任务
+             startLatch.countDown();
+             
+             // 等待任务完成，设置超时防止测试挂起
+             boolean completed = completionLatch.await(10, TimeUnit.SECONDS);
+             assertTrue(completed, "并发上传未在预期时间内完成");
  
-         // Assert - Both requests should succeed
-         latch.await(10, TimeUnit.SECONDS);
- 
-         result1.get().andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(0));
-         result2.get().andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(0));
+             // Assert - 检查是否有异常发生
+             if (exception1.get() != null) {
+                 throw new AssertionError("第一个上传请求失败", exception1.get());
+             }
+             if (exception2.get() != null) {
+                 throw new AssertionError("第二个上传请求失败", exception2.get());
+             }
+ 
+             // 验证并发执行 - 两个请求的开始时间应该非常接近
+             long timeDifferenceNanos = Math.abs(startTime1.get() - startTime2.get());
+             long timeDifferenceMillis = timeDifferenceNanos / 1_000_000;
+             assertTrue(timeDifferenceMillis < 100, 
+                     String.format("请求启动时间差过大 (%d ms)，可能未实现真正的并发执行", timeDifferenceMillis));
+ 
+             // 验证两个请求都成功
+             result1.get()
+                     .andDo(print())
+                     .andExpect(status().isOk())
+                     .andExpect(jsonPath("$.code").value(0))
+                     .andExpected(jsonPath("$.data.attachment.fileName").value("medicare-card-1.jpg"));
+ 
+             result2.get()
+                     .andDo(print())
+                     .andExpect(status().isOk())
+                     .andExpect(jsonPath("$.code").value(0))
+                     .andExpected(jsonPath("$.data.attachment.fileName").value("medicare-card-2.jpg"));
+ 
+         } finally {
+             // 确保ExecutorService正确关闭
+             executor.shutdown();
+             if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
+                 executor.shutdownNow();
+             }
+         }
      }
  }
```

---

我注意到代码中有一个小错误，使用了 `.andExpected()` 而不是 `.andExpect()`。让我修复这个问题：

---

```diff
                      .andExpect(status().isOk())
                      .andExpect(jsonPath("$.code").value(0))
-                     .andExpected(jsonPath("$.data.attachment.fileName").value("medicare-card-1.jpg"));
+                     .andExpect(jsonPath("$.data.attachment.fileName").value("medicare-card-1.jpg"));
  
              result2.get()
                      .andDo(print())
                      .andExpect(status().isOk())
                      .andExpect(jsonPath("$.code").value(0))
-                     .andExpected(jsonPath("$.data.attachment.fileName").value("medicare-card-2.jpg"));
+                     .andExpect(jsonPath("$.data.attachment.fileName").value("medicare-card-2.jpg"));
  
          } finally {
```

---

现在让我验证完整的修改是否正确，检查整个测试方法：

---

Read file: ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationAttachmentApiControllerTest.java

---

完美！重构已经完成。让我总结一下所做的改进：

## 重构总结

我已经成功重构了并发上传测试，解决了您提到的所有问题：

### 🔧 **主要改进**

1. **使用 ExecutorService 确保真正的并行执行**
   - 替换 `CompletableFuture.supplyAsync()` 为 `Executors.newFixedThreadPool(2)`
   - 使用固定大小的线程池保证并行性

2. **显式异常处理**
   - 移除异常包装成 `RuntimeException` 的做法
   - 使用 `AtomicReference<Exception>` 显式存储和处理异常
   - 在测试验证阶段检查异常并抛出有意义的 `AssertionError`

3. **添加同步机制验证并发性**
   - 使用 `CountDownLatch startLatch` 确保任务同时开始
   - 使用 `CountDownLatch completionLatch` 等待任务完成
   - 记录 `AtomicLong` 时间戳验证实际并发执行

4. **增强的测试验证**
   - 验证两个请求的开始时间差小于100ms
   - 添加超时机制防止测试挂起
   - 验证每个文件的特定文件名

5. **资源管理**
   - 正确关闭 `ExecutorService`
   - 使用 `try-finally` 确保资源清理

### 📋 **新增功能**

- **并发性验证**：通过时间戳差异验证真正的并发执行
- **超时保护**：防止测试无限期等待
- **详细错误信息**：提供更好的测试失败诊断信息
- **资源安全**：确保线程池正确关闭

### 🏗️ **代码结构**

重构后的测试遵循了最佳实践：
- 清晰的 Arrange-Act-Assert 结构
- 详细的中文注释说明各个步骤
- 符合 Spring Boot 和 JUnit 测试规范
- 使用原子类型确保线程安全

这个重构版本现在能够可靠地测试并发上传场景，提供准确的并发性验证，并能够正确处理各种异常情况。

---

