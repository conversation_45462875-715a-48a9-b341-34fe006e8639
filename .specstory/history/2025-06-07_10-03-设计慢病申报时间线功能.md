<!-- Generated by SpecStory -->

# 设计慢病申报时间线功能 (2025-06-07 19:03:25)

_**User**_

为慢病申报业务设计时间线功能，以便直观地展示出各关键节点的操作时间、操作描述、状态，方便掌握慢病申报记录的状态流转情况。先探讨一下设计，不写代码。

---

_**Assistant**_



---

我来分析一下慢病申报业务的时间线功能设计。

## 业务流程分析

从现有代码看，慢病申报有以下关键状态流转：
1. **DRAFT（草稿）** → **SUBMITTED（已提交）** → **APPROVED（已通过）** / **REJECTED（已驳回）**
2. **REJECTED（已驳回）** → **RESUBMITTED（已重新提交）** → **APPROVED（已通过）** / **REJECTED（已驳回）**

## 时间线功能设计思路

### 1. 需要记录的关键节点

**核心状态节点：**
- 创建申报记录（DRAFT）
- 提交申报（SUBMITTED） 
- 审核通过（APPROVED）
- 审核驳回（REJECTED）
- 重新提交（RESUBMITTED）

**操作细节节点：**
- 完善患者信息
- 设置/清除代办人信息  
- 选择慢病病种
- 上传认定资料附件
- 修改各类信息

### 2. 数据结构设计方案

#### 方案一：扩展现有表结构
在 `ChronicDiseaseRegistration` 表中新增时间线相关字段：
```sql
-- 重新提交时间
resubmit_time DATETIME NULL,
-- 驳回时间  
reject_time DATETIME NULL,
-- 最后修改患者信息时间
patient_info_update_time DATETIME NULL,
-- 最后修改代办人信息时间
agent_info_update_time DATETIME NULL,
-- 最后选择病种时间
disease_select_time DATETIME NULL,
-- 最后上传附件时间
document_upload_time DATETIME NULL
```

**优点：** 查询简单，性能好
**缺点：** 无法记录多次操作历史，字段会很多

#### 方案二：新增操作日志表（推荐）
创建 `chronic_disease_registration_timeline` 表：
```sql
CREATE TABLE chronic_disease_registration_timeline (
    id VARCHAR(32) PRIMARY KEY,
    registration_id VARCHAR(32) NOT NULL,
    event_type VARCHAR(50) NOT NULL,  -- 事件类型
    event_description TEXT,           -- 事件描述
    operator_type TINYINT NOT NULL,   -- 操作者类型：1-申请人，2-代办人，3-系统，4-审核员
    operator_name VARCHAR(100),       -- 操作者姓名
    old_status VARCHAR(20),           -- 变更前状态
    new_status VARCHAR(20),           -- 变更后状态
    event_time DATETIME NOT NULL,     -- 事件时间
    additional_data JSON,             -- 额外数据（如驳回原因等）
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 方案三：混合方案（最优）
- 保留现有的关键时间字段（createTime、submitTime、approveTime）
- 新增操作日志表记录详细操作历史
- 在关键状态变更时同时更新主表和日志表

### 3. 事件类型枚举设计

```java
public enum TimelineEventType {
    // 状态变更事件
    REGISTRATION_CREATED("REGISTRATION_CREATED", "创建申报记录"),
    REGISTRATION_SUBMITTED("REGISTRATION_SUBMITTED", "提交申报"),
    REGISTRATION_APPROVED("REGISTRATION_APPROVED", "审核通过"),
    REGISTRATION_REJECTED("REGISTRATION_REJECTED", "审核驳回"),
    REGISTRATION_RESUBMITTED("REGISTRATION_RESUBMITTED", "重新提交"),
    
    // 信息变更事件
    PATIENT_INFO_UPDATED("PATIENT_INFO_UPDATED", "更新患者信息"),
    AGENT_INFO_SET("AGENT_INFO_SET", "设置代办人信息"),
    AGENT_INFO_CLEARED("AGENT_INFO_CLEARED", "清除代办人信息"),
    DISEASE_SELECTED("DISEASE_SELECTED", "选择慢病病种"),
    DOCUMENTS_UPLOADED("DOCUMENTS_UPLOADED", "上传认定资料");
}
```

### 4. 前端展示设计

#### 时间线UI组件结构：
```javascript
// 时间线数据结构
{
  "timeline": [
    {
      "id": "1",
      "eventType": "REGISTRATION_CREATED",
      "eventDescription": "创建慢病申报记录",
      "eventTime": "2024-01-15 10:30:00",
      "operatorType": 1, // 申请人
      "operatorName": "张三",
      "status": "DRAFT",
      "icon": "create",
      "color": "blue"
    },
    {
      "id": "2", 
      "eventType": "PATIENT_INFO_UPDATED",
      "eventDescription": "完善患者基本信息",
      "eventTime": "2024-01-15 11:00:00",
      "operatorType": 1,
      "operatorName": "张三", 
      "icon": "edit",
      "color": "gray"
    }
  ]
}
```

#### 展示效果：
- 垂直时间线布局
- 不同事件类型用不同图标和颜色区分
- 重要状态变更突出显示
- 支持折叠显示详细操作

### 5. API接口设计

```java
@GetMapping("/{id}/timeline")
@Operation(summary = "获取慢病申报时间线")
ResponseDto<List<TimelineEventDto>> getRegistrationTimeline(
    @PathVariable String id,
    @RequestParam(required = false) Boolean includeDetails // 是否包含详细操作
);
```

### 6. 实现考虑点

#### 性能优化：
- 时间线数据按需查询，支持分页
- 重要节点可以缓存
- 考虑数据归档策略

#### 权限控制：
- 申请人和代办人可查看完整时间线
- 审核员可查看审核相关节点
- 管理员可查看所有节点

#### 扩展性：
- 事件类型支持动态扩展
- 支持自定义时间线过滤条件
- 预留对接外部系统的接口

这样的设计既能满足用户查看申报进度的需求，又能为后续的数据分析和系统优化提供数据支撑。你觉得这个设计方向如何？有什么需要调整或补充的地方吗？

---

_**User**_

很好，让我们采用方案三，开始编码

---

_**Assistant**_



---

好的，让我们开始实现方案三的混合方案。我将按照项目架构逐步编码。

## 1. 首先创建时间线事件类型枚举

---

```diff
- 
+ package space.lzhq.ph.enums;
+ 
+ /**
+  * 慢病申报时间线事件类型枚举
+  *
+  * <AUTHOR>
+  */
+ public enum TimelineEventType {
+ 
+     // 状态变更事件
+     REGISTRATION_CREATED("REGISTRATION_CREATED", "创建申报记录"),
+     REGISTRATION_SUBMITTED("REGISTRATION_SUBMITTED", "提交申报"),
+     REGISTRATION_APPROVED("REGISTRATION_APPROVED", "审核通过"),
+     REGISTRATION_REJECTED("REGISTRATION_REJECTED", "审核驳回"),
+     REGISTRATION_RESUBMITTED("REGISTRATION_RESUBMITTED", "重新提交"),
+ 
+     // 信息变更事件
+     PATIENT_INFO_UPDATED("PATIENT_INFO_UPDATED", "更新患者信息"),
+     AGENT_INFO_SET("AGENT_INFO_SET", "设置代办人信息"),
+     AGENT_INFO_CLEARED("AGENT_INFO_CLEARED", "清除代办人信息"),
+     AGENT_INFO_UPDATED("AGENT_INFO_UPDATED", "更新代办人信息"),
+     DISEASE_SELECTED("DISEASE_SELECTED", "选择慢病病种"),
+     DISEASE_UPDATED("DISEASE_UPDATED", "更新慢病病种"),
+     DOCUMENTS_UPLOADED("DOCUMENTS_UPLOADED", "上传认定资料"),
+     DOCUMENTS_UPDATED("DOCUMENTS_UPDATED", "更新认定资料");
+ 
+     private final String code;
+     private final String description;
+ 
+     TimelineEventType(String code, String description) {
+         this.code = code;
+         this.description = description;
+     }
+ 
+     public String getCode() {
+         return code;
+     }
+ 
+     public String getDescription() {
+         return description;
+     }
+ 
+     /**
+      * 判断是否为状态变更事件
+      */
+     public boolean isStatusChangeEvent() {
+         return this == REGISTRATION_CREATED ||
+                this == REGISTRATION_SUBMITTED ||
+                this == REGISTRATION_APPROVED ||
+                this == REGISTRATION_REJECTED ||
+                this == REGISTRATION_RESUBMITTED;
+     }
+ 
+     /**
+      * 判断是否为重要事件（需要突出显示）
+      */
+     public boolean isImportantEvent() {
+         return isStatusChangeEvent();
+     }
+ } 
```

---

## 2. 创建操作者类型枚举

---

```diff
- 
+ package space.lzhq.ph.enums;
+ 
+ /**
+  * 时间线操作者类型枚举
+  *
+  * <AUTHOR>
+  */
+ public enum TimelineOperatorType {
+ 
+     APPLICANT(1, "申请人"),
+     AGENT(2, "代办人"), 
+     SYSTEM(3, "系统"),
+     REVIEWER(4, "审核员"),
+     ADMIN(5, "管理员");
+ 
+     private final Integer code;
+     private final String description;
+ 
+     TimelineOperatorType(Integer code, String description) {
+         this.code = code;
+         this.description = description;
+     }
+ 
+     public Integer getCode() {
+         return code;
+     }
+ 
+     public String getDescription() {
+         return description;
+     }
+ 
+     /**
+      * 根据code获取枚举值
+      */
+     public static TimelineOperatorType fromCode(Integer code) {
+         if (code == null) {
+             return null;
+         }
+         for (TimelineOperatorType type : values()) {
+             if (type.code.equals(code)) {
+                 return type;
+             }
+         }
+         return null;
+     }
+ } 
```

---

## 3. 创建时间线实体类

---

```diff
- 
+ package space.lzhq.ph.domain;
+ 
+ import com.baomidou.mybatisplus.annotation.*;
+ import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
+ import com.fasterxml.jackson.annotation.JsonFormat;
+ import jakarta.validation.constraints.NotBlank;
+ import jakarta.validation.constraints.NotNull;
+ import lombok.Data;
+ import lombok.NoArgsConstructor;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ import space.lzhq.ph.enums.TimelineEventType;
+ import space.lzhq.ph.enums.TimelineOperatorType;
+ 
+ import java.io.Serial;
+ import java.io.Serializable;
+ import java.time.LocalDateTime;
+ import java.util.Map;
+ 
+ /**
+  * 慢病申报时间线对象 chronic_disease_registration_timeline
+  *
+  * <AUTHOR>
+  */
+ @Data
+ @NoArgsConstructor
+ @TableName(value = "chronic_disease_registration_timeline", autoResultMap = true)
+ public class ChronicDiseaseRegistrationTimeline implements Serializable {
+ 
+     @Serial
+     private static final long serialVersionUID = 1L;
+ 
+     /**
+      * 主键
+      */
+     @TableId(type = IdType.ASSIGN_UUID)
+     private String id;
+ 
+     /**
+      * 申报记录ID
+      */
+     @NotBlank(message = "申报记录ID不能为空")
+     private String registrationId;
+ 
+     /**
+      * 事件类型
+      */
+     @NotNull(message = "事件类型不能为空")
+     @EnumValue
+     private TimelineEventType eventType;
+ 
+     /**
+      * 事件描述
+      */
+     private String eventDescription;
+ 
+     /**
+      * 操作者类型
+      */
+     @NotNull(message = "操作者类型不能为空")
+     @EnumValue
+     private TimelineOperatorType operatorType;
+ 
+     /**
+      * 操作者姓名
+      */
+     private String operatorName;
+ 
+     /**
+      * 变更前状态
+      */
+     @EnumValue
+     private ChronicDiseaseRegistrationStatus oldStatus;
+ 
+     /**
+      * 变更后状态
+      */
+     @EnumValue
+     private ChronicDiseaseRegistrationStatus newStatus;
+ 
+     /**
+      * 事件时间
+      */
+     @NotNull(message = "事件时间不能为空")
+     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
+     private LocalDateTime eventTime;
+ 
+     /**
+      * 额外数据（JSON格式）
+      * 如驳回原因、附件信息等
+      */
+     @TableField(typeHandler = JacksonTypeHandler.class)
+     private Map<String, Object> additionalData;
+ 
+     /**
+      * 创建时间
+      */
+     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
+     private LocalDateTime createTime;
+ 
+     /**
+      * 构造函数 - 创建基础时间线事件
+      */
+     public ChronicDiseaseRegistrationTimeline(String registrationId, 
+                                             TimelineEventType eventType, 
+                                             String eventDescription,
+                                             TimelineOperatorType operatorType,
+                                             String operatorName) {
+         this.registrationId = registrationId;
+         this.eventType = eventType;
+         this.eventDescription = eventDescription;
+         this.operatorType = operatorType;
+         this.operatorName = operatorName;
+         this.eventTime = LocalDateTime.now();
+     }
+ 
+     /**
+      * 构造函数 - 创建状态变更时间线事件
+      */
+     public ChronicDiseaseRegistrationTimeline(String registrationId,
+                                             TimelineEventType eventType,
+                                             String eventDescription,
+                                             TimelineOperatorType operatorType,
+                                             String operatorName,
+                                             ChronicDiseaseRegistrationStatus oldStatus,
+                                             ChronicDiseaseRegistrationStatus newStatus) {
+         this(registrationId, eventType, eventDescription, operatorType, operatorName);
+         this.oldStatus = oldStatus;
+         this.newStatus = newStatus;
+     }
+ 
+     /**
+      * 构造函数 - 创建包含额外数据的时间线事件
+      */
+     public ChronicDiseaseRegistrationTimeline(String registrationId,
+                                             TimelineEventType eventType,
+                                             String eventDescription,
+                                             TimelineOperatorType operatorType,
+                                             String operatorName,
+                                             Map<String, Object> additionalData) {
+         this(registrationId, eventType, eventDescription, operatorType, operatorName);
+         this.additionalData = additionalData;
+     }
+ } 
```

---

## 4. 创建时间线响应DTO

---

```diff
- 
+ package space.lzhq.ph.dto.response;
+ 
+ import com.fasterxml.jackson.annotation.JsonFormat;
+ import io.swagger.v3.oas.annotations.media.Schema;
+ import lombok.Data;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ import space.lzhq.ph.enums.TimelineEventType;
+ import space.lzhq.ph.enums.TimelineOperatorType;
+ 
+ import java.time.LocalDateTime;
+ import java.util.Map;
+ 
+ /**
+  * 时间线事件响应DTO
+  *
+  * <AUTHOR>
+  */
+ @Data
+ @Schema(description = "时间线事件信息")
+ public class TimelineEventDto {
+ 
+     @Schema(description = "事件ID")
+     private String id;
+ 
+     @Schema(description = "事件类型")
+     private TimelineEventType eventType;
+ 
+     @Schema(description = "事件类型代码")
+     private String eventTypeCode;
+ 
+     @Schema(description = "事件描述")
+     private String eventDescription;
+ 
+     @Schema(description = "操作者类型")
+     private TimelineOperatorType operatorType;
+ 
+     @Schema(description = "操作者类型代码")
+     private Integer operatorTypeCode;
+ 
+     @Schema(description = "操作者类型描述")
+     private String operatorTypeDescription;
+ 
+     @Schema(description = "操作者姓名")
+     private String operatorName;
+ 
+     @Schema(description = "变更前状态")
+     private ChronicDiseaseRegistrationStatus oldStatus;
+ 
+     @Schema(description = "变更前状态代码")
+     private String oldStatusCode;
+ 
+     @Schema(description = "变更后状态")
+     private ChronicDiseaseRegistrationStatus newStatus;
+ 
+     @Schema(description = "变更后状态代码")
+     private String newStatusCode;
+ 
+     @Schema(description = "事件时间")
+     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
+     private LocalDateTime eventTime;
+ 
+     @Schema(description = "额外数据")
+     private Map<String, Object> additionalData;
+ 
+     @Schema(description = "是否为重要事件")
+     private Boolean isImportant;
+ 
+     @Schema(description = "事件图标")
+     private String icon;
+ 
+     @Schema(description = "事件颜色")
+     private String color;
+ 
+     /**
+      * 根据事件类型设置图标和颜色
+      */
+     public void setIconAndColor() {
+         if (eventType == null) {
+             return;
+         }
+ 
+         this.isImportant = eventType.isImportantEvent();
+ 
+         switch (eventType) {
+             case REGISTRATION_CREATED:
+                 this.icon = "create";
+                 this.color = "blue";
+                 break;
+             case REGISTRATION_SUBMITTED:
+             case REGISTRATION_RESUBMITTED:
+                 this.icon = "submit";
+                 this.color = "orange";
+                 break;
+             case REGISTRATION_APPROVED:
+                 this.icon = "check";
+                 this.color = "green";
+                 break;
+             case REGISTRATION_REJECTED:
+                 this.icon = "close";
+                 this.color = "red";
+                 break;
+             case PATIENT_INFO_UPDATED:
+             case AGENT_INFO_UPDATED:
+             case DISEASE_UPDATED:
+             case DOCUMENTS_UPDATED:
+                 this.icon = "edit";
+                 this.color = "gray";
+                 break;
+             case AGENT_INFO_SET:
+                 this.icon = "user-add";
+                 this.color = "blue";
+                 break;
+             case AGENT_INFO_CLEARED:
+                 this.icon = "user-delete";
+                 this.color = "orange";
+                 break;
+             case DISEASE_SELECTED:
+                 this.icon = "select";
+                 this.color = "purple";
+                 break;
+             case DOCUMENTS_UPLOADED:
+                 this.icon = "upload";
+                 this.color = "cyan";
+                 break;
+             default:
+                 this.icon = "info";
+                 this.color = "gray";
+                 break;
+         }
+     }
+ } 
```

---

## 5. 创建时间线Mapper接口

---

```diff
- 
+ package space.lzhq.ph.mapper;
+ 
+ import com.baomidou.mybatisplus.core.mapper.BaseMapper;
+ import org.apache.ibatis.annotations.Mapper;
+ import org.apache.ibatis.annotations.Param;
+ import space.lzhq.ph.domain.ChronicDiseaseRegistrationTimeline;
+ import space.lzhq.ph.enums.TimelineEventType;
+ 
+ import java.util.List;
+ 
+ /**
+  * 慢病申报时间线Mapper接口
+  *
+  * <AUTHOR>
+  */
+ @Mapper
+ public interface ChronicDiseaseRegistrationTimelineMapper extends BaseMapper<ChronicDiseaseRegistrationTimeline> {
+ 
+     /**
+      * 根据申报记录ID查询时间线事件列表
+      *
+      * @param registrationId 申报记录ID
+      * @param includeDetails 是否包含详细操作（false时只返回重要事件）
+      * @return 时间线事件列表，按事件时间倒序排列
+      */
+     List<ChronicDiseaseRegistrationTimeline> selectTimelineByRegistrationId(
+             @Param("registrationId") String registrationId,
+             @Param("includeDetails") Boolean includeDetails
+     );
+ 
+     /**
+      * 根据申报记录ID和事件类型查询最新的时间线事件
+      *
+      * @param registrationId 申报记录ID
+      * @param eventType      事件类型
+      * @return 最新的时间线事件
+      */
+     ChronicDiseaseRegistrationTimeline selectLatestByRegistrationIdAndEventType(
+             @Param("registrationId") String registrationId,
+             @Param("eventType") TimelineEventType eventType
+     );
+ 
+     /**
+      * 批量插入时间线事件
+      *
+      * @param timelines 时间线事件列表
+      * @return 插入的记录数
+      */
+     int insertBatch(@Param("timelines") List<ChronicDiseaseRegistrationTimeline> timelines);
+ } 
```

---

## 6. 创建时间线Mapper XML文件

---

```diff
- 
+ <?xml version="1.0" encoding="UTF-8" ?>
+ <!DOCTYPE mapper
+         PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
+         "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
+ <mapper namespace="space.lzhq.ph.mapper.ChronicDiseaseRegistrationTimelineMapper">
+ 
+     <resultMap type="space.lzhq.ph.domain.ChronicDiseaseRegistrationTimeline" id="TimelineResult">
+         <result property="id" column="id"/>
+         <result property="registrationId" column="registration_id"/>
+         <result property="eventType" column="event_type"/>
+         <result property="eventDescription" column="event_description"/>
+         <result property="operatorType" column="operator_type"/>
+         <result property="operatorName" column="operator_name"/>
+         <result property="oldStatus" column="old_status"/>
+         <result property="newStatus" column="new_status"/>
+         <result property="eventTime" column="event_time"/>
+         <result property="additionalData" column="additional_data" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
+         <result property="createTime" column="create_time"/>
+     </resultMap>
+ 
+     <sql id="selectTimelineVo">
+         SELECT id, registration_id, event_type, event_description, operator_type, operator_name,
+                old_status, new_status, event_time, additional_data, create_time
+         FROM chronic_disease_registration_timeline
+     </sql>
+ 
+     <select id="selectTimelineByRegistrationId" parameterType="Map" resultMap="TimelineResult">
+         <include refid="selectTimelineVo"/>
+         WHERE registration_id = #{registrationId}
+         <if test="includeDetails != null and includeDetails == false">
+             AND event_type IN ('REGISTRATION_CREATED', 'REGISTRATION_SUBMITTED', 'REGISTRATION_APPROVED', 
+                                'REGISTRATION_REJECTED', 'REGISTRATION_RESUBMITTED')
+         </if>
+         ORDER BY event_time DESC
+     </select>
+ 
+     <select id="selectLatestByRegistrationIdAndEventType" parameterType="Map" resultMap="TimelineResult">
+         <include refid="selectTimelineVo"/>
+         WHERE registration_id = #{registrationId}
+         AND event_type = #{eventType}
+         ORDER BY event_time DESC
+         LIMIT 1
+     </select>
+ 
+     <insert id="insertBatch" parameterType="List">
+         INSERT INTO chronic_disease_registration_timeline
+         (id, registration_id, event_type, event_description, operator_type, operator_name,
+          old_status, new_status, event_time, additional_data, create_time)
+         VALUES
+         <foreach collection="timelines" item="timeline" separator=",">
+             (#{timeline.id}, #{timeline.registrationId}, #{timeline.eventType}, #{timeline.eventDescription},
+              #{timeline.operatorType}, #{timeline.operatorName}, #{timeline.oldStatus}, #{timeline.newStatus},
+              #{timeline.eventTime}, #{timeline.additionalData}, #{timeline.createTime})
+         </foreach>
+     </insert>
+ 
+ </mapper> 
```

---

## 7. 创建时间线Service接口

---

```diff
- 
+ package space.lzhq.ph.service;
+ 
+ import com.baomidou.mybatisplus.extension.service.IService;
+ import jakarta.validation.constraints.NotBlank;
+ import jakarta.validation.constraints.NotNull;
+ import jakarta.validation.constraints.Size;
+ import space.lzhq.ph.domain.ChronicDiseaseRegistrationTimeline;
+ import space.lzhq.ph.dto.response.TimelineEventDto;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ import space.lzhq.ph.enums.TimelineEventType;
+ import space.lzhq.ph.enums.TimelineOperatorType;
+ 
+ import java.util.List;
+ import java.util.Map;
+ 
+ /**
+  * 慢病申报时间线服务接口
+  *
+  * <AUTHOR>
+  */
+ public interface ChronicDiseaseRegistrationTimelineService extends IService<ChronicDiseaseRegistrationTimeline> {
+ 
+     /**
+      * 获取申报记录的时间线
+      *
+      * @param registrationId 申报记录ID
+      * @param includeDetails 是否包含详细操作，false时只返回重要事件
+      * @return 时间线事件列表
+      */
+     List<TimelineEventDto> getRegistrationTimeline(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             Boolean includeDetails
+     );
+ 
+     /**
+      * 记录时间线事件（基础版本）
+      *
+      * @param registrationId   申报记录ID
+      * @param eventType        事件类型
+      * @param eventDescription 事件描述
+      * @param operatorType     操作者类型
+      * @param operatorName     操作者姓名
+      */
+     void recordTimelineEvent(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             @NotNull TimelineEventType eventType,
+             String eventDescription,
+             @NotNull TimelineOperatorType operatorType,
+             String operatorName
+     );
+ 
+     /**
+      * 记录状态变更时间线事件
+      *
+      * @param registrationId   申报记录ID
+      * @param eventType        事件类型
+      * @param eventDescription 事件描述
+      * @param operatorType     操作者类型
+      * @param operatorName     操作者姓名
+      * @param oldStatus        变更前状态
+      * @param newStatus        变更后状态
+      */
+     void recordStatusChangeEvent(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             @NotNull TimelineEventType eventType,
+             String eventDescription,
+             @NotNull TimelineOperatorType operatorType,
+             String operatorName,
+             ChronicDiseaseRegistrationStatus oldStatus,
+             @NotNull ChronicDiseaseRegistrationStatus newStatus
+     );
+ 
+     /**
+      * 记录包含额外数据的时间线事件
+      *
+      * @param registrationId   申报记录ID
+      * @param eventType        事件类型
+      * @param eventDescription 事件描述
+      * @param operatorType     操作者类型
+      * @param operatorName     操作者姓名
+      * @param additionalData   额外数据
+      */
+     void recordTimelineEventWithData(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             @NotNull TimelineEventType eventType,
+             String eventDescription,
+             @NotNull TimelineOperatorType operatorType,
+             String operatorName,
+             Map<String, Object> additionalData
+     );
+ 
+     /**
+      * 记录申报记录创建事件
+      *
+      * @param registrationId 申报记录ID
+      * @param operatorName   操作者姓名
+      */
+     void recordRegistrationCreated(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             String operatorName
+     );
+ 
+     /**
+      * 记录申报记录提交事件
+      *
+      * @param registrationId 申报记录ID
+      * @param operatorName   操作者姓名
+      * @param oldStatus      变更前状态
+      */
+     void recordRegistrationSubmitted(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             String operatorName,
+             ChronicDiseaseRegistrationStatus oldStatus
+     );
+ 
+     /**
+      * 记录代办人信息设置事件
+      *
+      * @param registrationId 申报记录ID
+      * @param operatorName   操作者姓名
+      * @param agentName      代办人姓名
+      */
+     void recordAgentInfoSet(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             String operatorName,
+             String agentName
+     );
+ 
+     /**
+      * 记录代办人信息清除事件
+      *
+      * @param registrationId 申报记录ID
+      * @param operatorName   操作者姓名
+      */
+     void recordAgentInfoCleared(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             String operatorName
+     );
+ 
+     /**
+      * 记录病种选择事件
+      *
+      * @param registrationId 申报记录ID
+      * @param operatorName   操作者姓名
+      * @param diseaseName    病种名称
+      */
+     void recordDiseaseSelected(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             String operatorName,
+             String diseaseName
+     );
+ 
+     /**
+      * 记录认定资料上传事件
+      *
+      * @param registrationId  申报记录ID
+      * @param operatorName    操作者姓名
+      * @param attachmentCount 附件数量
+      */
+     void recordDocumentsUploaded(
+             @NotBlank @Size(min = 32, max = 32) String registrationId,
+             String operatorName,
+             Integer attachmentCount
+     );
+ } 
```

---

## 8. 创建时间线Service实现类

---

```diff
- 
+ package space.lzhq.ph.service.impl;
+ 
+ import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
+ import lombok.extern.slf4j.Slf4j;
+ import org.springframework.beans.factory.annotation.Autowired;
+ import org.springframework.stereotype.Service;
+ import org.springframework.validation.annotation.Validated;
+ import space.lzhq.ph.domain.ChronicDiseaseRegistrationTimeline;
+ import space.lzhq.ph.dto.response.TimelineEventDto;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ import space.lzhq.ph.enums.TimelineEventType;
+ import space.lzhq.ph.enums.TimelineOperatorType;
+ import space.lzhq.ph.mapper.ChronicDiseaseRegistrationTimelineMapper;
+ import space.lzhq.ph.service.ChronicDiseaseRegistrationTimelineService;
+ 
+ import java.time.LocalDateTime;
+ import java.util.HashMap;
+ import java.util.List;
+ import java.util.Map;
+ import java.util.stream.Collectors;
+ 
+ /**
+  * 慢病申报时间线服务实现类
+  *
+  * <AUTHOR>
+  */
+ @Slf4j
+ @Service
+ @Validated
+ public class ChronicDiseaseRegistrationTimelineServiceImpl 
+         extends ServiceImpl<ChronicDiseaseRegistrationTimelineMapper, ChronicDiseaseRegistrationTimeline>
+         implements ChronicDiseaseRegistrationTimelineService {
+ 
+     @Autowired
+     private ChronicDiseaseRegistrationTimelineMapper timelineMapper;
+ 
+     @Override
+     public List<TimelineEventDto> getRegistrationTimeline(String registrationId, Boolean includeDetails) {
+         log.debug("获取申报记录时间线，ID: {}, 包含详情: {}", registrationId, includeDetails);
+         
+         List<ChronicDiseaseRegistrationTimeline> timelines = 
+                 timelineMapper.selectTimelineByRegistrationId(registrationId, includeDetails);
+         
+         return timelines.stream()
+                 .map(this::convertToDto)
+                 .collect(Collectors.toList());
+     }
+ 
+     @Override
+     public void recordTimelineEvent(String registrationId, TimelineEventType eventType, 
+                                    String eventDescription, TimelineOperatorType operatorType, 
+                                    String operatorName) {
+         log.debug("记录时间线事件，申报ID: {}, 事件类型: {}", registrationId, eventType);
+         
+         ChronicDiseaseRegistrationTimeline timeline = new ChronicDiseaseRegistrationTimeline(
+                 registrationId, eventType, eventDescription, operatorType, operatorName);
+         timeline.setCreateTime(LocalDateTime.now());
+         
+         this.save(timeline);
+     }
+ 
+     @Override
+     public void recordStatusChangeEvent(String registrationId, TimelineEventType eventType, 
+                                        String eventDescription, TimelineOperatorType operatorType, 
+                                        String operatorName, ChronicDiseaseRegistrationStatus oldStatus, 
+                                        ChronicDiseaseRegistrationStatus newStatus) {
+         log.debug("记录状态变更事件，申报ID: {}, 事件类型: {}, 状态变更: {} -> {}", 
+                  registrationId, eventType, oldStatus, newStatus);
+         
+         ChronicDiseaseRegistrationTimeline timeline = new ChronicDiseaseRegistrationTimeline(
+                 registrationId, eventType, eventDescription, operatorType, operatorName, oldStatus, newStatus);
+         timeline.setCreateTime(LocalDateTime.now());
+         
+         this.save(timeline);
+     }
+ 
+     @Override
+     public void recordTimelineEventWithData(String registrationId, TimelineEventType eventType, 
+                                            String eventDescription, TimelineOperatorType operatorType, 
+                                            String operatorName, Map<String, Object> additionalData) {
+         log.debug("记录包含额外数据的时间线事件，申报ID: {}, 事件类型: {}", registrationId, eventType);
+         
+         ChronicDiseaseRegistrationTimeline timeline = new ChronicDiseaseRegistrationTimeline(
+                 registrationId, eventType, eventDescription, operatorType, operatorName, additionalData);
+         timeline.setCreateTime(LocalDateTime.now());
+         
+         this.save(timeline);
+     }
+ 
+     @Override
+     public void recordRegistrationCreated(String registrationId, String operatorName) {
+         recordTimelineEvent(registrationId, TimelineEventType.REGISTRATION_CREATED, 
+                            "创建慢病申报记录", TimelineOperatorType.APPLICANT, operatorName);
+     }
+ 
+     @Override
+     public void recordRegistrationSubmitted(String registrationId, String operatorName, 
+                                            ChronicDiseaseRegistrationStatus oldStatus) {
+         recordStatusChangeEvent(registrationId, TimelineEventType.REGISTRATION_SUBMITTED, 
+                                "提交慢病申报", TimelineOperatorType.APPLICANT, operatorName, 
+                                oldStatus, ChronicDiseaseRegistrationStatus.SUBMITTED);
+     }
+ 
+     @Override
+     public void recordAgentInfoSet(String registrationId, String operatorName, String agentName) {
+         Map<String, Object> data = new HashMap<>();
+         data.put("agentName", agentName);
+         
+         recordTimelineEventWithData(registrationId, TimelineEventType.AGENT_INFO_SET, 
+                                    "设置代办人信息：" + agentName, TimelineOperatorType.APPLICANT, 
+                                    operatorName, data);
+     }
+ 
+     @Override
+     public void recordAgentInfoCleared(String registrationId, String operatorName) {
+         recordTimelineEvent(registrationId, TimelineEventType.AGENT_INFO_CLEARED, 
+                            "清除代办人信息", TimelineOperatorType.APPLICANT, operatorName);
+     }
+ 
+     @Override
+     public void recordDiseaseSelected(String registrationId, String operatorName, String diseaseName) {
+         Map<String, Object> data = new HashMap<>();
+         data.put("diseaseName", diseaseName);
+         
+         recordTimelineEventWithData(registrationId, TimelineEventType.DISEASE_SELECTED, 
+                                    "选择慢病病种：" + diseaseName, TimelineOperatorType.APPLICANT, 
+                                    operatorName, data);
+     }
+ 
+     @Override
+     public void recordDocumentsUploaded(String registrationId, String operatorName, Integer attachmentCount) {
+         Map<String, Object> data = new HashMap<>();
+         data.put("attachmentCount", attachmentCount);
+         
+         recordTimelineEventWithData(registrationId, TimelineEventType.DOCUMENTS_UPLOADED, 
+                                    "上传认定资料，共 " + attachmentCount + " 个附件", 
+                                    TimelineOperatorType.APPLICANT, operatorName, data);
+     }
+ 
+     /**
+      * 将时间线实体转换为DTO
+      */
+     private TimelineEventDto convertToDto(ChronicDiseaseRegistrationTimeline timeline) {
+         TimelineEventDto dto = new TimelineEventDto();
+         dto.setId(timeline.getId());
+         dto.setEventType(timeline.getEventType());
+         dto.setEventTypeCode(timeline.getEventType() != null ? timeline.getEventType().getCode() : null);
+         dto.setEventDescription(timeline.getEventDescription());
+         dto.setOperatorType(timeline.getOperatorType());
+         dto.setOperatorTypeCode(timeline.getOperatorType() != null ? timeline.getOperatorType().getCode() : null);
+         dto.setOperatorTypeDescription(timeline.getOperatorType() != null ? timeline.getOperatorType().getDescription() : null);
+         dto.setOperatorName(timeline.getOperatorName());
+         dto.setOldStatus(timeline.getOldStatus());
+         dto.setOldStatusCode(timeline.getOldStatus() != null ? timeline.getOldStatus().getCode() : null);
+         dto.setNewStatus(timeline.getNewStatus());
+         dto.setNewStatusCode(timeline.getNewStatus() != null ? timeline.getNewStatus().getCode() : null);
+         dto.setEventTime(timeline.getEventTime());
+         dto.setAdditionalData(timeline.getAdditionalData());
+         
+         // 设置图标和颜色
+         dto.setIconAndColor();
+         
+         return dto;
+     }
+ } 
```

---

## 9. 在Controller中添加时间线接口

---



---



---

现在我添加时间线接口。由于有很多linter错误，我需要先添加必要的import：

---



---

现在我在Controller末尾添加时间线接口：

---



---

## 10. 创建数据库表的DDL语句

---

```diff
- 
+ -- 慢病申报时间线表
+ DROP TABLE IF EXISTS `chronic_disease_registration_timeline`;
+ 
+ CREATE TABLE `chronic_disease_registration_timeline` (
+     `id` VARCHAR(32) NOT NULL COMMENT '主键',
+     `registration_id` VARCHAR(32) NOT NULL COMMENT '申报记录ID',
+     `event_type` VARCHAR(50) NOT NULL COMMENT '事件类型',
+     `event_description` TEXT COMMENT '事件描述',
+     `operator_type` TINYINT NOT NULL COMMENT '操作者类型：1-申请人，2-代办人，3-系统，4-审核员，5-管理员',
+     `operator_name` VARCHAR(100) COMMENT '操作者姓名',
+     `old_status` VARCHAR(20) COMMENT '变更前状态',
+     `new_status` VARCHAR(20) COMMENT '变更后状态',
+     `event_time` DATETIME NOT NULL COMMENT '事件时间',
+     `additional_data` JSON COMMENT '额外数据（如驳回原因等）',
+     `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
+     PRIMARY KEY (`id`),
+     KEY `idx_registration_id` (`registration_id`),
+     KEY `idx_event_type` (`event_type`),
+     KEY `idx_event_time` (`event_time`),
+     KEY `idx_operator_type` (`operator_type`),
+     CONSTRAINT `fk_timeline_registration` FOREIGN KEY (`registration_id`) 
+         REFERENCES `chronic_disease_registration` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
+ ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='慢病申报时间线表';
+ 
+ -- 创建索引以优化查询性能
+ CREATE INDEX `idx_registration_event_time` ON `chronic_disease_registration_timeline` (`registration_id`, `event_time` DESC);
+ CREATE INDEX `idx_registration_event_type` ON `chronic_disease_registration_timeline` (`registration_id`, `event_type`); 
```

---

现在，让我们编写一个简单的测试来验证时间线功能：

## 11. 创建时间线功能测试

---

```diff
- 
+ package space.lzhq.ph.controller;
+ 
+ import com.fasterxml.jackson.databind.ObjectMapper;
+ import org.junit.jupiter.api.DisplayName;
+ import org.junit.jupiter.api.Test;
+ import org.springframework.beans.factory.annotation.Autowired;
+ import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
+ import org.springframework.boot.test.context.SpringBootTest;
+ import org.springframework.http.MediaType;
+ import org.springframework.test.context.TestPropertySource;
+ import org.springframework.test.web.servlet.MockMvc;
+ import org.springframework.transaction.annotation.Transactional;
+ import space.lzhq.ph.service.ChronicDiseaseRegistrationTimelineService;
+ import space.lzhq.ph.enums.TimelineEventType;
+ import space.lzhq.ph.enums.TimelineOperatorType;
+ import space.lzhq.ph.enums.ChronicDiseaseRegistrationStatus;
+ 
+ import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
+ import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
+ 
+ /**
+  * 慢病申报时间线功能测试
+  *
+  * <AUTHOR>
+  */
+ @SpringBootTest
+ @AutoConfigureWebMvc
+ @TestPropertySource(properties = {
+     "spring.datasource.url=jdbc:h2:mem:testdb",
+     "spring.datasource.driver-class-name=org.h2.Driver",
+     "spring.jpa.database-platform=org.hibernate.dialect.H2Dialect"
+ })
+ @Transactional
+ @DisplayName("慢病申报时间线功能测试")
+ public class ChronicDiseaseRegistrationTimelineTest {
+ 
+     @Autowired
+     private MockMvc mockMvc;
+ 
+     @Autowired
+     private ChronicDiseaseRegistrationTimelineService timelineService;
+ 
+     @Autowired
+     private ObjectMapper objectMapper;
+ 
+     @Test
+     @DisplayName("测试记录时间线事件")
+     public void testRecordTimelineEvent() {
+         // 模拟申报记录ID
+         String registrationId = "test_registration_id_123456789012";
+         String operatorName = "测试用户";
+ 
+         // 记录创建事件
+         timelineService.recordRegistrationCreated(registrationId, operatorName);
+ 
+         // 记录提交事件
+         timelineService.recordRegistrationSubmitted(registrationId, operatorName, 
+                 ChronicDiseaseRegistrationStatus.DRAFT);
+ 
+         // 记录代办人设置事件
+         timelineService.recordAgentInfoSet(registrationId, operatorName, "代办人张三");
+ 
+         // 记录病种选择事件
+         timelineService.recordDiseaseSelected(registrationId, operatorName, "高血压");
+ 
+         // 记录附件上传事件
+         timelineService.recordDocumentsUploaded(registrationId, operatorName, 3);
+ 
+         // 验证时间线记录
+         var timeline = timelineService.getRegistrationTimeline(registrationId, true);
+         
+         // 应该有5个事件记录
+         assert timeline.size() == 5;
+         
+         // 验证事件类型
+         assert timeline.stream().anyMatch(event -> 
+                 event.getEventType() == TimelineEventType.REGISTRATION_CREATED);
+         assert timeline.stream().anyMatch(event -> 
+                 event.getEventType() == TimelineEventType.REGISTRATION_SUBMITTED);
+         assert timeline.stream().anyMatch(event -> 
+                 event.getEventType() == TimelineEventType.AGENT_INFO_SET);
+         assert timeline.stream().anyMatch(event -> 
+                 event.getEventType() == TimelineEventType.DISEASE_SELECTED);
+         assert timeline.stream().anyMatch(event -> 
+                 event.getEventType() == TimelineEventType.DOCUMENTS_UPLOADED);
+     }
+ 
+     @Test
+     @DisplayName("测试时间线详情过滤")
+     public void testTimelineDetailsFilter() {
+         String registrationId = "test_registration_id_234567890123";
+         String operatorName = "测试用户";
+ 
+         // 记录重要事件
+         timelineService.recordRegistrationCreated(registrationId, operatorName);
+         timelineService.recordRegistrationSubmitted(registrationId, operatorName, 
+                 ChronicDiseaseRegistrationStatus.DRAFT);
+ 
+         // 记录普通事件
+         timelineService.recordAgentInfoSet(registrationId, operatorName, "代办人张三");
+         timelineService.recordDiseaseSelected(registrationId, operatorName, "高血压");
+ 
+         // 获取所有事件
+         var allEvents = timelineService.getRegistrationTimeline(registrationId, true);
+         assert allEvents.size() == 4;
+ 
+         // 只获取重要事件
+         var importantEvents = timelineService.getRegistrationTimeline(registrationId, false);
+         assert importantEvents.size() == 2; // 只有创建和提交事件
+ 
+         // 验证重要事件都是状态变更事件
+         assert importantEvents.stream().allMatch(event -> 
+                 event.getEventType().isStatusChangeEvent());
+     }
+ 
+     @Test
+     @DisplayName("测试事件图标和颜色设置")
+     public void testEventIconAndColor() {
+         String registrationId = "test_registration_id_345678901234";
+         String operatorName = "测试用户";
+ 
+         // 记录不同类型的事件
+         timelineService.recordRegistrationCreated(registrationId, operatorName);
+         timelineService.recordAgentInfoSet(registrationId, operatorName, "代办人张三");
+ 
+         var timeline = timelineService.getRegistrationTimeline(registrationId, true);
+ 
+         // 验证创建事件的图标和颜色
+         var createEvent = timeline.stream()
+                 .filter(event -> event.getEventType() == TimelineEventType.REGISTRATION_CREATED)
+                 .findFirst().orElse(null);
+         assert createEvent != null;
+         assert "create".equals(createEvent.getIcon());
+         assert "blue".equals(createEvent.getColor());
+         assert createEvent.getIsImportant();
+ 
+         // 验证代办人设置事件的图标和颜色
+         var agentEvent = timeline.stream()
+                 .filter(event -> event.getEventType() == TimelineEventType.AGENT_INFO_SET)
+                 .findFirst().orElse(null);
+         assert agentEvent != null;
+         assert "user-add".equals(agentEvent.getIcon());
+         assert "blue".equals(agentEvent.getColor());
+         assert !agentEvent.getIsImportant();
+     }
+ } 
```

---

## 总结

我已经完成了慢病申报业务时间线功能的完整实现，采用了混合方案（方案三）。以下是实现的核心组件：

### 🎯 核心功能特性

1. **完整的时间线追踪** - 记录从创建到审核完成的全过程
2. **事件分类管理** - 区分重要事件和详细操作
3. **灵活的查询接口** - 支持包含/排除详细操作的查询
4. **丰富的展示信息** - 包含图标、颜色、操作者等UI友好信息

### 📁 已创建的文件

1. **枚举类**：
   - `TimelineEventType.java` - 时间线事件类型
   - `TimelineOperatorType.java` - 操作者类型

2. **实体类**：
   - `ChronicDiseaseRegistrationTimeline.java` - 时间线实体

3. **DTO类**：
   - `TimelineEventDto.java` - 时间线事件响应DTO

4. **数据访问层**：
   - `ChronicDiseaseRegistrationTimelineMapper.java` - Mapper接口
   - `ChronicDiseaseRegistrationTimelineMapper.xml` - MyBatis XML配置

5. **业务逻辑层**：
   - `ChronicDiseaseRegistrationTimelineService.java` - 服务接口
   - `ChronicDiseaseRegistrationTimelineServiceImpl.java` - 服务实现

6. **控制器层**：
   - 在`ChronicDiseaseRegistrationApiController.kt`中添加了`/timeline`接口

7. **数据库**：
   - `chronic_disease_registration_timeline.sql` - 数据库表DDL

8. **测试**：
   - `ChronicDiseaseRegistrationTimelineTest.java` - 功能测试类

### 🔥 关键设计亮点

1. **事件类型丰富** - 覆盖状态变更和信息修改等各种场景
2. **性能优化** - 支持过滤查询，可只显示重要事件
3. **UI友好** - 自动设置图标和颜色，便于前端展示
4. **扩展性强** - 支持额外数据存储，便于后续功能扩展
5. **权限控制** - 基于现有的session验证机制

### 📋 接下来的集成步骤

1. **在现有Service中集成时间线记录**：
   - 在`save`方法中记录创建事件
   - 在`submitRegistration`方法中记录提交事件
   - 在各种更新方法中记录相应事件

2. **创建数据库表**：
   - 执行提供的DDL语句创建时间线表

3. **前端集成**：
   - 调用`GET /{id}/timeline`接口获取时间线数据
   - 根据返回的图标、颜色信息渲染时间线UI

这个实现为慢病申报业务提供了完整的操作历史追踪功能，有助于用户了解申报进度，也为系统管理提供了重要的审计信息。

---

_**User**_

在 @ChronicDiseaseRegistrationServiceImpl.java 中集成时间线记录

---

_**Assistant**_



---

我来在 `ChronicDiseaseRegistrationServiceImpl.java` 中集成时间线记录功能。

---



---



---

现在添加一个帮助方法来安全地记录时间线事件：

---



---

现在在 save() 方法中添加创建事件记录：

---



---

在 clearAgentFlag() 方法中添加清除代办人事件记录：

---



---

在 updateAgent() 方法中添加设置/更新代办人事件记录：

---



---

在 updateDisease() 方法中添加病种选择/更新事件记录：

---



---

在 updateDocumentAttachments() 方法中添加认定资料上传事件记录：

---



---

在 submitRegistration() 方法中添加提交申报事件记录：

---



---



---



---

现在创建TimelineRecordException异常类：

---

```diff
  
  /**
-  * 时间线记录异常
+  * 时间线记录异常类
+  * 
+  * 当时间线事件记录过程中发生错误时抛出此异常
   *
   * <AUTHOR>
   */
  public class TimelineRecordException extends RuntimeException {
  
+     private static final long serialVersionUID = 1L;
+ 
+     /**
+      * 构造函数
+      *
+      * @param message 异常信息
+      */
      public TimelineRecordException(String message) {
          super(message);
      }
  
+     /**
+      * 构造函数
+      *
+      * @param message 异常信息
+      * @param cause   异常原因
+      */
      public TimelineRecordException(String message, Throwable cause) {
          super(message, cause);
      }
+ 
+     /**
+      * 构造函数
+      *
+      * @param cause 异常原因
+      */
+     public TimelineRecordException(Throwable cause) {
+         super(cause);
+     }
  } 
```

---

现在运行数据库脚本来创建时间线表：

---

```bash
cd /Users/<USER>/IdeaProjects/zhangyi-xyd6fy && echo "检查DDL脚本内容"
```

---

## 时间线功能集成完成 ✅

我已成功在 `ChronicDiseaseRegistrationServiceImpl.java` 中完成了时间线记录功能的集成。

### 🔧 完成的集成工作

1. **依赖注入**：
   - 添加了 `ChronicDiseaseRegistrationTimelineService` 依赖
   - 更新了构造函数

2. **安全记录机制**：
   - 创建了 `safeRecordTimeline()` 方法，确保时间线记录失败不影响主业务逻辑

3. **各业务操作的时间线记录**：
   - **创建申报记录** (`save()`)：记录 `REGISTRATION_CREATED` 事件
   - **清除代办标志** (`clearAgentFlag()`)：记录 `AGENT_INFO_CLEARED` 事件  
   - **设置代办人** (`updateAgent()`)：记录 `AGENT_INFO_SET` 事件
   - **选择病种** (`updateDisease()`)：记录 `DISEASE_SELECTED` 事件
   - **上传认定资料** (`updateDocumentAttachments()`)：记录 `DOCUMENTS_UPLOADED` 事件
   - **提交申报** (`submitRegistration()`)：记录 `REGISTRATION_SUBMITTED` 事件

4. **异常处理**：
   - 创建了 `TimelineRecordException` 异常类

### 🎯 核心特性

- **非侵入性**：时间线记录不会影响主业务逻辑的执行
- **自动记录**：在每个关键业务操作后自动记录相应的时间线事件
- **完整追踪**：从创建申报到提交的全流程都有详细记录
- **异常安全**：时间线记录失败只会产生警告日志，不会中断业务流程

### 📋 下一步建议

1. **数据库表创建**：执行 `ddl/ph/chronic_disease_registration_timeline.sql` 脚本
2. **测试验证**：创建测试用例验证时间线功能是否正常工作
3. **前端集成**：前端调用 `/api/chronic-disease-registration/{id}/timeline` 接口展示时间线

现在用户可以通过API接口查看慢病申报的完整时间线，包括每个操作的时间、描述、操作者等详细信息！

---

_**User**_

操作者并不总是患者本人，如果是代办模式，操作者应该是代办人。

---

_**Assistant**_



---

您说得对！操作者应该根据是否是代办模式来确定。我来修改 `ChronicDiseaseRegistrationServiceImpl.java` 中的逻辑。

---



---

现在修改 `save()` 方法中的时间线记录：

---



---

修改 `clearAgentFlag()` 方法中的时间线记录：

---



---

修改 `updateAgent()` 方法中的时间线记录：

---



---

修改 `updateDisease()` 方法中的时间线记录：

---



---



---

修改 `updateDocumentAttachments()` 方法中的时间线记录：

---



---



---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java

---

现在我看到了确切的代码，修改 `updateDocumentAttachments()` 方法中的时间线记录：

---



---

最后修改 `submitRegistration()` 方法中的时间线记录：

---



---

完美！现在我已经修改了 `ChronicDiseaseRegistrationServiceImpl.java` 中的所有时间线记录逻辑，添加了 `getOperatorName()` 辅助方法来根据代办标志确定操作者：

## 主要修改内容

**1. 新增辅助方法 `getOperatorName()`：**
- 判断申报记录的代办标志（`agentFlag`）
- 如果是代办模式（`agentFlag == 1`）且代办人姓名不为空，返回代办人姓名
- 否则返回患者本人姓名

**2. 更新所有时间线记录调用：**
- `save()` 方法：创建申报记录事件
- `clearAgentFlag()` 方法：清除代办人信息事件
- `updateAgent()` 方法：设置代办人信息事件
- `updateDisease()` 方法：选择病种事件
- `updateDocumentAttachments()` 方法：上传认定资料事件
- `submitRegistration()` 方法：提交申报记录事件

现在时间线系统能够正确识别操作者是患者本人还是代办人，这样用户在查看时间线时就能清楚地知道每个操作是由谁执行的，提供了更准确的审计追踪功能。

---

