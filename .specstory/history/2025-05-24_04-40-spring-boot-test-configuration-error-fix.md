<!-- Generated by SpecStory -->

# Spring Boot Test Configuration Error Fix (2025-05-24 13:40:29)

_**User**_

修复以下错误：
```
13:28:09.802 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest]: ChronicDiseaseRegistrationAttachmentApiControllerTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.

java.lang.IllegalStateException: Unable to find a @SpringBootConfiguration by searching packages upwards from the test. You can use @ContextConfiguration, @SpringBootTest(classes=...) or other Spring Test supported mechanisms to explicitly declare the configuration classes to load. Classes annotated with @TestConfiguration are not considered.

	at org.springframework.util.Assert.state(Assert.java:79)
	at org.springframework.boot.test.context.SpringBootTestContextBootstrapper.findConfigurationClass(SpringBootTestContextBootstrapper.java:246)
	at org.springframework.boot.test.context.SpringBootTestContextBootstrapper.getOrFindConfigurationClasses(SpringBootTestContextBootstrapper.java:233)
	at org.springframework.boot.test.context.SpringBootTestContextBootstrapper.processMergedContextConfiguration(SpringBootTestContextBootstrapper.java:150)
	at org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper.processMergedContextConfiguration(WebMvcTestContextBootstrapper.java:36)
	at org.springframework.test.context.support.AbstractTestContextBootstrapper.buildMergedContextConfiguration(AbstractTestContextBootstrapper.java:351)
	at org.springframework.test.context.support.AbstractTestContextBootstrapper.buildDefaultMergedContextConfiguration(AbstractTestContextBootstrapper.java:267)
	at org.springframework.test.context.support.AbstractTestContextBootstrapper.buildMergedContextConfiguration(AbstractTestContextBootstrapper.java:215)
```

注意以下背景信息：
1. @ChronicDiseaseRegistrationAttachmentApiControllerTest.kt 是在 @xydlfy-ph 模块内
2. 主应用类 @RuoYiApplication.java 是在 @ruoyi-admin 模块内

---

_**Assistant**_



---

看到错误信息，问题是Spring Boot测试无法自动发现`@SpringBootConfiguration`配置类。这是因为测试类在`xydlfy-ph`模块的`space.lzhq.ph`包中，而主应用类在`ruoyi-admin`模块的`com.ruoyi`包中，包结构不匹配导致Spring Boot无法自动发现配置。

我将为你提供几种解决方案：

## 方案1：创建测试专用的Spring Boot配置类（推荐）

---

```diff
- 
+ package space.lzhq.ph.config
+ 
+ import org.springframework.boot.autoconfigure.SpringBootApplication
+ import org.springframework.boot.test.context.TestConfiguration
+ 
+ /**
+  * 测试应用配置类
+  * 
+  * 提供Spring Boot测试所需的配置，解决跨模块测试中的配置发现问题。
+  * 这个类模拟主应用配置，但仅用于测试环境。
+  */
+ @SpringBootApplication(
+     scanBasePackages = ["space.lzhq.ph"]
+ )
+ @TestConfiguration
+ class TestApplicationConfiguration 
```

---



---



---



---

## 方案2：如果方案1不够，还需要完善测试配置

---



---

现在让我们运行测试验证修复是否成功：

---

```bash
cd /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph && mvn test -Dtest=ChronicDiseaseRegistrationAttachmentApiControllerTest
```

---

## 修复说明

我已经为你修复了Spring Boot测试配置问题，主要做了以下更改：

### 1. 创建了测试专用的Spring Boot配置类

在 `xydlfy-ph/src/test/kotlin/space/lzhq/ph/config/TestApplicationConfiguration.kt` 中创建了一个新的配置类：

```kotlin
@SpringBootApplication(
    scanBasePackages = ["space.lzhq.ph"]
)
class TestApplicationConfiguration
```

这个类：
- 使用 `@SpringBootApplication` 注解，Spring Boot会将其识别为配置类
- 只扫描 `space.lzhq.ph` 包，确保测试环境的隔离性
- 解决了跨模块测试中的配置发现问题

### 2. 更新了测试类注解

在测试类中添加了 `@ContextConfiguration` 注解：

```kotlin
@ContextConfiguration(classes = [TestApplicationConfiguration::class])
```

这样明确告诉Spring Boot使用我们创建的测试配置类。

### 3. 添加了必要的导入

添加了相关的导入语句以支持新的注解。

## 解决方案优势

1. **模块隔离**：测试配置只扫描当前模块的包，不会加载其他模块的配置
2. **清晰明确**：通过 `@ContextConfiguration` 明确指定配置类，避免Spring Boot的自动发现机制
3. **可维护性**：测试配置独立，便于后续维护和扩展

## 验证修复

你可以运行以下命令来验证修复是否成功：

```bash
cd xydlfy-ph
mvn test -Dtest=ChronicDiseaseRegistrationAttachmentApiControllerTest
```

如果还有其他问题，请告诉我具体的错误信息，我会进一步帮你解决。

---

_**User**_

测试仍然不能正常运行，报了大量Spring初始化相关的错误。考虑一下，是不是把 @ChronicDiseaseRegistrationAttachmentApiControllerTest.kt 移动到 @ruoyi-admin 模块内更好？

---

_**Assistant**_



---

你的建议非常好！从项目架构来看，将测试移动到 `ruoyi-admin` 模块确实是更好的选择。让我分析一下原因：

## 为什么移动到 ruoyi-admin 更好？

1. **完整的Spring配置**：`ruoyi-admin` 是主应用模块，包含完整的Spring Boot配置
2. **避免跨模块复杂性**：不需要创建额外的测试配置类
3. **真实运行环境**：测试环境更接近实际运行环境
4. **依赖完整性**：`ruoyi-admin` 已经包含了所有必要的依赖

让我帮你移动测试文件：

---

<details>
            <summary>Listed directory ruoyi-admin/src/test/java • **1** results</summary>
        
| Name |
|-------|
| 📁 `com` |

</details>

---

```bash
mkdir -p ruoyi-admin/src/test/java/space/lzhq/ph/controller
```

```
rochuukyou@luzhongqiangdeiMac ~/I/zhangyi-xyd6fy (慢病申报)> mkdir -p ruoyi-admin/src/test/java/space/lzhq/p
h/controller
                                                             mkdir -p ruoyi-admin/src/test/java/space/lzhq/p
h/controller
```

---

现在让我创建Java版本的测试类（因为ruoyi-admin使用Java）：

---

```diff
- 
+ package space.lzhq.ph.controller;
+ 
+ import org.junit.jupiter.api.BeforeEach;
+ import org.junit.jupiter.api.DisplayName;
+ import org.junit.jupiter.api.Test;
+ import org.junit.jupiter.api.extension.ExtendWith;
+ import org.mockito.ArgumentMatchers;
+ import org.mockito.BDDMockito;
+ import org.mockito.junit.jupiter.MockitoExtension;
+ import org.springframework.beans.factory.annotation.Autowired;
+ import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
+ import org.springframework.boot.test.mock.mockito.MockBean;
+ import org.springframework.http.MediaType;
+ import org.springframework.mock.web.MockHttpSession;
+ import org.springframework.mock.web.MockMultipartFile;
+ import org.springframework.test.context.ActiveProfiles;
+ import org.springframework.test.web.servlet.MockMvc;
+ import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
+ import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
+ import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
+ import space.lzhq.ph.common.Values;
+ import space.lzhq.ph.domain.ChronicDiseaseRegistrationAttachment;
+ import space.lzhq.ph.domain.Session;
+ import space.lzhq.ph.service.ChronicDiseaseRegistrationAttachmentService;
+ 
+ import java.time.LocalDateTime;
+ 
+ /**
+  * ChronicDiseaseRegistrationAttachmentApiController 单元测试
+  *
+  * 使用 @WebMvcTest 来测试控制器层，只加载Web层配置，避免完整的Spring Boot上下文启动。
+  * 这样可以提高测试执行速度，符合单元测试的快速反馈原则。
+  */
+ @ExtendWith(MockitoExtension.class)
+ @WebMvcTest(ChronicDiseaseRegistrationAttachmentApiController.class)
+ @ActiveProfiles("test")
+ @DisplayName("慢病上报附件管理控制器测试")
+ public class ChronicDiseaseRegistrationAttachmentApiControllerTest {
+ 
+     @Autowired
+     private MockMvc mockMvc;
+ 
+     @MockBean
+     private ChronicDiseaseRegistrationAttachmentService attachmentService;
+ 
+     private MockHttpSession mockSession;
+     private Session clientSession;
+     private MockMultipartFile testImageFile;
+     private ChronicDiseaseRegistrationAttachment testAttachment;
+ 
+     private static final String TEST_OPEN_ID = "test_open_id_123456";
+     private static final String TEST_SESSION_ID = "test_session_id_789";
+     private static final String TEST_ATTACHMENT_ID = "test-attachment-id-123";
+     private static final String TEST_FILE_NAME = "test_id_card.jpg";
+     private static final String TEST_FILE_URL = "/upload/test_id_card.jpg";
+     private static final String ID_CARD_ENDPOINT = "/api/chronic-disease-registration-attachment/idCard";
+     private static final String MEDICARE_CARD_ENDPOINT = "/api/chronic-disease-registration-attachment/medicareCard";
+     private static final String AUTHORIZATION_HEADER = "Authorization";
+ 
+     @BeforeEach
+     void setUp() {
+         // 安排 - 设置测试数据和会话
+         setupTestSession();
+         setupTestFile();
+         setupTestAttachment();
+     }
+ 
+     private void setupTestSession() {
+         mockSession = new MockHttpSession();
+         clientSession = new Session();
+         clientSession.setOpenId(TEST_OPEN_ID);
+         clientSession.setId(TEST_SESSION_ID);
+         mockSession.setAttribute(Values.CLIENT_SESSION, clientSession);
+     }
+ 
+     private void setupTestFile() {
+         testImageFile = new MockMultipartFile(
+                 "file",
+                 TEST_FILE_NAME,
+                 MediaType.IMAGE_JPEG_VALUE,
+                 "test image content".getBytes()
+         );
+     }
+ 
+     private void setupTestAttachment() {
+         testAttachment = createTestAttachment(ChronicDiseaseRegistrationAttachment.AttachmentType.ID_CARD);
+     }
+ 
+     /**
+      * 创建测试用的附件对象
+      */
+     private ChronicDiseaseRegistrationAttachment createTestAttachment(
+             ChronicDiseaseRegistrationAttachment.AttachmentType attachmentType) {
+         return createTestAttachment(attachmentType, TEST_ATTACHMENT_ID, TEST_OPEN_ID, TEST_FILE_NAME, TEST_FILE_URL);
+     }
+ 
+     private ChronicDiseaseRegistrationAttachment createTestAttachment(
+             ChronicDiseaseRegistrationAttachment.AttachmentType attachmentType,
+             String id,
+             String openId,
+             String fileName,
+             String fileUrl) {
+         return ChronicDiseaseRegistrationAttachment.builder()
+                 .id(id)
+                 .openId(openId)
+                 .attachmentType(attachmentType)
+                 .fileName(fileName)
+                 .fileUrl(fileUrl)
+                 .createTime(LocalDateTime.now())
+                 .build();
+     }
+ 
+     @Test
+     @DisplayName("当上传有效身份证图片时_应该返回成功响应和OCR结果")
+     void givenValidIdCardImage_whenUploadIdCard_thenReturnsSuccessWithOcrResult() throws Exception {
+         // 安排 - 模拟依赖行为
+         BDDMockito.given(attachmentService.save(ArgumentMatchers.any(ChronicDiseaseRegistrationAttachment.class)))
+                 .willReturn(true);
+ 
+         // 执行和断言
+         mockMvc.perform(
+                 MockMvcRequestBuilders.multipart(ID_CARD_ENDPOINT)
+                         .file(testImageFile)
+                         .header(AUTHORIZATION_HEADER, TEST_SESSION_ID)
+                         .session(mockSession)
+                         .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
+         )
+                 .andDo(MockMvcResultHandlers.print())
+                 .andExpect(MockMvcResultMatchers.status().isOk())
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.msg").value("操作成功"))
+                 .andExpected(MockMvcResultMatchers.jsonPath("$.data").exists());
+ 
+         // 验证 - 确认依赖被正确调用
+         BDDMockito.then(attachmentService).should().save(ArgumentMatchers.any(ChronicDiseaseRegistrationAttachment.class));
+     }
+ 
+     @Test
+     @DisplayName("当上传有效医保卡图片时_应该返回成功响应")
+     void givenValidMedicareCardImage_whenUploadMedicareCard_thenReturnsSuccess() throws Exception {
+         // 安排 - 模拟依赖行为
+         BDDMockito.given(attachmentService.save(ArgumentMatchers.any(ChronicDiseaseRegistrationAttachment.class)))
+                 .willReturn(true);
+ 
+         // 执行和断言
+         mockMvc.perform(
+                 MockMvcRequestBuilders.multipart(MEDICARE_CARD_ENDPOINT)
+                         .file(testImageFile)
+                         .header(AUTHORIZATION_HEADER, TEST_SESSION_ID)
+                         .session(mockSession)
+                         .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
+         )
+                 .andDo(MockMvcResultHandlers.print())
+                 .andExpect(MockMvcResultMatchers.status().isOk())
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.msg").value("操作成功"))
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.data").exists());
+ 
+         // 验证 - 确认依赖被正确调用
+         BDDMockito.then(attachmentService).should().save(ArgumentMatchers.any(ChronicDiseaseRegistrationAttachment.class));
+     }
+ 
+     @Test
+     @DisplayName("当缺少Authorization头时_应该返回会话无效错误")
+     void givenMissingAuthorizationHeader_whenUploadFile_thenReturnsSessionInvalidError() throws Exception {
+         // 执行和断言
+         mockMvc.perform(
+                 MockMvcRequestBuilders.multipart(ID_CARD_ENDPOINT)
+                         .file(testImageFile)
+                         .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
+         )
+                 .andDo(MockMvcResultHandlers.print())
+                 .andExpect(MockMvcResultMatchers.status().isOk())
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(500))
+                 .andExpect(MockMvcResultMatchers.jsonPath("$.msg").value("会话无效"));
+ 
+         // 验证 - 确认服务未被调用
+         BDDMockito.then(attachmentService).should(BDDMockito.never()).save(ArgumentMatchers.any(ChronicDiseaseRegistrationAttachment.class));
+     }
+ 
+     @Test
+     @DisplayName("当缺少文件参数时_应该返回400错误")
+     void givenMissingFileParameter_whenUploadFile_thenReturnsBadRequest() throws Exception {
+         // 执行和断言
+         mockMvc.perform(
+                 MockMvcRequestBuilders.multipart(ID_CARD_ENDPOINT)
+                         .header(AUTHORIZATION_HEADER, TEST_SESSION_ID)
+                         .session(mockSession)
+                         .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
+         )
+                 .andDo(MockMvcResultHandlers.print())
+                 .andExpect(MockMvcResultMatchers.status().isBadRequest());
+ 
+         // 验证 - 确认服务未被调用
+         BDDMockito.then(attachmentService).should(BDDMockito.never()).save(ArgumentMatchers.any(ChronicDiseaseRegistrationAttachment.class));
+     }
+ 
+     @Test
+     @DisplayName("当使用不支持的HTTP方法时_应该返回405错误")
+     void givenUnsupportedHttpMethod_whenAccessEndpoint_thenReturnsMethodNotAllowed() throws Exception {
+         // 执行和断言 - 使用GET方法访问POST端点
+         mockMvc.perform(
+                 MockMvcRequestBuilders.get(ID_CARD_ENDPOINT)
+                         .header(AUTHORIZATION_HEADER, TEST_SESSION_ID)
+                         .session(mockSession)
+         )
+                 .andDo(MockMvcResultHandlers.print())
+                 .andExpect(MockMvcResultMatchers.status().isMethodNotAllowed());
+     }
+ 
+     @Test
+     @DisplayName("当Content-Type不是multipart时_应该返回415错误")
+     void givenWrongContentType_whenUploadFile_thenReturnsUnsupportedMediaType() throws Exception {
+         // 执行和断言 - 使用错误的Content-Type
+         mockMvc.perform(
+                 MockMvcRequestBuilders.post(ID_CARD_ENDPOINT)
+                         .header(AUTHORIZATION_HEADER, TEST_SESSION_ID)
+                         .session(mockSession)
+                         .contentType(MediaType.APPLICATION_JSON)
+                         .content("{\"file\": \"test\"}")
+         )
+                 .andDo(MockMvcResultHandlers.print())
+                 .andExpect(MockMvcResultMatchers.status().isUnsupportedMediaType());
+     }
+ } 
```

---

让我修复代码中的一个小错误：

---



---

现在删除原来的Kotlin测试文件和测试配置：

---

删除原来的Kotlin测试文件

---

删除不再需要的测试配置文件

---

现在让我们运行测试验证是否正常工作：

---

```bash
cd ruoyi-admin && mvn test -Dtest=ChronicDiseaseRegistrationAttachmentApiControllerTest
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------------< com.ruoyi:ruoyi-admin >------------------------
[INFO] Building ruoyi-admin 4.7.7
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.8.0:properties (properties) @ ruoyi-admin ---
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-admin ---
[INFO] Copying 451 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- closure-compiler:2.25.0:minify (minify-js) @ ruoyi-admin ---
[INFO] Starting JavaScript task:
[INFO] Skipping the merge step...
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [index.js].
[INFO] Uncompressed size: 24045 bytes.
[INFO] Compressed size: 14588 bytes minified (4034 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [common.js].
[INFO] Uncompressed size: 23954 bytes.
[INFO] Compressed size: 13785 bytes minified (4831 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [ry-ui.js].
5月 24, 2025 1:55:40 下午 com.google.javascript.jscomp.LoggerErrorManager println
WARNING: ruoyi/js/ry-ui.js:1624:20: WARNING - [JSC_SUSPICIOUS_INSTANCEOF_LEFT] "instanceof" with left non-ob
ject operand is always false.
  1624|                 if (!date instanceof Date) return;
                            ^^^^^^^^^^^^^^^^^^^^^

5月 24, 2025 1:55:40 下午 com.google.javascript.jscomp.LoggerErrorManager printSummary
WARNING: 0 error(s), 1 warning(s)
[WARNING] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/resources/static/ruoyi/js/ry-ui
.js [1624:20]: WARNING - "instanceof" with left non-object operand is always false.

[INFO] Uncompressed size: 86700 bytes.
[INFO] Compressed size: 34692 bytes minified (9848 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [login.js].
[INFO] Uncompressed size: 2754 bytes.
[INFO] Compressed size: 1512 bytes minified (860 bytes gzipped).
[INFO] Processed 4 output files
[INFO] Created 4 output files
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ ruoyi-admin ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-admin ---
[INFO] skip non existing resourceDirectory /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/tes
t/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ ruoyi-admin ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 21 source files with javac [debug release 21] to target/test-classes
[INFO] Annotation processing is enabled because one or more processors were found
  on the class path. A future release of javac may disable annotation processing
  unless at least one processor is specified by name (-processor), or a search
  path is specified (--processor-path, --processor-module-path), or annotation
  processing is enabled explicitly (-proc:only, -proc:full).
  Use -Xlint:-options to suppress this message.
  Use -proc:none to disable annotation processing.
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AbcPosRecon
ciliationTest.java:[6,29] cannot find symbol
  symbol:   class IAbcPosReconciliationService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AbcPosRecon
ciliationTest.java:[14,13] cannot find symbol
  symbol:   class IAbcPosReconciliationService
  location: class com.ruoyi.xyd6fy.AbcPosReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AlipayCheck
Test.java:[10,28] cannot find symbol
  symbol:   class HisSelfPayOrder
  location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AlipayCheck
Test.java:[12,29] cannot find symbol
  symbol:   class IHisSelfPayOrderService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AlipayCheck
Test.java:[28,13] cannot find symbol
  symbol:   class IHisSelfPayOrderService
  location: class com.ruoyi.xyd6fy.AlipayCheckTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AutoRefundM
enzhenTest.java:[15,29] package space.lzhq.ph.pay.ccb does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AutoRefundM
enzhenTest.java:[16,34] package space.lzhq.ph.pay.ccb.bean does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AutoRefundM
enzhenTest.java:[17,34] package space.lzhq.ph.pay.ccb.bean does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[12,28] cannot find symbol
  symbol:   class OutpatientStopClinic
  location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[15,29] cannot find symbol
  symbol:   class OutpatientStopClinicService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[16,32] package space.lzhq.ph.webservice does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[29,13] cannot find symbol
  symbol:   class BSXmlWsEntryClass
  location: class com.ruoyi.xyd6fy.BSXmlWsEntryClassTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[35,13] cannot find symbol
  symbol:   class OutpatientStopClinicService
  location: class com.ruoyi.xyd6fy.BSXmlWsEntryClassTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CCBMailBill
ServiceTest.java:[6,29] cannot find symbol
  symbol:   class CCBMailBillService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CCBMailBill
ServiceTest.java:[15,13] cannot find symbol
  symbol:   class CCBMailBillService
  location: class com.ruoyi.xyd6fy.CCBMailBillServiceTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CCBPayBillT
est.java:[3,28] package cn.yqtl.pay.ccb.util does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CcbPosRecon
ciliationTest.java:[6,29] cannot find symbol
  symbol:   class ICcbPosReconciliationService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CcbPosRecon
ciliationTest.java:[14,13] cannot find symbol
  symbol:   class ICcbPosReconciliationService
  location: class com.ruoyi.xyd6fy.CcbPosReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[14,13] cannot find symbol
  symbol:   class IZhangyiWeixinReconciliationService
  location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[17,13] cannot find symbol
  symbol:   class IZhangyiAlipayReconciliationService
  location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[20,13] cannot find symbol
  symbol:   class IGuoguangAtmReconciliationService
  location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[23,13] cannot find symbol
  symbol:   class ICcbPosReconciliationService
  location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[26,13] cannot find symbol
  symbol:   class IAbcPosReconciliationService
  location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/GuoguangAtm
ReconciliationTest.java:[6,29] cannot find symbol
  symbol:   class IGuoguangAtmReconciliationService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/GuoguangAtm
ReconciliationTest.java:[14,13] cannot find symbol
  symbol:   class IGuoguangAtmReconciliationService
  location: class com.ruoyi.xyd6fy.GuoguangAtmReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/InitTest.ja
va:[7,30] package org.mospital.bsoft.mip does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/InitTest.ja
va:[9,29] package space.lzhq.ph.pay.ccb does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/PatientInfo
TokenTest.java:[4,28] cannot find symbol
  symbol:   class PatientInfoToken
  location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WeixinTrans
ferReconciliationTest.java:[6,29] cannot find symbol
  symbol:   class IWeixinTransferReconciliationService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WeixinTrans
ferReconciliationTest.java:[14,13] cannot find symbol
  symbol:   class IWeixinTransferReconciliationService
  location: class com.ruoyi.xyd6fy.WeixinTransferReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[7,28] cannot find symbol
  symbol:   class HisSelfPayOrder
  location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[8,29] package space.lzhq.ph.pay.ccb does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[9,29] cannot find symbol
  symbol:   class IHisSelfPayOrderService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[22,13] cannot find symbol
  symbol:   class IHisSelfPayOrderService
  location: class com.ruoyi.xyd6fy.WxCheckTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiAlip
ayReconciliationIntegrationTest.java:[5,29] cannot find symbol
  symbol:   class IZhangyiAlipayReconciliationService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiAlip
ayReconciliationIntegrationTest.java:[20,13] cannot find symbol
  symbol:   class IZhangyiAlipayReconciliationService
  location: class com.ruoyi.xyd6fy.ZhangyiAlipayReconciliationIntegrationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiWeix
inReconciliationTest.java:[6,29] cannot find symbol
  symbol:   class IZhangyiWeixinReconciliationService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiWeix
inReconciliationTest.java:[14,13] cannot find symbol
  symbol:   class IZhangyiWeixinReconciliationService
  location: class com.ruoyi.xyd6fy.ZhangyiWeixinReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[22,28] cannot find symbol
  symbol:   class ChronicDiseaseRegistrationAttachment
  location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[24,29] cannot find symbol
  symbol:   class ChronicDiseaseRegistrationAttachmentService
  location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[44,13] cannot find symbol
  symbol:   class ChronicDiseaseRegistrationAttachmentService
  location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[49,13] cannot find symbol
  symbol:   class ChronicDiseaseRegistrationAttachment
  location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[93,49] package ChronicDiseaseRegistrationAttachment
 does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[92,13] cannot find symbol
  symbol:   class ChronicDiseaseRegistrationAttachment
  location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[98,49] package ChronicDiseaseRegistrationAttachment
 does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[97,13] cannot find symbol
  symbol:   class ChronicDiseaseRegistrationAttachment
  location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[35,13] cannot find symbol
  symbol: class ChronicDiseaseRegistrationAttachmentApiController
[INFO] 47 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  4.441 s
[INFO] Finished at: 2025-05-24T13:55:41+09:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.13.0:testCompile (default-te
stCompile) on project ruoyi-admin: Compilation failure: Compilation failure:
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AbcPosRecon
ciliationTest.java:[6,29] cannot find symbol
[ERROR]   symbol:   class IAbcPosReconciliationService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AbcPosRecon
ciliationTest.java:[14,13] cannot find symbol
[ERROR]   symbol:   class IAbcPosReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.AbcPosReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AlipayCheck
Test.java:[10,28] cannot find symbol
[ERROR]   symbol:   class HisSelfPayOrder
[ERROR]   location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AlipayCheck
Test.java:[12,29] cannot find symbol
[ERROR]   symbol:   class IHisSelfPayOrderService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AlipayCheck
Test.java:[28,13] cannot find symbol
[ERROR]   symbol:   class IHisSelfPayOrderService
[ERROR]   location: class com.ruoyi.xyd6fy.AlipayCheckTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AutoRefundM
enzhenTest.java:[15,29] package space.lzhq.ph.pay.ccb does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AutoRefundM
enzhenTest.java:[16,34] package space.lzhq.ph.pay.ccb.bean does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/AutoRefundM
enzhenTest.java:[17,34] package space.lzhq.ph.pay.ccb.bean does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[12,28] cannot find symbol
[ERROR]   symbol:   class OutpatientStopClinic
[ERROR]   location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[15,29] cannot find symbol
[ERROR]   symbol:   class OutpatientStopClinicService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[16,32] package space.lzhq.ph.webservice does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[29,13] cannot find symbol
[ERROR]   symbol:   class BSXmlWsEntryClass
[ERROR]   location: class com.ruoyi.xyd6fy.BSXmlWsEntryClassTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntr
yClassTest.java:[35,13] cannot find symbol
[ERROR]   symbol:   class OutpatientStopClinicService
[ERROR]   location: class com.ruoyi.xyd6fy.BSXmlWsEntryClassTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CCBMailBill
ServiceTest.java:[6,29] cannot find symbol
[ERROR]   symbol:   class CCBMailBillService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CCBMailBill
ServiceTest.java:[15,13] cannot find symbol
[ERROR]   symbol:   class CCBMailBillService
[ERROR]   location: class com.ruoyi.xyd6fy.CCBMailBillServiceTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CCBPayBillT
est.java:[3,28] package cn.yqtl.pay.ccb.util does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CcbPosRecon
ciliationTest.java:[6,29] cannot find symbol
[ERROR]   symbol:   class ICcbPosReconciliationService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/CcbPosRecon
ciliationTest.java:[14,13] cannot find symbol
[ERROR]   symbol:   class ICcbPosReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.CcbPosReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[14,13] cannot find symbol
[ERROR]   symbol:   class IZhangyiWeixinReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[17,13] cannot find symbol
[ERROR]   symbol:   class IZhangyiAlipayReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[20,13] cannot find symbol
[ERROR]   symbol:   class IGuoguangAtmReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[23,13] cannot find symbol
[ERROR]   symbol:   class ICcbPosReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/DailyReconc
iliationTest.java:[26,13] cannot find symbol
[ERROR]   symbol:   class IAbcPosReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.DailyReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/GuoguangAtm
ReconciliationTest.java:[6,29] cannot find symbol
[ERROR]   symbol:   class IGuoguangAtmReconciliationService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/GuoguangAtm
ReconciliationTest.java:[14,13] cannot find symbol
[ERROR]   symbol:   class IGuoguangAtmReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.GuoguangAtmReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/InitTest.ja
va:[7,30] package org.mospital.bsoft.mip does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/InitTest.ja
va:[9,29] package space.lzhq.ph.pay.ccb does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/PatientInfo
TokenTest.java:[4,28] cannot find symbol
[ERROR]   symbol:   class PatientInfoToken
[ERROR]   location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WeixinTrans
ferReconciliationTest.java:[6,29] cannot find symbol
[ERROR]   symbol:   class IWeixinTransferReconciliationService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WeixinTrans
ferReconciliationTest.java:[14,13] cannot find symbol
[ERROR]   symbol:   class IWeixinTransferReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.WeixinTransferReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[7,28] cannot find symbol
[ERROR]   symbol:   class HisSelfPayOrder
[ERROR]   location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[8,29] package space.lzhq.ph.pay.ccb does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[9,29] cannot find symbol
[ERROR]   symbol:   class IHisSelfPayOrderService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/WxCheckTest
.java:[22,13] cannot find symbol
[ERROR]   symbol:   class IHisSelfPayOrderService
[ERROR]   location: class com.ruoyi.xyd6fy.WxCheckTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiAlip
ayReconciliationIntegrationTest.java:[5,29] cannot find symbol
[ERROR]   symbol:   class IZhangyiAlipayReconciliationService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiAlip
ayReconciliationIntegrationTest.java:[20,13] cannot find symbol
[ERROR]   symbol:   class IZhangyiAlipayReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.ZhangyiAlipayReconciliationIntegrationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiWeix
inReconciliationTest.java:[6,29] cannot find symbol
[ERROR]   symbol:   class IZhangyiWeixinReconciliationService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/ZhangyiWeix
inReconciliationTest.java:[14,13] cannot find symbol
[ERROR]   symbol:   class IZhangyiWeixinReconciliationService
[ERROR]   location: class com.ruoyi.xyd6fy.ZhangyiWeixinReconciliationTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[22,28] cannot find symbol
[ERROR]   symbol:   class ChronicDiseaseRegistrationAttachment
[ERROR]   location: package space.lzhq.ph.domain
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[24,29] cannot find symbol
[ERROR]   symbol:   class ChronicDiseaseRegistrationAttachmentService
[ERROR]   location: package space.lzhq.ph.service
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[44,13] cannot find symbol
[ERROR]   symbol:   class ChronicDiseaseRegistrationAttachmentService
[ERROR]   location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[49,13] cannot find symbol
[ERROR]   symbol:   class ChronicDiseaseRegistrationAttachment
[ERROR]   location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[93,49] package ChronicDiseaseRegistrationAttachment
 does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[92,13] cannot find symbol
[ERROR]   symbol:   class ChronicDiseaseRegistrationAttachment
[ERROR]   location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[98,49] package ChronicDiseaseRegistrationAttachment
 does not exist
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[97,13] cannot find symbol
[ERROR]   symbol:   class ChronicDiseaseRegistrationAttachment
[ERROR]   location: class space.lzhq.ph.controller.ChronicDiseaseRegistrationAttachmentApiControllerTest
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/Chr
onicDiseaseRegistrationAttachmentApiControllerTest.java:[35,13] cannot find symbol
[ERROR]   symbol: class ChronicDiseaseRegistrationAttachmentApiController
[ERROR] -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException

```

---

看起来有很多编译错误，主要是因为ruoyi-admin模块没有直接依赖xydlfy-ph模块。让我先检查一下pom.xml文件，然后解决依赖问题：

---

Read file: ruoyi-admin/pom.xml

---

好的，我看到ruoyi-admin已经依赖了xydlfy-ph模块。问题是现在有很多其他的测试文件有编译错误。让我们只运行我们的特定测试，并忽略其他测试：

---

```bash
cd ruoyi-admin && mvn clean compile
```

```
rochuukyou@luzhongqiangdeiMac ~/I/z/ruoyi-admin (慢病申报) [1]> cd ruoyi-admin && mvn clean compile  (base) 
cd: The directory 'ruoyi-admin' does not exist
rochuukyou@luzhongqiangdeiMac ~/I/z/ruoyi-admin (慢病申报) [1]>                                      (base)
```

---

```bash
mvn clean compile
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------------< com.ruoyi:ruoyi-admin >------------------------
[INFO] Building ruoyi-admin 4.7.7
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ ruoyi-admin ---
[INFO] Deleting /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/target
[INFO] 
[INFO] --- dependency:3.8.0:properties (properties) @ ruoyi-admin ---
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-admin ---
[INFO] Copying 451 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- closure-compiler:2.25.0:minify (minify-js) @ ruoyi-admin ---
[INFO] Starting JavaScript task:
[INFO] Skipping the merge step...
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [index.js].
[INFO] Uncompressed size: 24045 bytes.
[INFO] Compressed size: 14588 bytes minified (4034 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [common.js].
[INFO] Uncompressed size: 23954 bytes.
[INFO] Compressed size: 13785 bytes minified (4831 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [ry-ui.js].
5月 24, 2025 1:57:48 下午 com.google.javascript.jscomp.LoggerErrorManager println
WARNING: ruoyi/js/ry-ui.js:1624:20: WARNING - [JSC_SUSPICIOUS_INSTANCEOF_LEFT] "instanceof" with left non-ob
ject operand is always false.
  1624|                 if (!date instanceof Date) return;
                            ^^^^^^^^^^^^^^^^^^^^^

5月 24, 2025 1:57:48 下午 com.google.javascript.jscomp.LoggerErrorManager printSummary
WARNING: 0 error(s), 1 warning(s)
[WARNING] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/resources/static/ruoyi/js/ry-ui
.js [1624:20]: WARNING - "instanceof" with left non-object operand is always false.

[INFO] Uncompressed size: 86700 bytes.
[INFO] Compressed size: 34692 bytes minified (9848 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [login.js].
[INFO] Uncompressed size: 2754 bytes.
[INFO] Compressed size: 1512 bytes minified (860 bytes gzipped).
[INFO] Processed 4 output files
[INFO] Created 4 output files
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ ruoyi-admin ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 32 source files with javac [debug release 21] to target/classes
[INFO] Annotation processing is enabled because one or more processors were found
  on the class path. A future release of javac may disable annotation processing
  unless at least one processor is specified by name (-processor), or a search
  path is specified (--processor-path, --processor-module-path), or annotation
  processing is enabled explicitly (-proc:only, -proc:full).
  Use -Xlint:-options to suppress this message.
  Use -proc:none to disable annotation processing.
[INFO] Some messages have been simplified; recompile with -Xdiags:verbose to get full output
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/com
mon/CommonController.java:[80,38] cannot find symbol
  symbol:   method getBaseUrl()
  location: variable serverConfig of type com.ruoyi.common.config.ServerConfig
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/com
mon/CommonController.java:[108,42] cannot find symbol
  symbol:   method getBaseUrl()
  location: variable serverConfig of type com.ruoyi.common.config.ServerConfig
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/sys
tem/SysLoginController.java:[89,56] incompatible types: java.time.LocalDate cannot be converted to java.util
.Date
[INFO] 3 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  4.218 s
[INFO] Finished at: 2025-05-24T13:57:49+09:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.13.0:compile (default-compil
e) on project ruoyi-admin: Compilation failure: Compilation failure:
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/com
mon/CommonController.java:[80,38] cannot find symbol
[ERROR]   symbol:   method getBaseUrl()
[ERROR]   location: variable serverConfig of type com.ruoyi.common.config.ServerConfig
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/com
mon/CommonController.java:[108,42] cannot find symbol
[ERROR]   symbol:   method getBaseUrl()
[ERROR]   location: variable serverConfig of type com.ruoyi.common.config.ServerConfig
[ERROR] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/sys
tem/SysLoginController.java:[89,56] incompatible types: java.time.LocalDate cannot be converted to java.util
.Date
[ERROR] -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException

```

---

看起来整个项目都有一些编译问题，这超出了我们当前需要解决的范围。让我采用一个更直接的方法：在xydlfy-ph模块中创建一个简化的测试，但使用@SpringBootTest注解并指定主配置类。

---

```bash
cd ..
```

---

