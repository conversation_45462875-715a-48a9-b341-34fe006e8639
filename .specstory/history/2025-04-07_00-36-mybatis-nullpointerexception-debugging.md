<!-- Generated by SpecStory -->

# MyBatis NullPointerException Debugging (2025-04-07 09:36:12)

_**User**_

接口报错：
```
org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: java.lang.NullPointerException: Name is null
### The error may exist in URL [jar:nested:/D:/zyxcx/zyxcx.jar/!BOOT-INF/lib/xydlfy-ph-1.0.0.jar!/mapper/ph/WxPaymentMapper.xml]
### The error may involve space.lzhq.ph.mapper.WxPaymentMapper.selectWxPaymentList_COUNT
### The error occurred while handling results
### SQL: SELECT count(0) FROM ph_wx_payment
### Cause: java.lang.NullPointerException: Name is null
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy221.selectWxPaymentList(Unknown Source)
	at space.lzhq.ph.service.impl.WxPaymentServiceImpl.selectWxPaymentList(WxPaymentServiceImpl.java:87)
	at space.lzhq.ph.controller.WxPaymentAdminController.list(WxPaymentAdminController.kt:61)
```

---

_**Assistant**_

Let me help analyze this error and provide a solution.

First, let me explain the error in both English and Chinese:

**English**:
The error indicates a NullPointerException when querying the database, specifically when executing the count query for pagination. The error message "Name is null" suggests there might be an issue with the MyBatis mapper configuration or the way parameters are being handled.

**中文**:
这个错误表明在查询数据库时出现了空指针异常，特别是在执行分页查询的count语句时。错误信息"Name is null"表明可能是MyBatis映射配置或参数处理方面存在问题。

Let me search the codebase to understand more about the implementation:

---

<details>
            <summary>Searched codebase "WxPayment domain class definition" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxPayment.java` | L1-102 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxPaymentAdminController.kt` | L1-35 |
| `xydlfy-ph/src/main/resources/mapper/ph/WxPaymentMapper.xml` | L1-52 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/ext/WeixinExt.kt` | L1-48 |
| `ph-common/src/main/kotlin/space/lzhq/ph/common/WeixinServices.kt` | L1-21 |
| `ddl/ph/ph_wx_payment.sql` | L1-37 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxRefund.java` | L1-105 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/WxPaymentServiceImpl.java` | L209-260 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxPaymentAdminController.kt` | L35-102 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L1-45 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/IWxRefundService.java` | L1-84 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/ext/WeixinExt.kt` | L48-92 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/IWxPaymentService.java` | L1-78 |
| `xydlfy-ph/src/main/resources/mapper/ph/WxPaymentMapper.xml` | L149-188 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxTransferApiController.kt` | L1-27 |
| `ph-common/src/main/kotlin/space/lzhq/ph/common/PaymentKit.kt` | L1-46 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxTransfer.java` | L1-83 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/IWxPaymentService.java` | L78-149 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L201-247 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/WxPaymentServiceImpl.java` | L1-43 |
| `xydlfy-ph/src/main/resources/mapper/ph/WxPaymentMapper.xml` | L105-127 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/WxPaymentServiceImpl.java` | L101-171 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/WxCheckDetail.java` | L1-98 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/mapper/WxPaymentMapper.java` | L1-75 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/WxPaymentServiceImpl.java` | L260-300 |

</details>

---

Based on the error message and code analysis, I can identify the issue:

**Problem Analysis**:
1. The error occurs in the `selectWxPaymentList_COUNT` query
2. The error message indicates a `NullPointerException` with "Name is null"
3. Looking at the `WxPaymentMapper.xml`, there's a potential issue with the parameter handling in the WHERE clause

**Root Cause**:
The issue appears to be related to the MyBatis parameter handling in the mapper XML file. The error occurs when trying to access a parameter name that is null during the COUNT query generation.

Here's the solution:

**English**:
1. The issue is in the `WxPaymentMapper.xml` file's `selectWxPaymentList` query
2. We need to add null checks for the parameters in the WHERE clause
3. The params map access needs to be properly handled

**中文**:
1. 问题出在 `WxPaymentMapper.xml` 文件的 `selectWxPaymentList` 查询中
2. 我们需要在 WHERE 子句中添加参数的空值检查
3. 需要正确处理 params 映射的访问

Here's the fix:

```xml:xydlfy-ph/src/main/resources/mapper/ph/WxPaymentMapper.xml
<select id="selectWxPaymentList" parameterType="WxPayment" resultMap="WxPaymentResult">
    <include refid="selectWxPaymentVo"/>
    <where>
        <if test="patientNo != null and patientNo != ''">and patient_no = #{patientNo}</if>
        <if test="jzCardNo != null and jzCardNo != ''">and jz_card_no = #{jzCardNo}</if>
        <if test="zhuyuanNo != null and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
        <if test="zyPayNo != null and zyPayNo != ''">and zy_pay_no = #{zyPayNo}</if>
        <if test="wxPayNo != null and wxPayNo != ''">and wx_pay_no = #{wxPayNo}</if>
        <if test="manualPayState != null">and manual_pay_state = #{manualPayState}</if>
        <if test="wxTradeStatus != null and wxTradeStatus != ''">and wx_trade_status = #{wxTradeStatus}</if>
        <if test="hisTradeStatus != null and hisTradeStatus != ''">and his_trade_status = #{hisTradeStatus}</if>
        <if test="hisReceiptNo != null and hisReceiptNo != ''">and his_receipt_no = #{hisReceiptNo}</if>
        <if test="settlementIds != null and settlementIds != ''">and settlement_ids = #{settlementIds}</if>
        <if test="params != null">
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="params.filterErrorOrder != null and params.filterErrorOrder != ''">
                and (wx_trade_status = '支付成功' and his_trade_status != '充值成功')
            </if>
            <if test="params.wxTradeStatusIsUnknown != null and params.wxTradeStatusIsUnknown == true">
                and (wx_trade_status is null or wx_trade_status = '')
            </if>
        </if>
    </where>
    order by id desc
</select>
```

Key changes made:
1. Added a null check for the `params` object
2. Added more strict null checks for all parameters
3. Properly nested the params-related conditions

**Implementation Steps**:
1. Update the `WxPaymentMapper.xml` file with the above changes
2. Rebuild and redeploy the application

**Additional Recommendations**:
1. Add proper validation in the service layer before passing parameters to the mapper
2. Consider adding logging to track parameter values in case of future issues
3. Consider using MyBatis-Plus's built-in wrapper for more type-safe queries

Let me know if you need any clarification or have questions about the implementation!

---

