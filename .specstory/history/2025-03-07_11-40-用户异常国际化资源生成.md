<!-- Generated by SpecStory -->

# 用户异常国际化资源生成 (2025-03-07 20:40:13)

_**User**_

为这些异常生成国际化资源

---

_**Assistant**_

<think>我需要为这些用户异常类创建国际化资源文件。从提供的代码片段中，我可以看到有多个继承自`UserException`的异常类，需要为它们提供国际化消息。这些异常类包括：

1. CaptchaException.java
2. UserPasswordNotMatchException.java
3. RoleBlockedException.java
4. UserPasswordRetryLimitCountException.java
5. UserPasswordRetryLimitExceedException.java
6. UserDeleteException.java
7. UserBlockedException.java
8. UserNotExistsException.java
9. BlackListException.java

我需要查看这些类的实现，了解它们的消息代码，然后为这些消息代码添加相应的国际化资源。

我看到已经有一个空的`messages_zh_CN.properties`文件，这应该是要填充中文消息的国际化资源文件。我需要查看这些异常类的详细内容来找出它们所使用的消息代码。</think>我需要为这些用户相关的异常类创建国际化资源。首先，让我了解这些异常类的具体定义，以便为它们创建正确的国际化消息。

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserException.java

---

现在，让我检查单个异常类的实现，了解它们的消息代码：

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/CaptchaException.java

---

让我继续检查其他异常类：

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserPasswordNotMatchException.java

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/RoleBlockedException.java

---

让我继续检查剩余的异常类：

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserPasswordRetryLimitCountException.java

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserPasswordRetryLimitExceedException.java

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserDeleteException.java

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserBlockedException.java

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserNotExistsException.java

---

Read file: ruoyi-common/src/main/java/com/ruoyi/common/exception/user/BlackListException.java

---

现在，我已经收集了所有异常类的消息代码。接下来，我需要创建中文国际化资源文件。

以下是我从异常类中收集到的消息代码：
1. user.jcaptcha.error (验证码错误)
2. user.password.not.match (用户密码不正确)
3. role.blocked (角色已锁定)
4. user.password.retry.limit.count (用户密码尝试次数)
5. user.password.retry.limit.exceed (用户密码尝试次数超限)
6. user.password.delete (用户已删除)
7. user.blocked (用户已锁定)
8. user.not.exists (用户不存在)
9. login.blocked (登录黑名单)

现在，我将编辑 messages_zh_CN.properties 文件，添加这些国际化消息：

---

```diff
- 
+ # 验证码错误
+ user.jcaptcha.error=验证码错误
+ # 用户密码错误
+ user.password.not.match=用户不存在/密码错误
+ # 角色锁定
+ role.blocked=角色已被锁定，请联系管理员
+ # 用户密码尝试次数
+ user.password.retry.limit.count=密码输入错误{0}次，剩余{1}次将会被锁定
+ # 用户密码尝试次数超限
+ user.password.retry.limit.exceed=密码输入错误{0}次，账户已被锁定，请联系管理员
+ # 用户已删除
+ user.password.delete=对不起，您的账号已被删除
+ # 用户已锁定
+ user.blocked=用户已被锁定，请联系管理员
+ # 用户不存在
+ user.not.exists=用户不存在
+ # 登录黑名单
+ login.blocked=很抱歉，您的IP已被列入黑名单，请联系管理员
+ 
```

---

现在，让我检查一下是否还需要创建英文国际化资源文件：

---

<details>
            <summary>Listed directory ruoyi-admin/src/main/resources • **25** results</summary>
        
| Name |
|-------|
| 📄 `messages_zh_CN.properties` |
| 📄 `bsoft.setting` |
| 📄 `1600097839_cert.pem` |
| 📄 `1600097839_key.pem` |
| 📄 `application.yml` |
| 📁 `templates` |
| 📄 `logback.xml` |
| 📄 `application-druid.yml` |
| 📄 `application-prod.yml` |
| 📄 `bsoft-hai.setting` |
| 📄 `weicity-test.setting` |
| 📄 `weicity.setting` |
| 📄 `zktec.setting` |
| 📁 `static` |
| 📁 `ehcache` |
| 📄 `erhc.setting` |
| 📄 `guoguang.setting` |
| 📄 `his.setting` |
| 📄 `invoice.setting` |
| 📁 `mybatis` |
| 📄 `shine.setting` |
| 📄 `1600097839.p12` |
| 📄 `alipay.setting` |
| 📄 `banner.txt` |
| 📁 `cert` |

</details>

---

让我查看是否还有英文或其他语言的资源文件：

---

<details>
            <summary>Grep search for "messages_en" • **0** files</summary>
        
No results found
</details>

---

现在，我还需要为英文版本创建资源文件。我将创建 messages.properties（默认资源文件）和 messages_en_US.properties（英文资源文件）：

---

```diff
- 
+ # Captcha error
+ user.jcaptcha.error=Invalid verification code
+ # User password error
+ user.password.not.match=User does not exist/Wrong password
+ # Role blocked
+ role.blocked=The role has been blocked, please contact the administrator
+ # User password retry count
+ user.password.retry.limit.count=Password input error {0} times, remaining {1} times will be locked
+ # User password retry limit exceeded
+ user.password.retry.limit.exceed=Password input error {0} times, the account has been locked, please contact the administrator
+ # User deleted
+ user.password.delete=Sorry, your account has been deleted
+ # User blocked
+ user.blocked=User has been blocked, please contact the administrator
+ # User does not exist
+ user.not.exists=User does not exist
+ # Login blacklist
+ login.blocked=Sorry, your IP has been blacklisted, please contact the administrator 
```

---

```diff
- 
+ # Captcha error
+ user.jcaptcha.error=Invalid verification code
+ # User password error
+ user.password.not.match=User does not exist/Wrong password
+ # Role blocked
+ role.blocked=The role has been blocked, please contact the administrator
+ # User password retry count
+ user.password.retry.limit.count=Password input error {0} times, remaining {1} times will be locked
+ # User password retry limit exceeded
+ user.password.retry.limit.exceed=Password input error {0} times, the account has been locked, please contact the administrator
+ # User deleted
+ user.password.delete=Sorry, your account has been deleted
+ # User blocked
+ user.blocked=User has been blocked, please contact the administrator
+ # User does not exist
+ user.not.exists=User does not exist
+ # Login blacklist
+ login.blocked=Sorry, your IP has been blacklisted, please contact the administrator 
```

---

我已经成功创建了用户异常的国际化资源文件，包括：

1. messages_zh_CN.properties（中文）
2. messages.properties（默认，英文）
3. messages_en_US.properties（英文-美国）

在这些文件中，我添加了以下异常消息的国际化文本：

1. user.jcaptcha.error - 验证码错误异常
2. user.password.not.match - 用户密码不匹配异常
3. role.blocked - 角色锁定异常
4. user.password.retry.limit.count - 用户密码重试次数异常
5. user.password.retry.limit.exceed - 用户密码重试超限异常
6. user.password.delete - 用户已删除异常
7. user.blocked - 用户已锁定异常
8. user.not.exists - 用户不存在异常
9. login.blocked - 登录黑名单异常

这些国际化资源文件将使系统能够根据用户的语言设置显示相应的错误消息，从而提高系统的用户体验和国际化适应性。

需要注意的是，对于带参数的消息（如user.password.retry.limit.count和user.password.retry.limit.exceed），我使用了{0}、{1}等占位符，这些将在运行时被实际值替换。

以上资源文件配置完成后，系统将能够自动根据用户的语言设置选择合适的错误消息。

所有异常的国际化资源已成功配置完毕。

---

