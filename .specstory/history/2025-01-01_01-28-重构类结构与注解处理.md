<!-- Generated by SpecStory -->

# 重构类结构与注解处理 (2025-01-01 10:28:35)

_**User**_

这三个类的字段几乎完全一致，考虑重构，为它们添加一个公共的父类，注意处理好MybatisPlus相关的注解（`@TableName`、`@TableId`），和FastExcel相关的注解（`@ExcelProperty`、`@ColumnWidth`）

---

_**Assistant**_

我来帮您重构这三个类。我们可以创建一个抽象基类来包含共同的字段和方法。

首先创建基类：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/domain/BaseReconciliation.java
package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

@ExcelIgnoreUnannotated
public abstract class BaseReconciliation {
    @TableId("date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = {"账单日"})
    @ColumnWidth(12)
    protected LocalDate date;

    @ExcelProperty(value = {"充值", "HIS"})
    protected BigDecimal hisPayAmount;

    @ExcelProperty(value = {"充值", "平账？"})
    protected Integer payBalanced;

    @ExcelProperty(value = {"退款", "HIS"})
    protected BigDecimal hisRefundAmount;

    @ExcelProperty(value = {"退款", "平账？"})
    protected Integer refundBalanced;

    @ExcelProperty(value = {"合计", "HIS"})
    protected BigDecimal hisTotalAmount;

    @ExcelProperty(value = {"合计", "平账？"})
    protected Integer totalBalanced;

    // 所有getter和setter方法
    // ...

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        BaseReconciliation that = (BaseReconciliation) o;
        return getDate().equals(that.getDate());
    }

    @Override
    public int hashCode() {
        return getDate().hashCode();
    }
}
```

然后修改三个子类：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/domain/GuoguangATMReconciliation.java
@TableName(value = "reconciliation_guoguang_atm")
public class GuoguangATMReconciliation extends BaseReconciliation {
    public static final String POS_ID = "067507881";

    @ExcelProperty(value = {"充值", "自助机"})
    private BigDecimal atmPayAmount;

    @ExcelProperty(value = {"充值", "自助机-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffPayAmount;

    @ExcelProperty(value = {"退款", "自助机"})
    private BigDecimal atmRefundAmount;

    @ExcelProperty(value = {"退款", "自助机-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffRefundAmount;

    @ExcelProperty(value = {"合计", "自助机"})
    private BigDecimal atmTotalAmount;

    @ExcelProperty(value = {"合计", "自助机-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffTotalAmount;

    // getter和setter方法
    // ...
}
```

```java:xydlfy-ph/src/main/java/space/lzhq/ph/domain/ZhangyiWeixinReconciliation.java
@TableName(value = "reconciliation_zhangyi_weixin")
public class ZhangyiWeixinReconciliation extends BaseReconciliation {
    public static final String POS_ID = "047298390";

    @ExcelProperty(value = {"充值", "掌医"})
    private BigDecimal appPayAmount;

    @ExcelProperty(value = {"充值", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffPayAmount;

    @ExcelProperty(value = {"退款", "掌医"})
    private BigDecimal appRefundAmount;

    @ExcelProperty(value = {"退款", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffRefundAmount;

    @ExcelProperty(value = {"合计", "掌医"})
    private BigDecimal appTotalAmount;

    @ExcelProperty(value = {"合计", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffTotalAmount;

    // getter和setter方法
    // ...
}
```

```java:xydlfy-ph/src/main/java/space/lzhq/ph/domain/ZhangyiAlipayReconciliation.java
@TableName(value = "reconciliation_zhangyi_alipay")
public class ZhangyiAlipayReconciliation extends BaseReconciliation {
    @ExcelProperty(value = {"充值", "掌医"})
    private BigDecimal appPayAmount;

    @ExcelProperty(value = {"充值", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffPayAmount;

    @ExcelProperty(value = {"退款", "掌医"})
    private BigDecimal appRefundAmount;

    @ExcelProperty(value = {"退款", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffRefundAmount;

    @ExcelProperty(value = {"合计", "掌医"})
    private BigDecimal appTotalAmount;

    @ExcelProperty(value = {"合计", "掌医-HIS"})
    @ColumnWidth(12)
    private BigDecimal diffTotalAmount;

    // getter和setter方法
    // ...
}
```

主要改动说明：

1. 创建了一个抽象基类 `BaseReconciliation`，包含了所有共同的字段和方法
2. 将共同的注解（`@ExcelIgnoreUnannotated`）移到基类
3. 每个子类保留自己的 `@TableName` 注解
4. 子类只需要定义特有的字段，比如 `atmPayAmount`/`appPayAmount` 等
5. 将 `equals()` 和 `hashCode()` 方法移到基类
6. 基类中的字段使用 protected 访问修饰符，这样子类可以直接访问

这样重构后的代码更加简洁，减少了重复代码，同时保持了原有的功能。每个子类只需要关注自己特有的字段，共同的字段和行为都由基类提供。

---

