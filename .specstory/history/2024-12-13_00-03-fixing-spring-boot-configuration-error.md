<!-- Generated by SpecStory -->

# Fixing Spring Boot Configuration Error (2024-12-13 09:03:33)

_**User**_

```
Caused by: org.springframework.boot.context.properties.source.InvalidConfigurationPropertyNameException: Configuration property name 'spring.datasource.druid.hisOrderView' is not valid
	at org.springframework.boot.context.properties.source.ConfigurationPropertyName.elementsOf(ConfigurationPropertyName.java:633)
	at org.springframework.boot.context.properties.source.ConfigurationPropertyName.elementsOf(ConfigurationPropertyName.java:610)
	at org.springframework.boot.context.properties.source.ConfigurationPropertyName.of(ConfigurationPropertyName.java:601)
	at org.springframework.boot.context.properties.source.ConfigurationPropertyName.of(ConfigurationPropertyName.java:578)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:256)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:94)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:96)
```
修复这个错误

---

_**Assistant**_

这个错误是因为 Spring Boot 配置属性名称中不支持驼峰命名法。我们需要将 `hisOrderView` 改为使用短横线分隔的形式 `his-order-view`。

以下是需要修改的部分：

```yaml:ruoyi-admin/src/main/resources/application-druid.yml
// ... existing code ...
      lis:
        # 从数据源开关/默认关闭
        enabled: true
        url: *************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: HIS
        password: HIS
      his-order-view:    # 将 hisOrderView 改为 his-order-view
        enabled: true
        url: **************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: dzpt
        password: dzpt
// ... existing code ...
```

修改说明：
- 将配置属性名 `hisOrderView` 改为 `his-order-view`
- 其他配置保持不变
- 请确保在 Java 代码中相应的属性名也做相应调整

这样修改后，Spring Boot 就能正确识别这个配置属性了。

---

_**User**_

```
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 3000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
```
但我看到这里有很多属性都是驼峰命名

---

_**Assistant**_

这是一个很好的观察。实际上，这个错误的真正原因可能不是驼峰命名的问题。Druid 数据源确实支持驼峰命名的配置属性。

问题可能出在配置结构上。对于多数据源配置，我们需要确保每个数据源都有完整的连接池配置。让我们尝试修改配置结构：

```yaml:ruoyi-admin/src/main/resources/application-druid.yml
      hisOrderView:
        enabled: true
        url: **************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: dzpt
        password: dzpt
        # 添加连接池配置
        initialSize: 5
        minIdle: 10
        maxActive: 20
        maxWait: 3000
        connectTimeout: 30000
        socketTimeout: 60000
        validationQuery: SELECT 1 FROM DUAL
```

修改说明：
- 保持原有的驼峰命名 `hisOrderView`
- 为这个数据源添加必要的连接池配置参数
- 每个数据源应该有自己的连接池配置

如果这样修改后还是不行，建议检查：
1. 相关的 Java 配置类是否正确定义了对应的属性
2. 是否正确引入了 Druid 的依赖
3. 是否有正确的配置类来处理这些属性

你能分享一下处理多数据源的 Java 配置类吗？这样我可以更准确地找出问题所在。

---

_**User**_

代码中定义的表名是 `V_DZPT_DZMB`，似乎在哪里被加上了 `ph.`前缀：
```
Caused by: java.sql.SQLSyntaxErrorException: Table 'ph.v_dzpt_dzmb' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:113)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:938)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:359)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy134.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy132.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy131.query(Unknown Source)
	at com.github.pagehelper.util.ExecutorUtil.executeAutoCount(ExecutorUtil.java:169)
	at com.github.pagehelper.PageInterceptor.count(PageInterceptor.java:244)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:153)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy131.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
```

---

_**Assistant**_



这个错误是因为 MyBatis-Plus 默认使用了当前数据源（master）的 schema 作为前缀。我们需要在实体类上明确指定完整的表名，包括 schema。

修改 `HisSelfPayOrder.java` 的 `@TableName` 注解：

```java
@TableName(value = "V_DZPT_DZMB", schema = "dzpt")
public class HisSelfPayOrder {
    // ... 其他代码保持不变
}
```

或者，如果你不想指定 schema，可以在数据源配置中修改 URL，在 URL 中指定默认 schema：

```yaml
      his-order-view:
        enabled: true
        url: *********************************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: dzpt
        password: dzpt
```

另外，确保你的多数据源配置中正确设置了 MyBatis-Plus 的数据库类型，因为 Oracle 和 MySQL 的表名处理方式不同。在数据源配置类中添加：

```java
@Bean
public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
    // 设置数据库类型为 Oracle
    interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.ORACLE));
    return interceptor;
}
```

这些修改应该能解决表名前缀的问题。

---

