<!-- Generated by SpecStory -->

# 生成数据表结构的Markdown文档 (2025-03-20 18:59:33)

_**User**_

根据这两张表的字段，生成描述数据表结构的markdown文档

---

_**Assistant**_



---

# LIS系统数据表结构文档

## 1. V_LIS_REPORT 检验报告主表

该表存储检验报告的基本信息。

| 字段名 | 数据类型 | 描述 |
| ------ | ------ | ------ |
| ID | VARCHAR | 报告ID，主键 |
| NAME | VARCHAR | 患者姓名 |
| OUTPATIENT_ID | VARCHAR | 门诊ID/患者ID |
| TYPE | VARCHAR | 报告类型 |
| GENDER | VARCHAR | 患者性别 |
| PURPOSE | VARCHAR | 检查目的 |
| EXAM_TIME | DATETIME | 检查时间 |
| REPORT_TIME | DATETIME | 报告时间 |

## 2. V_LIS_REPORT_ITEM 检验报告明细表

该表存储检验报告中的具体检验项目及结果。

| 字段名 | 数据类型 | 描述 |
| ------ | ------ | ------ |
| REPORT_ID | VARCHAR | 报告ID，关联V_LIS_REPORT表的ID |
| ITEM_NAME | VARCHAR | 检验项目名称 |
| RESULT_VALUE | VARCHAR | 检验结果值 |
| UNIT | VARCHAR | 计量单位 |
| TEST_ITEM_REFERENCE | VARCHAR | 参考值范围 |

## 表关系

V_LIS_REPORT和V_LIS_REPORT_ITEM通过REPORT_ID字段关联，形成一对多关系：
- 一份检验报告(V_LIS_REPORT)可以包含多个检验项目(V_LIS_REPORT_ITEM)
- 每个检验项目(V_LIS_REPORT_ITEM)属于一份特定的检验报告(V_LIS_REPORT)

# LIS System Database Structure Documentation

## 1. V_LIS_REPORT Test Report Main Table

This table stores basic information about laboratory test reports.

| Field Name | Data Type | Description |
| ------ | ------ | ------ |
| ID | VARCHAR | Report ID, primary key |
| NAME | VARCHAR | Patient name |
| OUTPATIENT_ID | VARCHAR | Outpatient ID/Patient ID |
| TYPE | VARCHAR | Report type |
| GENDER | VARCHAR | Patient gender |
| PURPOSE | VARCHAR | Examination purpose |
| EXAM_TIME | DATETIME | Examination time |
| REPORT_TIME | DATETIME | Report time |

## 2. V_LIS_REPORT_ITEM Test Report Detail Table

This table stores specific test items and results in the test report.

| Field Name | Data Type | Description |
| ------ | ------ | ------ |
| REPORT_ID | VARCHAR | Report ID, relates to the ID field in V_LIS_REPORT |
| ITEM_NAME | VARCHAR | Test item name |
| RESULT_VALUE | VARCHAR | Test result value |
| UNIT | VARCHAR | Unit of measurement |
| TEST_ITEM_REFERENCE | VARCHAR | Reference range |

## Table Relationship

V_LIS_REPORT and V_LIS_REPORT_ITEM are related through the REPORT_ID field, forming a one-to-many relationship:
- One test report (V_LIS_REPORT) can include multiple test items (V_LIS_REPORT_ITEM)
- Each test item (V_LIS_REPORT_ITEM) belongs to one specific test report (V_LIS_REPORT)

---

