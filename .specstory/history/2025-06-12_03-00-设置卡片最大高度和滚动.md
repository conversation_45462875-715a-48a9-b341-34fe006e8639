<!-- Generated by SpecStory -->

# 设置卡片最大高度和滚动 (2025-06-12 12:00:06)

_**User**_

设置`申报进度`卡片的最大高度为750px，如果内容超出最大高度，卡片内部可以纵向滚动

---

_**Assistant**_



---

我来帮您设置申报进度卡片的最大高度和滚动功能。让我先查看一下这个模板文件的完整内容，了解当前的样式设置。

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---

让我继续查看模板文件的其余部分，特别是关于申报进度卡片的样式：

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---

现在让我查看申报进度卡片的部分：

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---

现在我已经了解了申报进度卡片的结构。我需要为这个特定的卡片添加最大高度限制和滚动功能。我将在现有的 CSS 样式中添加针对申报进度卡片的特定样式。

---



---

现在我需要为申报进度卡片添加 CSS 类，以确保在不支持 `:has()` 选择器的浏览器中也能正常工作：

---



---



---

完成！我已经成功为申报进度卡片设置了最大高度限制和滚动功能。让我总结一下所做的修改：

## 修改内容总结：

### 1. CSS 样式添加
我在现有的样式中添加了以下CSS规则：

```css
/* 申报进度卡片高度限制和滚动样式 */
.detail-card:has(.timeline) {
    max-height: 750px;
    overflow-y: auto;
}

/* 兼容不支持 :has() 选择器的浏览器 */
.detail-card.timeline-progress-card {
    max-height: 750px;
    overflow-y: auto;
}

/* 优化滚动条样式 */
.detail-card:has(.timeline)::-webkit-scrollbar,
.detail-card.timeline-progress-card::-webkit-scrollbar {
    width: 8px;
}

.detail-card:has(.timeline)::-webkit-scrollbar-track,
.detail-card.timeline-progress-card::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.detail-card:has(.timeline)::-webkit-scrollbar-thumb,
.detail-card.timeline-progress-card::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.detail-card:has(.timeline)::-webkit-scrollbar-thumb:hover,
.detail-card.timeline-progress-card::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
```

### 2. HTML 结构调整
为申报进度卡片添加了 `timeline-progress-card` 类：

```html
<div class="detail-card timeline-progress-card">
```

## 功能特点：

1. **最大高度限制**：申报进度卡片的最大高度设置为 750px
2. **纵向滚动**：当内容超出最大高度时，卡片内部会显示纵向滚动条
3. **跨浏览器兼容**：使用两种选择器确保在支持和不支持 `:has()` 选择器的浏览器中都能正常工作
4. **优化的滚动条样式**：自定义了滚动条的外观，使其更美观
   - 滚动条宽度：8px
   - 滚动条轨道：浅灰色背景，圆角边框
   - 滚动条滑块：深灰色，支持悬停效果

这样设置后，申报进度卡片在内容较多时不会占用过多页面空间，用户可以通过滚动查看完整的时间线内容。

---

_**User**_

随着页面宽度变小，患者信息卡片中附件部分的样式发生了错乱

---

_**Assistant**_



---

我来查看一下当前患者信息卡片中附件部分的样式，然后修复在小屏幕上的显示问题。

---

Read file: xydlfy-ph/src/main/resources/templates/ph/chronic-disease-registration/detail.html

---

现在我来分析问题并添加响应式的CSS规则来修复附件部分在小屏幕上的显示问题。我需要在现有的CSS中添加媒体查询和响应式样式：

---



---

现在我来优化整体页面的响应式布局，确保在小屏幕上整个页面都能良好显示：

---



---

完成！我已经为患者信息卡片中的附件部分添加了完整的响应式样式优化。现在让我总结一下修复的内容：

## 修复内容总结：

### 1. 附件显示优化
**原有问题：**
- 长文件名在小屏幕上挤压操作按钮
- 按钮在移动设备上太小，难以点击
- flex布局在小屏幕上显示混乱

**修复方案：**
```css
/* 基础优化 */
.attachment-info {
    flex: 1;
    min-width: 0; /* 允许flex项目收缩到内容以下 */
}
.attachment-info > div {
    word-break: break-all;
    overflow-wrap: break-word;
    line-height: 1.4;
}
.attachment-actions {
    margin-left: 10px;
    flex-shrink: 0; /* 防止按钮被压缩 */
}
```

### 2. 小屏幕响应式设计 (768px以下)
```css
@media (max-width: 768px) {
    .attachment-item {
        flex-direction: column; /* 垂直排列 */
        align-items: flex-start;
        gap: 10px;
    }
    
    .attachment-info {
        width: 100%;
    }
    
    .attachment-actions {
        margin-left: 0;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        gap: 5px;
    }
    
    .attachment-actions .btn {
        min-width: 44px; /* 提高移动端可点击性 */
        padding: 8px 12px;
    }
}
```

### 3. 超小屏幕优化 (480px以下)
```css
@media (max-width: 480px) {
    .attachment-actions {
        justify-content: center;
    }
    
    .attachment-actions .btn {
        flex: 1;
        max-width: 80px;
        margin: 0 2px;
    }
    
    .detail-card {
        padding: 15px; /* 减小内边距 */
    }
    
    .info-label, .info-value {
        font-size: 13px; /* 调整字体大小 */
        word-break: break-all; /* 处理长文本 */
    }
}
```

### 4. 整体页面布局优化
```css
@media (max-width: 991px) {
    .container-div .row .col-md-8,
    .container-div .row .col-md-4 {
        width: 100%;
        float: none;
        margin-bottom: 20px;
    }
    
    .detail-card.timeline-progress-card {
        max-height: 400px; /* 小屏幕上减少申报进度卡片高度 */
    }
}
```

### 5. 细节优化
```css
@media (max-width: 768px) {
    .detail-title {
        font-size: 16px;
    }
    
    .detail-title .pull-right {
        float: none !important;
        text-align: left;
        margin-top: 10px;
    }
    
    .switch-container {
        font-size: 12px;
    }
}
```

## 优化效果：

1. **文件名显示**：长文件名会自动换行，不再挤压按钮
2. **按钮布局**：在小屏幕上按钮垂直排列在附件信息下方
3. **可点击性**：移动端按钮增大到44px最小宽度，符合移动端设计规范
4. **整体布局**：页面在小屏幕上采用单列布局，避免水平滚动
5. **视觉层次**：保持良好的视觉层次和间距

现在附件部分在所有屏幕尺寸下都能正常显示，不会再出现样式错乱的问题。

---

