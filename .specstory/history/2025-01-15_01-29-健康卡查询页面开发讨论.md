<!-- Generated by SpecStory -->

# 健康卡查询页面开发讨论 (2025-01-15 10:29:20)

_**User**_

1. 开发一个页面。
2. 页面顶部有一个表单。表单中有一个证件类型(cardType)选择框（01表示居民身份证、03表示护照、10表示外国人永久居留身份证），一个证件号码(cardNo)输入框，一个查询按钮。
3. 点击查询按钮后，向以下地址发送GET请求（/ph/health-card/query），页面会显示查询结果。
4. 正常的返回结果如下：
```
{
  "msg": "操作成功",
  "code": 0,
  "data": {
    "birthday": "19880328",
    "nation": "01",
    "cardOrgName": "新疆维吾尔自治区人民医院",
    "idCode": "FEC938EC14E90D916C3371859509056DACC7D9FA581476715B075DC82E785E71",
    "sex": "1",
    "telephone": "",
    "validStartdate": "",
    "idCardTypeCode": "01",
    "scenePhoto": "",
    "empi": "531E8A43202E0FF139A81B822B1C037BBF895632B57A2433D8AFBAE376AA3878",
    "erhcCardNo": "6568F49C31EF30894A7267C8F3EC4048C5A3506760C5F1773708E944C282BC04",
    "linkman": "",
    "currentAddress": "新疆昌吉",
    "idInst": "",
    "validEnddate": "",
    "cardOrgCode": "457601797",
    "phone": "321A002AF2EEA690B74A448D35F0C445",
    "name": "9F4836AF6AF8977F6B9050C6FAF271AB",
    "decryptedLinkman": "",
    "decryptedName": "卢中强",
    "decryptedIdCode": "******************",
    "decryptedPhone": "15309949421",
    "decryptedTelephone": ""
  }
}
```

5. 异常的返回结果如下：
```
{
  "msg": "没有找到此人员信息!",
  "code": 500
}
```

6. 使用浏览器本地缓存保留最近10次查询记录，并显示在页面底部。
7. 使用bootstrap、jquery等技术，尽量保证页面美观。


---

_**Assistant**_

我会帮你创建一个健康卡查询页面。让我们分步骤来实现：

1. 首先创建HTML模板文件：

```html:templates/ph/health-card/index.html
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('健康卡查询')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <!-- 查询表单 -->
            <div class="col-sm-12 search-collapse">
                <form id="health-card-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>证件类型：</label>
                                <select name="cardType" id="cardType" class="form-control">
                                    <option value="01">居民身份证</option>
                                    <option value="03">护照</option>
                                    <option value="10">外国人永久居留身份证</option>
                                </select>
                            </li>
                            <li>
                                <label>证件号码：</label>
                                <input type="text" name="cardNo" id="cardNo" class="form-control"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="query()"><i class="fa fa-search"></i>&nbsp;查询</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <!-- 查询结果展示 -->
            <div class="col-sm-12" id="result-panel" style="display:none;">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>查询结果</h5>
                    </div>
                    <div class="ibox-content">
                        <table class="table table-bordered">
                            <tbody id="result-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 历史记录 -->
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>最近查询记录</h5>
                    </div>
                    <div class="ibox-content">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>证件类型</th>
                                    <th>证件号码</th>
                                    <th>姓名</th>
                                    <th>查询时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="history-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    
    <script th:inline="javascript">
        const CARD_TYPE_MAP = {
            '01': '居民身份证',
            '03': '护照',
            '10': '外国人永久居留身份证'
        };

        // 查询方法
        function query() {
            const cardType = $('#cardType').val();
            const cardNo = $('#cardNo').val();

            if (!cardNo) {
                $.modal.alertWarning('请输入证件号码');
                return;
            }

            $.ajax({
                url: ctx + 'ph/health-card/query',
                type: 'GET',
                data: {
                    cardType: cardType,
                    cardNo: cardNo
                },
                success: function(res) {
                    if (res.code === 0) {
                        showResult(res.data);
                        saveHistory({
                            cardType: cardType,
                            cardNo: cardNo,
                            name: res.data.decryptedName,
                            timestamp: new Date().getTime()
                        });
                    } else {
                        $.modal.alertError(res.msg);
                    }
                }
            });
        }

        // 显示查询结果
        function showResult(data) {
            const resultItems = [
                {label: '姓名', value: data.decryptedName},
                {label: '证件号码', value: data.decryptedIdCode},
                {label: '手机号', value: data.decryptedPhone},
                {label: '出生日期', value: data.birthday},
                {label: '性别', value: data.sex === '1' ? '男' : '女'},
                {label: '民族', value: data.nation},
                {label: '现住址', value: data.currentAddress},
                {label: '发卡机构', value: data.cardOrgName}
            ];

            let html = '';
            resultItems.forEach(item => {
                html += `<tr><td width="150"><strong>${item.label}：</strong></td><td>${item.value || '-'}</td></tr>`;
            });

            $('#result-body').html(html);
            $('#result-panel').show();
        }

        // 保存查询历史
        function saveHistory(record) {
            let history = JSON.parse(localStorage.getItem('healthCardHistory') || '[]');
            history.unshift(record);
            if (history.length > 10) {
                history = history.slice(0, 10);
            }
            localStorage.setItem('healthCardHistory', JSON.stringify(history));
            showHistory();
        }

        // 显示历史记录
        function showHistory() {
            const history = JSON.parse(localStorage.getItem('healthCardHistory') || '[]');
            let html = '';
            
            history.forEach(record => {
                html += `
                    <tr>
                        <td>${CARD_TYPE_MAP[record.cardType]}</td>
                        <td>${record.cardNo}</td>
                        <td>${record.name}</td>
                        <td>${new Date(record.timestamp).toLocaleString()}</td>
                        <td>
                            <a class="btn btn-xs btn-info" onclick="reQuery('${record.cardType}', '${record.cardNo}')">
                                重新查询
                            </a>
                        </td>
                    </tr>
                `;
            });

            $('#history-body').html(html);
        }

        // 重新查询
        function reQuery(cardType, cardNo) {
            $('#cardType').val(cardType);
            $('#cardNo').val(cardNo);
            query();
        }

        // 页面加载完成后显示历史记录
        $(function() {
            showHistory();
        });
    </script>
</body>
</html>
```

这个实现包含以下特点：

1. 使用Bootstrap的栅格系统和表单组件实现页面布局
2. 顶部查询表单包含证件类型下拉框和证件号码输入框
3. 中间部分显示查询结果，使用表格形式展示
4. 底部显示最近10次查询记录
5. 使用localStorage存储查询历史
6. 实现了重新查询功能
7. 适当的错误处理和提示

主要功能：

1. 查询表单：
   - 证件类型下拉框（居民身份证、护照、外国人永久居留身份证）
   - 证件号码输入框
   - 查询按钮

2. 查询结果显示：
   - 显示姓名、证件号码、手机号等信息
   - 使用表格形式展示，布局清晰

3. 历史记录功能：
   - 使用localStorage存储最近10次查询记录
   - 显示证件类型、证件号码、姓名、查询时间
   - 提供重新查询功能

4. 错误处理：
   - 输入验证
   - 错误提示

使用说明：
1. 页面依赖RuoYi框架的通用CSS和JS
2. 使用了Bootstrap的样式和组件
3. 使用jQuery处理DOM操作和AJAX请求
4. 使用localStorage实现本地存储

注意事项：
1. 确保页面所需的CSS和JS文件都已正确引入
2. 需要RuoYi框架的支持
3. 浏览器需要支持localStorage

这个实现提供了一个美观且功能完整的健康卡查询界面，支持查询、显示结果、保存历史记录等功能。

---

