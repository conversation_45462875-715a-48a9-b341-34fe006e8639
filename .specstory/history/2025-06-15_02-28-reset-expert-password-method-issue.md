<!-- Generated by SpecStory -->

# Reset expert password method issue (2025-06-15 11:28:14)

_**User**_

`resetExpertPassword` 实现有问题。这个方法只更新 user 的 salt 和 password 即可

---

_**Assistant**_



---

您说得对！`resetExpertPassword` 方法的实现确实有问题。当前实现调用了 `userService.updateUser(user)`，这个方法会处理角色和岗位的关联关系，但重置密码时我们只需要更新 `salt` 和 `password` 字段即可。

让我先查看一下相关的代码结构，然后修复这个问题。

---

<details>
            <summary>Searched codebase "updateUser mapper password salt 更新密码相关方法" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml` | L223-247 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysProfileController.java` | L66-111 |
| `ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml` | L247-278 |
| `ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml` | L278-286 |
| `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L248-327 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseExpertServiceImpl.java` | L146-207 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/service/SysPasswordService.java` | L1-25 |
| `ruoyi-system/src/main/java/com/ruoyi/system/mapper/SysUserMapper.java` | L86-127 |
| `ddl/ph/sys_user.sql` | L1-27 |
| `ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml` | L1-33 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/service/SysPasswordService.java` | L25-77 |
| `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L434-470 |
| `ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml` | L166-223 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ShiroUtils.java` | L1-67 |
| `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysUserController.java` | L177-223 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/util/ShiroUtils.java` | L1-68 |
| `ruoyi-admin/src/main/resources/templates/system/user/resetPwd.html` | L1-46 |
| `ruoyi-admin/src/main/resources/templates/system/user/profile/profile.html` | L144-156 |
| `ruoyi-common/src/main/java/com/ruoyi/common/exception/user/UserPasswordNotMatchException.java` | L1-19 |
| `ruoyi-admin/src/main/resources/templates/system/user/profile/profile.html` | L296-351 |
| `ruoyi-common/src/main/java/com/ruoyi/common/utils/ShiroUtils.java` | L67-74 |
| `ruoyi-admin/src/main/resources/templates/system/user/profile/resetPwd.html` | L1-22 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/util/ShiroUtils.java` | L68-75 |
| `ruoyi-admin/src/main/resources/templates/system/user/profile/profile.html` | L176-205 |
| `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/entity/SysUser.java` | L1-100 |

</details>

---

非常好！从搜索结果中可以看到，`ISysUserService` 接口中已经有专门的 `resetUserPwd` 方法，而且它内部调用的是 `updateUserInfo` 方法，而不是 `updateUser` 方法。让我查看一下这两个方法的区别：

---

<details>
            <summary>Grep search for "resetUserPwd|updateUserInfo" • **6** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysUserController.java` | L187 | `if (userService.resetUserPwd(user) > 0) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysProfileController.java` | L81 | `if (userService.resetUserPwd(user) > 0) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysProfileController.java` | L125 | `if (userService.updateUserInfo(currentUser) > 0) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysProfileController.java` | L145 | `if (userService.updateUserInfo(currentUser) > 0) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/ISysUserService.java` | L124 | `int updateUserInfo(SysUser user);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/ISysUserService.java` | L140 | `int resetUserPwd(SysUser user);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L249 | `public int updateUserInfo(SysUser user) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L273 | `public int resetUserPwd(SysUser user) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L274 | `return updateUserInfo(user);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/service/SysLoginService.java` | L215 | `userService.updateUserInfo(user);` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L249 | `public int updateUserInfo(SysUser user) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L273 | `public int resetUserPwd(SysUser user) {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java` | L274 | `return updateUserInfo(user);` |

</details>

---

完美！现在我看到了区别：

1. `resetUserPwd(user)` 方法调用的是 `updateUserInfo(user)`
2. `updateUserInfo(user)` 方法直接调用 `userMapper.updateUser(user)`，不处理角色和岗位关联
3. 而 `updateUser(user)` 方法会删除并重新插入角色和岗位关联关系

所以修复方案是将 `userService.updateUser(user)` 改为 `userService.resetUserPwd(user)`，或者更准确地说，改为 `userService.updateUserInfo(user)`。

让我修改这个方法：

---



---

很好！现在 `resetExpertPassword` 方法已经修复了。修改后的实现：

1. **使用专门的密码重置方法**：将 `userService.updateUser(user)` 改为 `userService.resetUserPwd(user)`
2. **避免不必要的操作**：`resetUserPwd` 方法内部调用的是 `updateUserInfo`，只更新用户基本信息，不会处理角色和岗位的关联关系
3. **符合单一职责原则**：重置密码只应该更新密码相关字段（salt 和 password），不应该影响其他用户信息

这样修改后的方法更加精确和高效，避免了不必要的数据库操作（删除和重新插入角色、岗位关联），也降低了潜在的副作用风险。

根据代码结构分析：
- `updateUser()` = 更新用户信息 + 重新设置角色岗位关联
- `resetUserPwd()` = `updateUserInfo()` = 只更新用户基本信息（包括密码）

所以使用 `resetUserPwd()` 是正确的选择。

---

_**User**_

从 `updateUser` 的SQL语句看，它除了更新密码和salt，也更新了许多其它无关的属性。我觉得我们还是单独做一个更新密码的方法更好。

---

_**Assistant**_



---

您说得非常对！从 `updateUser` 的 SQL 语句可以看出，即使通过 `resetUserPwd` 方法调用，最终还是会执行同一个 SQL，虽然 MyBatis 的动态 SQL 只会更新非空字段，但这样做确实不够精确和高效。

重置密码时，我们应该只更新密码相关的字段：`password`、`salt`、`pwd_update_date` 和 `update_time`。

让我们创建一个专门的更新密码方法：

---

Read file: ruoyi-system/src/main/java/com/ruoyi/system/mapper/SysUserMapper.java

---

现在让我们添加专门的更新密码方法。首先在 Mapper 接口中添加方法：

---



---

接下来在 XML 中添加对应的 SQL 语句：

---



---

现在添加服务接口方法：

---

Read file: ruoyi-system/src/main/java/com/ruoyi/system/service/ISysUserService.java

---



---



---

现在在服务实现类中添加方法：

---



---



---

最后，修改 `ChronicDiseaseExpertServiceImpl` 中的 `resetExpertPassword` 方法：

---



---



---

完美！现在我们已经完成了专门的密码更新方法的实现。让我总结一下我们所做的改进：

## 修改总结

### 1. **新增 `updateUserPassword` 方法**
- **Mapper 接口**：在 `SysUserMapper.java` 中添加了 `updateUserPassword` 方法声明
- **SQL 实现**：在 `SysUserMapper.xml` 中添加了专门的 SQL，只更新密码相关字段：
  ```sql
  <update id="updateUserPassword" parameterType="SysUser">
      update sys_user
      set password = #{password},
          salt = #{salt},
          pwd_update_date = sysdate(),
          update_time = sysdate()
      where user_id = #{userId}
  </update>
  ```

### 2. **服务层完整实现**
- **服务接口**：在 `ISysUserService.java` 中添加了 `updateUserPassword` 方法声明
- **服务实现**：在 `SysUserServiceImpl.java` 中添加了对应的实现方法

### 3. **业务层调用更新**
- 修改 `ChronicDiseaseExpertServiceImpl.resetExpertPassword` 方法，从 `userService.resetUserPwd(user)` 改为 `userService.updateUserPassword(user)`

## 优势对比

### 之前的实现问题：
- 调用 `updateUser()` 会检查和更新多达 15+ 个字段
- 即使使用 `resetUserPwd()` 方法，最终还是调用同一个复杂的 SQL
- 性能开销较大，语义不够明确

### 新实现的优势：
1. **精确性**：只更新密码相关的 4 个字段（`password`、`salt`、`pwd_update_date`、`update_time`）
2. **性能优化**：SQL 语句简洁，执行效率更高
3. **语义清晰**：方法名 `updateUserPassword` 明确表达了功能意图
4. **安全性**：减少了意外更新其他字段的风险
5. **维护性**：代码更容易理解和维护

这样的改进完全符合单一职责原则和 MyBatis-Plus 最佳实践，是一个很好的重构优化！

---

