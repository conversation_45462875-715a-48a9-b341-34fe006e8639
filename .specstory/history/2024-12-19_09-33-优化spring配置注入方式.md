<!-- Generated by SpecStory -->

# 优化Spring配置注入方式 (2024-12-19 18:33:39)

_**User**_

Configuration 是否有更好的注入方式

---

_**Assistant**_

是的,对于这种配置类,我们可以使用更好的注入方式。以下是几种改进建议:

1. 使用 `@ConfigurationProperties` 方式:

```java:xydlfy-ph/src/main/java/space/lzhq/ph/config/CCBProperties.java
@ConfigurationProperties(prefix = "ccb")
@Component
public class CCBProperties {
    private String merchantId;
    private String userId; 
    private String password;
    private String ebsUrl;
    private String billUrl;
    private Integer timeout;
    
    // getters and setters
}
```

```java:xydlfy-ph/src/main/java/space/lzhq/ph/service/CCBPayServiceExt.java
@Component
public class CCBPayServiceExt {
    
    private final CCBProperties ccbProperties;
    
    public CCBPayServiceExt(CCBProperties ccbProperties) {
        this.ccbProperties = ccbProperties;
    }

    private cn.yqtl.pay.ccb.Configuration getCcbConfig() {
        Charset charset = cn.yqtl.pay.ccb.Configuration.Companion.getDEFAULT_CHARSET();
        return new cn.yqtl.pay.ccb.Configuration(
            ccbProperties.getMerchantId(),
            ccbProperties.getUserId(),
            ccbProperties.getPassword(), 
            ccbProperties.getEbsUrl(),
            ccbProperties.getBillUrl(),
            charset,
            ccbProperties.getTimeout()
        );
    }
}
```

这种方式的优点:
1. 配置更集中,易于管理
2. 支持配置校验和元数据生成
3. 更好的类型安全
4. 减少重复的 `@Value` 注解
5. 便于测试和复用

记得在启动类或配置类上添加:
```java
@EnableConfigurationProperties(CCBProperties.class)
```

另外,如果 `Configuration` 对象不需要经常变化,还可以直接将其声明为 Bean:

```java:xydlfy-ph/src/main/java/space/lzhq/ph/config/CCBConfig.java
@Configuration
public class CCBConfig {
    
    @Bean
    public cn.yqtl.pay.ccb.Configuration ccbConfiguration(CCBProperties properties) {
        return new cn.yqtl.pay.ccb.Configuration(
            properties.getMerchantId(),
            properties.getUserId(),
            properties.getPassword(),
            properties.getEbsUrl(),
            properties.getBillUrl(),
            cn.yqtl.pay.ccb.Configuration.Companion.getDEFAULT_CHARSET(),
            properties.getTimeout()
        );
    }
}
```

这样在需要使用的地方直接注入 `Configuration` 即可:
```java
@Autowired
private cn.yqtl.pay.ccb.Configuration ccbConfiguration;
```

---

