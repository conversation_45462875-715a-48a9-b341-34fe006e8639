<!-- Generated by SpecStory -->

# 建行POS机对账重构方案讨论 (2025-05-11 15:06:38)

_**User**_

继续完成建行POS机渠道的对账重构

---

_**Assistant**_

好的，下面是建行POS机（CCB POS）渠道对账重构的详细方案和建议实现，完全对齐你在掌医微信渠道重构时的分层、策略、可扩展设计思路。

---

## 1. 匹配策略接口与实现

### 1.1 定义建行POS对账匹配策略接口实现

**接口复用**：直接使用`ReconciliationMatcher<A, D extends ReconciliationDetail>`。

### 1.2 实现建行POS匹配策略

```java
// src/main/java/space/lzhq/ph/reconciliation/matcher/impl/CcbPosMatcher.java
package space.lzhq.ph.reconciliation.matcher.impl;

import org.springframework.stereotype.Component;
import space.lzhq.ph.domain.CcbPosReconciliationDetail;
import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.reconciliation.matcher.ReconciliationMatcher;
import space.lzhq.ph.domain.CCBPosOrder;

import java.time.LocalDate;
import java.util.Map;

import static space.lzhq.ph.domain.ReconciliationDetail.*;

@Component
public class CcbPosMatcher implements ReconciliationMatcher<CCBPosOrder, CcbPosReconciliationDetail> {

    @Override
    public String getAppPaymentMatchKey(CCBPosOrder posOrder) {
        // 系统参考号作为支付匹配键
        return posOrder.getSystemRefNo();
    }

    @Override
    public String getAppRefundMatchKey(CCBPosOrder posOrder) {
        // 系统参考号作为退款匹配键
        return posOrder.getSystemRefNo();
    }

    @Override
    public String getHisPaymentMatchKey(HisSelfPayOrder hisOrder) {
        // 银行订单号作为支付匹配键
        return hisOrder.getBankOrderNo();
    }

    @Override
    public String getHisRefundMatchKey(HisSelfPayOrder hisOrder) {
        // 银行订单号作为退款匹配键
        return hisOrder.getBankOrderNo();
    }

    @Override
    public void mapAppOrder(CCBPosOrder posOrder, CcbPosReconciliationDetail detail, Map<String, CcbPosReconciliationDetail> paymentMap, Map<String, CcbPosReconciliationDetail> refundMap) {
        if (posOrder.isPay()) {
            paymentMap.put(getAppPaymentMatchKey(posOrder), detail);
        } else {
            refundMap.put(getAppRefundMatchKey(posOrder), detail);
        }
    }

    @Override
    public CcbPosReconciliationDetail findMatchDetail(HisSelfPayOrder hisOrder, Map<String, CcbPosReconciliationDetail> paymentMap, Map<String, CcbPosReconciliationDetail> refundMap) {
        if (hisOrder.isPayment()) {
            return paymentMap.get(getHisPaymentMatchKey(hisOrder));
        } else {
            return refundMap.get(getHisRefundMatchKey(hisOrder));
        }
    }

    @Override
    public CcbPosReconciliationDetail createDetail(LocalDate billDate) {
        return new CcbPosReconciliationDetail(billDate);
    }

    @Override
    public void updateHisInfo(CcbPosReconciliationDetail detail, HisSelfPayOrder hisOrder) {
        detail.setHisTradeTime(hisOrder.getTradeTime());
        detail.setHisTradeAmount(hisOrder.getTradeAmount());
        detail.setPatientId(hisOrder.getPatientId());
        detail.setPatientName(hisOrder.getPatientName());
        detail.setBusinessType(hisOrder.getRemark());
        detail.setCashier(hisOrder.getCashier());
        if (detail.getHisTradeAmount().compareTo(detail.getAppTradeAmount()) != 0) {
            detail.setResult(RESULT_AMOUNT_MISMATCH);
        } else {
            detail.setResult(RESULT_BALANCED);
        }
    }

    @Override
    public CcbPosReconciliationDetail createHisSurplusDetail(LocalDate billDate, HisSelfPayOrder hisOrder) {
        CcbPosReconciliationDetail detail = createDetail(billDate);
        detail.setTradeType(hisOrder.isPayment() ? TRADE_TYPE_PAY : TRADE_TYPE_REFUND);
        detail.setHisTradeTime(hisOrder.getTradeTime());
        detail.setHisTradeAmount(hisOrder.getTradeAmount());
        if (hisOrder.isPayment()) {
            detail.setBankOrderNo(hisOrder.getBankOrderNo());
        } else {
            detail.setAppOrderNo(hisOrder.getBankOrderNo());
        }
        detail.setPatientId(hisOrder.getPatientId());
        detail.setPatientName(hisOrder.getPatientName());
        detail.setBusinessType(hisOrder.getRemark());
        detail.setCashier(hisOrder.getCashier());
        detail.setResult(RESULT_HIS_SURPLUS);
        return detail;
    }
}
```

---

## 2. 服务实现重构

### 2.1 注入并使用匹配策略

在`CcbPosReconciliationServiceImpl`中注入`CcbPosMatcher`，并重构`processPosOrders`和`processHisOrders`方法，完全复用策略。

### 2.2 重构后的核心方法示例

```java
// 伪代码，实际请参考掌医微信重构后的结构
@Autowired
private CcbPosMatcher ccbPosMatcher;

private BaseReconciliation.ReconciliationResult<CcbPosReconciliationDetail> processReconciliationDetails(
        LocalDate billDate,
        List<CCBPosOrder> posOrders,
        List<HisSelfPayOrder> hisOrders
) {
    List<CcbPosReconciliationDetail> reconciliationDetails = new ArrayList<>();
    Map<String, CcbPosReconciliationDetail> paymentMap = new HashMap<>();
    Map<String, CcbPosReconciliationDetail> refundMap = new HashMap<>();

    // 处理POS账单
    BaseReconciliation.AmountSummary appSummary = processPosOrders(billDate, posOrders, reconciliationDetails, paymentMap, refundMap);

    // 处理HIS订单
    BaseReconciliation.AmountSummary hisSummary = processHisOrders(billDate, hisOrders, reconciliationDetails, paymentMap, refundMap);

    return new BaseReconciliation.ReconciliationResult<>(
            reconciliationDetails,
            appSummary.payAmount(),
            appSummary.refundAmount(),
            hisSummary.payAmount(),
            hisSummary.refundAmount()
    );
}

private BaseReconciliation.AmountSummary processPosOrders(
        LocalDate billDate, List<CCBPosOrder> posOrders,
        List<CcbPosReconciliationDetail> reconciliationDetails,
        Map<String, CcbPosReconciliationDetail> paymentMap,
        Map<String, CcbPosReconciliationDetail> refundMap
) {
    BigDecimal appPayAmount = BigDecimal.ZERO;
    BigDecimal appRefundAmount = BigDecimal.ZERO;

    for (CCBPosOrder posOrder : posOrders) {
        CcbPosReconciliationDetail detail = ccbPosMatcher.createDetail(billDate);
        detail.setAppTradeTime(posOrder.getTradeTime());
        detail.setBankOrderNo(posOrder.getOrderNo());
        detail.setPosId(posOrder.getPosId());
        detail.setTerminalId(posOrder.getTerminalId());
        detail.setCounterNo(posOrder.getCounterNo());
        if (posOrder.isPay()) {
            detail.setAppOrderNo(posOrder.getSystemRefNo());
            detail.setAppTradeAmount(posOrder.getTransactionAmount());
            detail.setTradeType(TRADE_TYPE_PAY);
            detail.setResult(RESULT_APP_SURPLUS);
            appPayAmount = appPayAmount.add(detail.getAppTradeAmount());
        } else {
            detail.setAppOrderNo(posOrder.getSystemRefNo());
            detail.setAppTradeAmount(posOrder.getTransactionAmount().negate());
            detail.setTradeType(TRADE_TYPE_REFUND);
            detail.setResult(RESULT_APP_SURPLUS);
            appRefundAmount = appRefundAmount.add(detail.getAppTradeAmount());
        }
        ccbPosMatcher.mapAppOrder(posOrder, detail, paymentMap, refundMap);
        reconciliationDetails.add(detail);
    }
    return new BaseReconciliation.AmountSummary(appPayAmount, appRefundAmount);
}

private BaseReconciliation.AmountSummary processHisOrders(
        LocalDate billDate, List<HisSelfPayOrder> hisOrders,
        List<CcbPosReconciliationDetail> reconciliationDetails,
        Map<String, CcbPosReconciliationDetail> paymentMap,
        Map<String, CcbPosReconciliationDetail> refundMap
) {
    BigDecimal hisPayAmount = BigDecimal.ZERO;
    BigDecimal hisRefundAmount = BigDecimal.ZERO;

    for (HisSelfPayOrder hisOrder : hisOrders) {
        CcbPosReconciliationDetail detail = ccbPosMatcher.findMatchDetail(hisOrder, paymentMap, refundMap);
        if (detail != null && detail.getResult() == RESULT_APP_SURPLUS) {
            ccbPosMatcher.updateHisInfo(detail, hisOrder);
        } else {
            detail = ccbPosMatcher.createHisSurplusDetail(billDate, hisOrder);
            reconciliationDetails.add(detail);
        }
        if (hisOrder.isPayment()) {
            hisPayAmount = hisPayAmount.add(detail.getHisTradeAmount());
        } else {
            hisRefundAmount = hisRefundAmount.add(detail.getHisTradeAmount());
        }
    }
    return new BaseReconciliation.AmountSummary(hisPayAmount, hisRefundAmount);
}
```

---

## 3. 匹配规则配置与文档

### 3.1 匹配规则配置

```yaml
# ccb-pos-match-rules.yml
ccb_pos:
  description: "建行POS对账匹配规则"
  pay_rules:
    - source_field: "systemRefNo"
      target_field: "bankOrderNo"
      description: "系统参考号匹配HIS银行订单号"
  refund_rules:
    - source_field: "systemRefNo"
      target_field: "bankOrderNo"
      description: "系统参考号匹配HIS银行订单号"
```

---

## 4. 单元测试

请参考掌医微信Matcher的单元测试方式，编写`CcbPosMatcherTest`，确保匹配逻辑正确。

---

## 5. 总结

- 匹配策略完全解耦，便于扩展和维护。
- 服务实现高度复用模板方法，便于后续新渠道接入。
- 匹配规则可配置、可文档化，便于团队协作和后续可视化。

---

### 英文简要说明

**To refactor the CCB POS reconciliation channel, follow the same strategy pattern and service decoupling as the WeChat channel. Implement a `CcbPosMatcher` for matching logic, refactor the service to use this matcher, and provide configuration and documentation for the matching rules. This ensures maintainability, extensibility, and consistency across all reconciliation channels.**

---

如需具体代码文件结构、注入、测试用例等详细实现，请告知！  
如果需要直接生成上述Matcher和ServiceImpl的完整代码，请说明。

---

