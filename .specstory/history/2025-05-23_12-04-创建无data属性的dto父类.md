<!-- Generated by SpecStory -->

# 创建无data属性的DTO父类 (2025-05-23 21:04:18)

_**User**_

创建一个无data属性的Dto作为 @ResponseDto.kt 的父类

---

_**Assistant**_



---

我将创建一个无data属性的基础Dto作为ResponseDto的父类。

---

```diff
- 
+ package space.lzhq.ph.dto.response
+ 
+ import io.swagger.v3.oas.annotations.media.Schema
+ 
+ /**
+  * 通用响应数据结构基类
+  */
+ @Schema(description = "通用响应数据结构基类")
+ open class BaseResponseDto(
+     @Schema(description = "响应码，0表示成功", example = "0")
+     val code: Int,
+ 
+     @Schema(description = "响应消息", example = "操作成功")
+     val msg: String
+ ) 
```

---

现在修改ResponseDto.kt使其继承BaseResponseDto：

---

```diff
  @Schema(description = "通用响应数据结构")
  data class ResponseDto<T>(
-     @Schema(description = "响应码，0表示成功", example = "0")
-     val code: Int,
- 
-     @Schema(description = "响应消息", example = "操作成功")
-     val msg: String,
- 
      @Schema(description = "响应数据")
      val data: T? = null
+ ) : BaseResponseDto(
+     code = 0,
+     msg = "操作成功"
  )
```

---

已成功创建BaseResponseDto作为父类，并修改ResponseDto使其继承基类。父类包含code和msg属性，子类只保留data属性并设置了默认的code和msg值。

---

