<!-- Generated by SpecStory -->

# 添加新字段 diseaseId (2025-06-02 11:35:35)

_**User**_

添加新字段`Integer diseaseId (慢病病种)`

---

_**Assistant**_



---

我来为您在 `ChronicDiseaseRegistration` 类中添加新字段 `diseaseId`。

---

```diff
      @NotNull(message = "医保类型不能为空")
      private InsuranceType insuranceType;
+ 
+     /**
+      * 慢病病种
+      */
+     @Excel(name = "慢病病种")
+     @NotNull(message = "慢病病种不能为空")
+     private Integer diseaseId;
  
      /**
```

---

已成功为 `ChronicDiseaseRegistration` 类添加了新字段 `diseaseId`。添加的内容包括：

1. **字段定义**: `private Integer diseaseId;`
2. **注解配置**:
   - `@Excel(name = "慢病病种")`: 用于Excel导入导出功能
   - `@NotNull(message = "慢病病种不能为空")`: 验证注解，确保字段不为空
3. **JavaDoc 注释**: 清晰说明字段用途

该字段已按照项目的编码规范进行添加：
- 放置在医保类型字段之后，逻辑上相关联
- 遵循了现有的注解模式和命名约定
- 包含了适当的验证逻辑
- 由于使用了 Lombok 的 `@Data` 注解，getter/setter 方法会自动生成

新字段将用于标识慢病申报记录所对应的具体病种类型。

---

_**User**_

添加新的API端点用于更新慢病申报记录的慢病病种

---

_**Assistant**_



---

我需要为更新慢病申报记录的慢病病种添加新的API端点。让我先查看相关文件来了解现有结构。

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/ChronicDiseaseRegistrationService.java

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/domain/ChronicDisease.java

---

现在让我创建一个更新慢病病种的DTO，然后在Service中添加相应方法，最后在Controller中添加新的API端点。

首先创建DTO：

---

<details>
            <summary>Searched codebase "ChronicDiseaseAgentUpdateDto" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/dto/request/ChronicDiseaseAgentUpdateDto.java

---

现在让我创建一个更新慢病病种的DTO：

---

```diff
- 
+ package space.lzhq.ph.dto.request;
+ 
+ import io.swagger.v3.oas.annotations.media.Schema;
+ import jakarta.validation.constraints.NotNull;
+ import jakarta.validation.constraints.Positive;
+ import lombok.Data;
+ 
+ import java.io.Serial;
+ import java.io.Serializable;
+ 
+ /**
+  * 慢病申报病种更新请求DTO
+  */
+ @Data
+ @Schema(description = "慢病申报病种更新请求DTO")
+ public class ChronicDiseaseDiseaseUpdateDto implements Serializable {
+ 
+     @Serial
+     private static final long serialVersionUID = 1L;
+ 
+     /**
+      * 慢病病种ID
+      */
+     @Schema(description = "慢病病种ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
+     @NotNull(message = "慢病病种ID不能为空")
+     @Positive(message = "慢病病种ID必须为正整数")
+     private Integer diseaseId;
+ } 
```

---

现在在Service接口中添加新方法：

---

```diff
  import jakarta.validation.constraints.NotBlank;
  import jakarta.validation.constraints.NotNull;
+ import jakarta.validation.constraints.Positive;
  import jakarta.validation.constraints.Size;
  import space.lzhq.ph.domain.ChronicDisease;
```

```diff
      );
  
+     /**
+      * 更新慢病申报记录的慢病病种
+      * <p>
+      * 此方法用于更新现有慢病申报记录的慢病病种。执行以下操作：
+      * <ul>
+      *   <li>验证申报记录是否存在</li>
+      *   <li>验证申报记录是否属于当前用户</li>
+      *   <li>验证申报记录状态是否允许编辑</li>
+      *   <li>验证病种ID是否有效且支持当前医保类型</li>
+      *   <li>更新申报记录的病种ID</li>
+      * </ul>
+      *
+      * @param id        申报记录ID，不能为空
+      * @param diseaseId 慢病病种ID，必须为正整数且不能为空
+      * @param openId    用户OpenID，用于验证权限，长度必须在16-28字符之间
+      * @return 更新后的申报记录实体
+      * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申报记录不存在时
+      * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
+      * @throws space.lzhq.ph.exception.EditNotAllowedException 当申报记录状态不允许编辑时
+      * @throws space.lzhq.ph.exception.InvalidDiseaseException 当病种ID无效或不支持当前医保类型时
+      * @throws RuntimeException 当数据库操作失败时
+      * @since 1.0.0
+      */
+     ChronicDiseaseRegistration updateDisease(
+             @NotBlank @Size(min = 32, max = 32) String id,
+             @NotNull @Positive Integer diseaseId,
+             @NotBlank @Size(min = 16, max = 28) String openId
+     );
+ 
  }
```

---

现在查看Service实现类来添加具体实现：

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java

---

现在查看Service实现类的完整结构，然后添加新方法的实现：

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java

---

现在我在Service实现类中添加updateDisease方法的实现。首先需要导入必要的包：

---

```diff
  import jakarta.validation.constraints.NotBlank;
  import jakarta.validation.constraints.NotNull;
+ import jakarta.validation.constraints.Positive;
  import jakarta.validation.constraints.Size;
  import lombok.extern.slf4j.Slf4j;
```

---

现在我需要实现updateDisease方法来解决linter错误：

---

```diff
          return chronicDiseaseService.findByInsuranceType(insuranceType);
      }
+ 
+     @Override
+     @Transactional(rollbackFor = Exception.class)
+     public ChronicDiseaseRegistration updateDisease(
+             @NotBlank @Size(min = 32, max = 32) String id,
+             @NotNull @Positive Integer diseaseId,
+             @NotBlank @Size(min = 16, max = 28) String openId
+     ) {
+         log.info("开始更新慢病病种，申报ID: {}, 病种ID: {}, 用户ID: {}", id, diseaseId, openId);
+ 
+         // 1. 根据ID查询申报记录
+         ChronicDiseaseRegistration registration = this.getById(id);
+         if (registration == null) {
+             log.warn("申报记录不存在，申报ID: {}", id);
+             throw new ResourceNotFoundException(
+                     "申报记录不存在",
+                     "ChronicDiseaseRegistration",
+                     id
+             );
+         }
+ 
+         // 2. 验证申报记录的openId是否与当前用户的openId一致
+         if (!openId.equals(registration.getOpenId())) {
+             log.warn("申报记录不属于当前用户，申报ID: {}, 记录openId: {}, 当前用户openId: {}",
+                     id, registration.getOpenId(), openId);
+             throw new UnauthorizedResourceAccessException(
+                     "申报记录不属于当前用户",
+                     "ChronicDiseaseRegistration",
+                     id,
+                     openId
+             );
+         }
+ 
+         // 3. 验证申报记录状态是否允许用户编辑
+         if (!registration.canBeModifiedByUser()) {
+             log.warn("申报记录状态不允许用户编辑，申报ID: {}, 状态: {}", id, registration.getStatus());
+             throw new EditNotAllowedException(
+                     "申报记录当前状态不允许修改：" + registration.getStatus().getDescription(),
+                     "ChronicDiseaseRegistration",
+                     id,
+                     registration.getStatus().getDescription()
+             );
+         }
+ 
+         // 4. 验证病种ID是否有效
+         ChronicDisease disease = chronicDiseaseService.getById(diseaseId);
+         if (disease == null) {
+             log.warn("慢病病种不存在，病种ID: {}", diseaseId);
+             throw new space.lzhq.ph.exception.InvalidDiseaseException(
+                     "病种不存在，病种ID: " + diseaseId,
+                     diseaseId
+             );
+         }
+ 
+         // 5. 验证病种是否支持当前医保类型
+         InsuranceType insuranceType = registration.getInsuranceType();
+         if (insuranceType == null) {
+             log.warn("申报记录医保类型为空，申报ID: {}", id);
+             throw new IllegalStateException("申报记录医保类型为空，申报ID: " + id);
+         }
+ 
+         boolean supportedByInsurance = chronicDiseaseService.isDiseaseSupported(disease, insuranceType);
+         if (!supportedByInsurance) {
+             log.warn("病种不支持当前医保类型，病种ID: {}, 医保类型: {}", diseaseId, insuranceType);
+             throw new space.lzhq.ph.exception.InvalidDiseaseException(
+                     "病种不支持当前医保类型（" + insuranceType.getDescription() + "），病种ID: " + diseaseId,
+                     diseaseId
+             );
+         }
+ 
+         // 6. 更新申报记录的病种ID
+         registration.setDiseaseId(diseaseId);
+         registration.setUpdateTime(LocalDateTime.now());
+ 
+         // 7. 保存更新后的记录
+         boolean updateSuccess = this.updateById(registration);
+         if (!updateSuccess) {
+             log.error("更新慢病病种失败，申报ID: {}", id);
+             throw new RuntimeException("更新慢病病种失败，请重试");
+         }
+ 
+         log.info("成功更新慢病病种，申报ID: {}, 病种ID: {}, 病种名称: {}, 用户ID: {}",
+                 id, diseaseId, disease.getName(), openId);
+         return registration;
+     }
  
      /**
```

---

我需要检查现有的异常类和Service方法，然后修正这些错误。让我先查看现有的异常类：

---

<details>
            <summary>Grep search for "InvalidDiseaseException" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/ChronicDiseaseRegistrationService.java` | L109 | `* @throws space.lzhq.ph.exception.InvalidDiseaseException 当病种ID无效或不支持当前医保类型时` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java` | L321 | `throw new space.lzhq.ph.exception.InvalidDiseaseException(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java` | L337 | `throw new space.lzhq.ph.exception.InvalidDiseaseException(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/ChronicDiseaseRegistrationService.java` | L109 | `* @throws space.lzhq.ph.exception.InvalidDiseaseException 当病种ID无效或不支持当前医保类型时` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java` | L321 | `throw new space.lzhq.ph.exception.InvalidDiseaseException(` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseRegistrationServiceImpl.java` | L337 | `throw new space.lzhq.ph.exception.InvalidDiseaseException(` |

</details>

---

看起来InvalidDiseaseException还不存在，我需要创建它。同时检查IChronicDiseaseService中有什么方法：

---

<details>
            <summary>Searched codebase "IChronicDiseaseService" • **8** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/IChronicDiseaseService.java

---

现在我需要：
1. 创建InvalidDiseaseException异常类
2. 在IChronicDiseaseService中添加isDiseaseSupported方法
3. 修改Service实现以使用正确的异常类

先创建异常类：

---

<details>
            <summary>Searched codebase "ResourceNotFoundException" • **2** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |

</details>

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/exception/ResourceNotFoundException.java

---

现在创建InvalidDiseaseException：

---

```diff
- 
+ package space.lzhq.ph.exception;
+ 
+ import lombok.Getter;
+ 
+ /**
+  * 无效病种异常
+  * 当病种ID无效或不支持指定医保类型时抛出此异常
+  */
+ @Getter
+ public class InvalidDiseaseException extends RuntimeException {
+ 
+     /**
+      * 病种ID
+      */
+     private final Integer diseaseId;
+ 
+     /**
+      * 构造函数
+      *
+      * @param message 异常消息
+      */
+     public InvalidDiseaseException(String message) {
+         super(message);
+         this.diseaseId = null;
+     }
+ 
+     /**
+      * 构造函数
+      *
+      * @param message   异常消息
+      * @param diseaseId 病种ID
+      */
+     public InvalidDiseaseException(String message, Integer diseaseId) {
+         super(message);
+         this.diseaseId = diseaseId;
+     }
+ 
+     /**
+      * 构造函数
+      *
+      * @param message 异常消息
+      * @param cause   异常原因
+      */
+     public InvalidDiseaseException(String message, Throwable cause) {
+         super(message, cause);
+         this.diseaseId = null;
+     }
+ 
+     /**
+      * 构造函数
+      *
+      * @param message   异常消息
+      * @param cause     异常原因
+      * @param diseaseId 病种ID
+      */
+     public InvalidDiseaseException(String message, Throwable cause, Integer diseaseId) {
+         super(message, cause);
+         this.diseaseId = diseaseId;
+     }
+ } 
```

---

现在在IChronicDiseaseService中添加isDiseaseSupported方法：

---

```diff
      List<ChronicDisease> findByInsuranceType(@NotNull InsuranceType insuranceType);
  
+     /**
+      * 检查指定病种是否支持给定的医保类型
+      * 
+      * @param disease       慢性病病种，不能为空
+      * @param insuranceType 医保类型，不能为空
+      * @return 如果病种支持该医保类型返回true，否则返回false
+      */
+     boolean isDiseaseSupported(@NotNull ChronicDisease disease, @NotNull InsuranceType insuranceType);
+ 
  }
```

---

现在修复Service实现类中的异常导入：

---

```diff
  import space.lzhq.ph.exception.AttachmentValidationException;
  import space.lzhq.ph.exception.EditNotAllowedException;
+ import space.lzhq.ph.exception.InvalidDiseaseException;
  import space.lzhq.ph.exception.ResourceNotFoundException;
  import space.lzhq.ph.exception.UnauthorizedResourceAccessException;
```

```diff
          if (disease == null) {
              log.warn("慢病病种不存在，病种ID: {}", diseaseId);
-             throw new space.lzhq.ph.exception.InvalidDiseaseException(
+             throw new InvalidDiseaseException(
                      "病种不存在，病种ID: " + diseaseId,
                      diseaseId
```

```diff
          if (!supportedByInsurance) {
              log.warn("病种不支持当前医保类型，病种ID: {}, 医保类型: {}", diseaseId, insuranceType);
-             throw new space.lzhq.ph.exception.InvalidDiseaseException(
+             throw new InvalidDiseaseException(
                      "病种不支持当前医保类型（" + insuranceType.getDescription() + "），病种ID: " + diseaseId,
                      diseaseId
```

---

现在检查IChronicDiseaseService的实现类中是否需要添加isDiseaseSupported方法的实现：

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/ChronicDiseaseServiceImpl.java

---

我需要在ChronicDiseaseServiceImpl中添加isDiseaseSupported方法的实现：

---

```diff
          };
      }
+ 
+     /**
+      * 检查指定病种是否支持给定的医保类型
+      * 
+      * @param disease       慢性病病种，不能为空
+      * @param insuranceType 医保类型，不能为空
+      * @return 如果病种支持该医保类型返回true，否则返回false
+      */
+     @Override
+     public boolean isDiseaseSupported(@NotNull ChronicDisease disease, @NotNull InsuranceType insuranceType) {
+         return switch (insuranceType) {
+             case CITY_WORKER -> disease.getInsuranceCityWorker() != null && disease.getInsuranceCityWorker() == 1;
+             case CITY_RESIDENT -> disease.getInsuranceCityResident() != null && disease.getInsuranceCityResident() == 1;
+             case DISTRICT -> disease.getInsuranceDistrict() != null && disease.getInsuranceDistrict() == 1;
+             case CORP -> disease.getInsuranceCorp() != null && disease.getInsuranceCorp() == 1;
+             default -> throw new IllegalArgumentException("Unsupported insurance type: " + insuranceType);
+         };
+     }
  
      /**
```

---

现在在Controller中添加新的API端点。先查看Controller文件的末尾部分：

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt

---

现在在Controller中添加更新慢病病种的API端点。我需要先导入新创建的DTO：

---

```diff
  import space.lzhq.ph.dto.request.ChronicDiseaseAgentUpdateDto
  import space.lzhq.ph.dto.request.ChronicDiseasePatientFormDto
+ import space.lzhq.ph.dto.request.ChronicDiseaseDiseaseUpdateDto
  import space.lzhq.ph.dto.response.*
  import space.lzhq.ph.enums.ChronicDiseaseRegistrationAttachmentType
```

---

现在我在Controller中添加更新慢病病种的API端点（先忽略上面的linter错误，因为它们是已存在的问题）：

---

```diff
          return ResponseDto.success(ChronicDiseaseMapper.INSTANCE.toResponseDtoList(availableDiseases))
      }
+ 
+     @PutMapping(
+         "/{id}/disease",
+         consumes = [MediaType.APPLICATION_JSON_VALUE],
+         produces = [MediaType.APPLICATION_JSON_VALUE]
+     )
+     @RequireSession
+     @Transactional
+     @Operation(
+         summary = "更新慢病申报的病种",
+         description = "更新指定慢病申报记录的病种。\n\n**前置条件**：\n" +
+                 "- 申报记录必须属于当前用户\n" +
+                 "- 申报记录状态必须允许用户编辑\n" +
+                 "- 病种ID必须有效且支持当前申报记录的医保类型\n\n" +
+                 "**操作内容**：\n" +
+                 "- 验证病种是否存在\n" +
+                 "- 验证病种是否支持申报记录的医保类型\n" +
+                 "- 更新申报记录的病种ID (diseaseId)\n\n" +
+                 "**示例请求头**：\n```\nAuthorization: 7cb73c8e-4328-4c32-a89c-1f5e8e2f027b\n```"
+     )
+     @ApiResponses(
+         value = [
+             ApiResponse(
+                 responseCode = "200",
+                 description = "更新成功",
+                 useReturnTypeSchema = true
+             ),
+             ApiResponse(
+                 responseCode = "500",
+                 description = "申报记录不存在或不属于当前用户",
+                 content = [
+                     Content(
+                         mediaType = MediaType.APPLICATION_JSON_VALUE,
+                         schema = Schema(
+                             implementation = BaseResponseDto::class
+                         )
+                     )
+                 ]
+             ),
+             ApiResponse(
+                 responseCode = "500",
+                 description = "申报记录状态不允许编辑",
+                 content = [
+                     Content(
+                         mediaType = MediaType.APPLICATION_JSON_VALUE,
+                         schema = Schema(
+                             implementation = BaseResponseDto::class
+                         )
+                     )
+                 ]
+             ),
+             ApiResponse(
+                 responseCode = "500",
+                 description = "病种ID无效或不支持当前医保类型",
+                 content = [
+                     Content(
+                         mediaType = MediaType.APPLICATION_JSON_VALUE,
+                         schema = Schema(
+                             implementation = BaseResponseDto::class
+                         )
+                     )
+                 ]
+             ),
+             ApiResponse(
+                 responseCode = "500",
+                 description = "操作失败",
+                 content = [
+                     Content(
+                         mediaType = MediaType.APPLICATION_JSON_VALUE,
+                         schema = Schema(
+                             implementation = BaseResponseDto::class
+                         )
+                     )
+                 ]
+             )
+         ]
+     )
+     fun updateDisease(
+         @Parameter(
+             description = "慢病申报记录ID",
+             required = true,
+             example = "abd2a91e-6ff9-f835-a76f-801e6ba9b3aa"
+         )
+         @PathVariable id: String,
+         @Valid @RequestBody diseaseDto: ChronicDiseaseDiseaseUpdateDto
+     ): ResponseDto<ChronicDiseaseRegistrationResponseDto?> {
+         val openId = request.getClientSession()?.openId
+             ?: return ResponseDto.error("会话无效")
+ 
+         // 直接调用服务方法，异常处理交给全局异常处理器
+         val updatedRegistration = registrationService.updateDisease(id, diseaseDto.diseaseId, openId)
+         val responseDto = ChronicDiseaseRegistrationMapper.INSTANCE.toResponseDto(updatedRegistration)
+         return ResponseDto.success(responseDto)
+     }
  
      /**
```

---

我已经成功添加了更新慢病申报记录的慢病病种的新API端点。让我为您总结一下所完成的工作：

## 完成的工作

### 1. 在实体类中添加了新字段
- 在 `ChronicDiseaseRegistration` 实体类中添加了 `diseaseId` 字段
- 包含了适当的注解：`@Excel`、`@NotNull` 和 JavaDoc 注释

### 2. 创建了新的DTO类
- 创建了 `ChronicDiseaseDiseaseUpdateDto` 用于接收更新慢病病种的请求
- 包含了验证注解和 Swagger 文档注解

### 3. 扩展了Service接口和实现
- 在 `ChronicDiseaseRegistrationService` 中添加了 `updateDisease` 方法
- 在 `ChronicDiseaseRegistrationServiceImpl` 中实现了完整的业务逻辑：
  - 验证申报记录存在性和权限
  - 验证申报记录状态是否允许编辑
  - 验证病种ID的有效性
  - 验证病种是否支持当前医保类型
  - 更新记录并记录日志

### 4. 扩展了慢病Service
- 在 `IChronicDiseaseService` 中添加了 `isDiseaseSupported` 方法
- 在 `ChronicDiseaseServiceImpl` 中实现了病种与医保类型的支持检查逻辑

### 5. 创建了新的异常类
- 创建了 `InvalidDiseaseException` 异常类，用于处理无效病种相关的错误

### 6. 添加了新的API端点
- 在 `ChronicDiseaseRegistrationApiController` 中添加了 `PUT /{id}/disease` 端点
- 包含了完整的 Swagger 文档和错误响应定义
- 实现了适当的权限验证和事务管理

## API端点详情

**端点**: `PUT /api/chronic-disease-registration/{id}/disease`

**功能**: 更新指定慢病申报记录的病种

**前置条件**:
- 申报记录必须属于当前用户
- 申报记录状态必须允许用户编辑  
- 病种ID必须有效且支持当前申报记录的医保类型

**请求体示例**:
```json
{
  "diseaseId": 1
}
```

**响应**: 返回更新后的慢病申报记录

这个实现遵循了项目的现有架构模式，包括适当的验证、异常处理、日志记录和API文档。新的端点可以安全地更新慢病申报记录的病种，同时确保数据的一致性和安全性。

---

