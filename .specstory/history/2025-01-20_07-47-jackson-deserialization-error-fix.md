<!-- Generated by SpecStory -->

# Jackson Deserialization Error Fix (2025-01-20 16:47:20)

_**User**_

修复以下报错：
```
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Cannot construct instance of `space.lzhq.ph.domain.PatientInfoToken` (no Creators, like default constructor, exist): cannot deserialize from Object value (no delegate- or property-based Creator)
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 2]
```

---

_**Assistant**_

这个错误是因为 Jackson 在反序列化时需要一个无参构造函数，但是使用了 `@Builder` 注解后没有生成无参构造函数。我们需要添加 `@NoArgsConstructor` 和 `@AllArgsConstructor` 注解来解决这个问题。

以下是修改建议：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/domain/PatientInfoToken.java
// ... existing code ...
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientInfoToken {
    // ... existing code ...
}
```

解释：
1. `@NoArgsConstructor`: 生成无参构造函数，使 Jackson 能够在反序列化时创建对象
2. `@AllArgsConstructor`: 生成全参数构造函数，配合 `@Builder` 使用
3. 保留原有的 `@Data` 和 `@Builder` 注解

这样修改后，Jackson 就能够正确地反序列化 JSON 到 PatientInfoToken 对象了。

---

这个错误的中文解释：
这个错误发生的原因是 Jackson 在将 JSON 转换为对象时，需要一个无参构造函数来创建对象实例。当我们使用 Lombok 的 `@Builder` 注解时，它只生成了构建器相关的代码，没有生成默认的无参构造函数。通过添加 `@NoArgsConstructor` 和 `@AllArgsConstructor` 注解，我们可以确保类同时具有无参构造函数和全参数构造函数，从而使 Jackson 能够正确地进行序列化和反序列化操作。

Git commit message:
```
fix: 添加 Lombok 无参构造函数注解修复 Jackson 反序列化错误

- 添加 @NoArgsConstructor 和 @AllArgsConstructor 注解
- 解决 PatientInfoToken 反序列化时的构造函数缺失问题
```

---

