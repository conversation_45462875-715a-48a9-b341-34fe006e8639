<!-- Generated by SpecStory -->

# Fix TOCTOU race condition in permission checks (2025-06-15 14:37:22)

_**User**_

Fix this bug...

A Time-of-Check-Time-of-Use (TOCTOU) race condition exists in methods performing expert permission checks. `ShiroUtils.getSysUser()` is called twice: once within `isChronicDiseaseExpert()` (which includes a null check) and again immediately after in the calling method's permission block (without a null check). If the user session becomes invalid between these two calls, the second retrieval can return null, leading to a `NullPointerException` when accessing user properties like `getUserId()`. The user object should be retrieved once and reused to prevent this.


---

_**Assistant**_



---

我分析了您提到的TOCTOU竞争条件bug。确实存在这个问题：`isChronicDiseaseExpert()` 方法和调用方法中都会调用 `ShiroUtils.getSysUser()`，在两次调用之间用户会话可能失效，导致第二次调用返回null而引发NullPointerException。

让我修复这个问题：

---



---

现在修复 `getAdminDetailById` 方法中的TOCTOU问题：

---



---

修复 `approveRegistration` 方法中的TOCTOU问题：

---



---



---

修复 `batchApproveRegistrations` 方法中的TOCTOU问题：

---



---



---

修复 `deleteRegistration` 方法中的TOCTOU问题：

---



---



---

最后修复 `buildSearchQueryWrapper` 方法中的TOCTOU问题：

---



---

完美！我已经成功修复了TOCTOU竞争条件bug。下面是修复的总结：

## 修复总结

### 问题分析
原代码存在Time-of-Check-Time-of-Use (TOCTOU) 竞争条件问题：
1. `isChronicDiseaseExpert()` 方法内部调用 `ShiroUtils.getSysUser()` 并进行null检查
2. 在各个业务方法中，先调用 `isChronicDiseaseExpert()` 检查权限，然后再次调用 `ShiroUtils.getSysUser()` 但没有null检查
3. 两次调用之间用户会话可能失效，导致第二次调用返回null，引发`NullPointerException`

### 修复方案
1. **新增 `getCurrentExpertUser()` 方法**：
   - 一次性获取用户对象并检查是否为慢病专家
   - 如果是专家则返回用户对象，否则返回null
   - 包含完整的异常处理

2. **重构 `isChronicDiseaseExpert()` 方法**：
   - 复用 `getCurrentExpertUser()` 方法
   - 保持原有API不变

3. **修复所有存在TOCTOU问题的方法**：
   - `getAdminDetailById()`
   - `approveRegistration()`
   - `batchApproveRegistrations()`
   - `deleteRegistration()`
   - `buildSearchQueryWrapper()`

### 修复效果
- **消除竞争条件**：确保用户对象只获取一次并复用
- **提高代码安全性**：防止NullPointerException
- **保持API兼容性**：现有的 `isChronicDiseaseExpert()` 方法调用不受影响
- **提升性能**：减少了重复的用户对象获取操作

现在代码已经安全了，不会再出现因用户会话在权限检查过程中失效而导致的空指针异常。

---

