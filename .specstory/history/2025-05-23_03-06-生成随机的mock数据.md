<!-- Generated by SpecStory -->

# 生成随机的mock数据 (2025-05-23 12:06:55)

_**User**_

使用随机的mock数据

---

_**Assistant**_



---

我来帮你将硬编码的测试数据改为随机的mock数据。首先让我查看一下项目结构，了解可用的依赖。

---

<details>
            <summary>Listed current directory • **37** results</summary>
        
| Name |
|-------|
| 📁 `.idea` |
| 📁 `.git` |
| 📁 `ruoyi-admin` |
| 📁 `xydlfy-ph` |
| 📁 `ph-pay-ccb` |
| 📁 `ruoyi-framework` |
| 📁 `ruoyi-generator` |
| 📁 `ph-common` |
| 📁 `ruoyi-quartz` |
| 📁 `ruoyi-system` |
| 📁 `ruoyi-common` |
| 📁 `logs` |
| 📁 `profile` |
| 📄 `pom.xml` |
| 📁 `.cursor` |
| 📁 `docs` |
| 📄 `.DS_Store` |
| 📁 `design` |
| 📁 `doc` |
| 📄 `README.md` |
| 📁 `.vscode` |
| 📁 `apidoc` |
| 📄 `deploy.bat` |
| 📄 `.gitignore` |
| 📄 `start_prod.bat` |
| 📄 `package.sh` |
| 📁 `sql` |
| 📄 `start.sh` |
| 📄 `stop.sh` |
| 📄 `ry.sh` |
| 📄 `mvn_install_jar.sh` |
| 📁 `lib` |
| 📁 `avatar` |
| 📄 `backup_mysql.bat` |
| 📁 `bin` |
| 📁 `ddl` |
| 📄 `LICENSE` |

</details>

---

Read file: ruoyi-admin/pom.xml

---

现在让我查看根pom.xml来了解项目的测试依赖情况：

---

Read file: pom.xml

---

现在让我查看PatientInfoToken类来了解其字段类型：

---

<details>
            <summary>Searched codebase "PatientInfoToken class definition builder" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/PatientInfoToken.java` | L1-46 |
| `ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/PatientInfoTokenTest.java` | L1-26 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/Patient.java` | L1-100 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L252-279 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L1-33 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L169-208 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ErhcCardApiController.kt` | L1-31 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ReportApiController.kt` | L213-257 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L1-42 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ReportApiController.kt` | L1-37 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientAdminController.kt` | L1-25 |
| `ph-common/src/main/kotlin/space/lzhq/ph/common/Values.kt` | L1-21 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L33-85 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/AlipayMipController.kt` | L163-203 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L85-129 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ErhcCardApiController.kt` | L268-300 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/NurseCardApiController.kt` | L1-24 |
| `ddl/ph/ph_patient.sql` | L1-34 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxTransferApiController.kt` | L1-28 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L262-281 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/AlipayMipController.kt` | L121-163 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/AlipayMipController.kt` | L1-34 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ErhcCardApiController.kt` | L162-220 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/domain/Patient.java` | L100-167 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxTransferApiController.kt` | L79-125 |

</details>

---

现在我已经了解了`PatientInfoToken`类的结构。让我创建一个使用随机mock数据的测试版本：

---

```diff
  import org.junit.jupiter.api.Test;
  import space.lzhq.ph.domain.PatientInfoToken;
+ 
+ import java.time.LocalDate;
+ import java.util.Random;
  
  import static org.junit.jupiter.api.Assertions.assertEquals;
  
  class PatientInfoTokenTest {
  
-     @Test
-     void test() {
-         PatientInfoToken token1 = PatientInfoToken.builder()
-                 .patientId("2272185")
-                 .patientIdCardNo("371722200111176832")
-                 .patientCardNo("371722200111176832")
-                 .patientName("孔令杰")
-                 .patientMobile("13800138000")
+     private final Random random = new Random();
+     
+     /**
+      * 生成随机的身份证号码
+      * 格式：地区码(6位) + 年月日(8位) + 顺序码(3位) + 校验码(1位)
+      */
+     private String generateRandomIdCardNo() {
+         // 常见地区码前缀
+         String[] areaCodes = {"110101", "310101", "440301", "371722", "230101", "500101"};
+         String areaCode = areaCodes[random.nextInt(areaCodes.length)];
+         
+         // 生成1980-2000年的随机出生日期
+         int year = 1980 + random.nextInt(21);
+         int month = 1 + random.nextInt(12);
+         int day = 1 + random.nextInt(28); // 简化处理，避免月份天数问题
+         
+         String birthDate = String.format("%04d%02d%02d", year, month, day);
+         
+         // 生成3位顺序码
+         String sequenceCode = String.format("%03d", random.nextInt(1000));
+         
+         // 简化校验码生成，直接随机选择
+         String checkCode = random.nextBoolean() ? "X" : String.valueOf(random.nextInt(10));
+         
+         return areaCode + birthDate + sequenceCode + checkCode;
+     }
+     
+     /**
+      * 生成随机的手机号码
+      * 使用常见的号段前缀
+      */
+     private String generateRandomMobile() {
+         String[] prefixes = {"138", "139", "150", "151", "188", "189", "130", "131", "132"};
+         String prefix = prefixes[random.nextInt(prefixes.length)];
+         String suffix = String.format("%08d", random.nextInt(100000000));
+         return prefix + suffix;
+     }
+     
+     /**
+      * 生成随机的患者ID
+      */
+     private String generateRandomPatientId() {
+         return String.valueOf(1000000 + random.nextInt(9000000));
+     }
+     
+     /**
+      * 生成随机的中文姓名
+      */
+     private String generateRandomChineseName() {
+         String[] surnames = {"王", "李", "张", "刘", "陈", "杨", "黄", "赵", "吴", "周", "徐", "孙", "马", "朱", "胡", "郭", "何", "林", "高", "罗"};
+         String[] givenNames = {"伟", "芳", "娜", "敏", "静", "杰", "涛", "明", "超", "勇", "军", "磊", "强", "飞", "红", "丽", "华", "燕", "洋", "峰"};
+         
+         String surname = surnames[random.nextInt(surnames.length)];
+         String givenName = givenNames[random.nextInt(givenNames.length)];
+         
+         // 有时候生成两个字的名字
+         if (random.nextBoolean()) {
+             givenName += givenNames[random.nextInt(givenNames.length)];
+         }
+         
+         return surname + givenName;
+     }
+     
+     /**
+      * 创建随机的PatientInfoToken
+      */
+     private PatientInfoToken createRandomPatientInfoToken() {
+         String idCardNo = generateRandomIdCardNo();
+         return PatientInfoToken.builder()
+                 .patientId(generateRandomPatientId())
+                 .patientIdCardNo(idCardNo)
+                 .patientCardNo(idCardNo) // 通常就诊卡号与身份证号相同
+                 .patientName(generateRandomChineseName())
+                 .patientMobile(generateRandomMobile())
                  .build();
- 
+     }
+ 
+     @Test
+     void testWithRandomData() {
+         // 生成随机的患者信息
+         PatientInfoToken token1 = createRandomPatientInfoToken();
+         
+         // 生成token字符串
          String tokenString = token1.generateToken();
- 
+         
+         // 解析token
          PatientInfoToken token2 = PatientInfoToken.parseToken(tokenString);
+         
+         // 验证解析后的对象与原对象相等
          assertEquals(token1, token2);
      }
+     
+     @Test
+     void testMultipleRandomData() {
+         // 测试多次随机数据，确保逻辑的稳定性
+         for (int i = 0; i < 10; i++) {
+             PatientInfoToken token1 = createRandomPatientInfoToken();
+             String tokenString = token1.generateToken();
+             PatientInfoToken token2 = PatientInfoToken.parseToken(tokenString);
+             assertEquals(token1, token2, "第" + (i + 1) + "次测试失败");
+         }
+     }
  
  }
```

---

现在让我运行测试来验证修改后的代码是否正常工作：

---

```bash
cd ruoyi-admin && mvn test -Dtest=PatientInfoTokenTest
```

---

_**User**_

方法名和变量不太合适

---

_**Assistant**_



---

你说得对，让我优化一下方法名和变量名，使其更加清晰和符合最佳实践：

---

```diff
  
      @Test
-     void test() {
-         RandomStringUtils randomStringUtils = RandomStringUtils.secure();
-         PatientInfoToken token1 = PatientInfoToken.builder()
-                 .patientId(randomStringUtils.nextNumeric(6))
-                 .patientIdCardNo(randomStringUtils.nextNumeric(18))
-                 .patientCardNo(randomStringUtils.nextNumeric(10))
-                 .patientName(randomStringUtils.nextAlphabetic(8))
-                 .patientMobile(randomStringUtils.nextNumeric(11))
+     void shouldParseTokenCorrectlyWithRandomData() {
+         // Given: 创建包含随机数据的患者信息token
+         PatientInfoToken originalToken = PatientInfoToken.builder()
+                 .patientId(RandomStringUtils.randomNumeric(6))
+                 .patientIdCardNo(RandomStringUtils.randomNumeric(18))
+                 .patientCardNo(RandomStringUtils.randomNumeric(10))
+                 .patientName(RandomStringUtils.randomAlphabetic(8))
+                 .patientMobile(RandomStringUtils.randomNumeric(11))
                  .build();
  
-         String tokenString = token1.generateToken();
+         // When: 生成token字符串并解析
+         String tokenString = originalToken.generateToken();
+         PatientInfoToken parsedToken = PatientInfoToken.parseToken(tokenString);
  
-         PatientInfoToken token2 = PatientInfoToken.parseToken(tokenString);
-         assertEquals(token1, token2);
+         // Then: 解析后的token应该与原始token相等
+         assertEquals(originalToken, parsedToken);
      }
  
```

---

现在代码的改进包括：

1. **更具描述性的方法名**：`shouldParseTokenCorrectlyWithRandomData()` 清楚地表达了测试的目的 - 验证使用随机数据时token能正确解析

2. **更好的变量命名**：
   - `originalToken` - 明确表示这是原始的token
   - `parsedToken` - 明确表示这是解析后的token
   - 移除了冗余的 `randomStringUtils` 变量

3. **使用静态方法调用**：直接使用 `RandomStringUtils.randomNumeric()` 而不是创建实例

4. **添加了清晰的注释**：使用 Given-When-Then 模式的注释，符合BDD（行为驱动开发）的最佳实践

5. **更符合测试命名约定**：方法名以 `should` 开头，清楚地表达了预期行为

这样的代码更加清晰、易读，并且符合Java和JUnit的最佳实践。

---

