<!-- Generated by SpecStory -->

# Fixing Bug in Reconciliation Detail Service (2024-12-31 13:14:09)

_**User**_

Fix this bug in `xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/GuoguangATMReconciliationDetailServiceImpl.java`:
```
In the `listNotBalanced` method, `ZhangyiWeixinReconciliationDetail.RESULT_BALANCED` is incorrectly used instead of `GuoguangATMReconciliationDetail.RESULT_BALANCED`. This causes incorrect filtering of reconciliation details, potentially misidentifying balanced and unbalanced transactions.
```

---

_**Assistant**_

我来帮你修复这个问题。这是一个类型引用错误，我们需要将 `ZhangyiWeixinReconciliationDetail.RESULT_BALANCED` 改为 `GuoguangATMReconciliationDetail.RESULT_BALANCED`。

以下是对文件的修改建议：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/GuoguangATMReconciliationDetailServiceImpl.java
    // 其他代码保持不变...

    @Override
    public List<GuoguangATMReconciliationDetail> listNotBalanced(LocalDate billDate) {
        String billDateStr = billDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        return this.lambdaQuery().likeRight(GuoguangATMReconciliationDetail::getId, billDateStr)
                .ne(GuoguangATMReconciliationDetail::getResult, GuoguangATMReconciliationDetail.RESULT_BALANCED)
                .list();
    }

    // 其他代码保持不变...
```

这个修改：
1. 将错误引用的 `ZhangyiWeixinReconciliationDetail.RESULT_BALANCED` 替换为正确的 `GuoguangATMReconciliationDetail.RESULT_BALANCED`
2. 确保使用正确的实体类中定义的常量值来判断对账结果
3. 这样可以保证正确筛选出未平账的记录

同时，你可能需要检查一下 `GuoguangATMReconciliationDetail` 类中是否已经正确定义了 `RESULT_BALANCED` 常量。如果没有定义，你需要在该类中添加这个常量定义。

---

