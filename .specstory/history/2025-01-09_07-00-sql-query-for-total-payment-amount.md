<!-- Generated by SpecStory -->

# SQL Query for Total Payment Amount (2025-01-09 16:00:55)

_**User**_

参考以下SQL语句
```
SELECT SUM(t."交易金额")  FROM dzpt.v_dzpt_dzmb t
WHERE t."交易时间" >= TO_DATE('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
AND t."交易时间" <= TO_DATE('2024-01-01 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
AND t."交易类型"='支付';
```
编写查询指定日期总支付金额的方法

---

_**Assistant**_

我将帮您在HisSelfPayOrderMapper接口中添加一个查询指定日期总支付金额的方法。

```java:xydlfy-ph/src/main/java/space/lzhq/ph/mapper/HisSelfPayOrderMapper.java
package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.HisSelfPayOrder;

@Repository
@DataSource(value = DataSourceType.HIS_ORDER_VIEW)
public interface HisSelfPayOrderMapper extends BaseMapper<HisSelfPayOrder> {
    
    /**
     * 查询指定日期的总支付金额
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 总支付金额
     */
    @Select("SELECT SUM(t.\"交易金额\") FROM dzpt.v_dzpt_dzmb t " +
            "WHERE t.\"交易时间\" >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS') " +
            "AND t.\"交易时间\" <= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS') " +
            "AND t.\"交易类型\" = '支付'")
    Double getTotalPaymentAmountByDate(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
```

我添加了一个名为`getTotalPaymentAmountByDate`的方法，该方法：

1. 使用`@Select`注解直接编写SQL查询
2. 接收两个参数：`startTime`和`endTime`，分别表示查询的开始时间和结束时间
3. 使用`@Param`注解标注参数，以便在SQL中引用
4. 返回类型为`Double`，表示总支付金额

使用示例：
```java
String startTime = "2024-01-01 00:00:00";
String endTime = "2024-01-01 23:59:59";
Double totalAmount = hisSelfPayOrderMapper.getTotalPaymentAmountByDate(startTime, endTime);
```

这个方法会返回指定日期范围内的总支付金额。如果没有数据或查询出错，将返回null。

---

_**User**_

传入字符串参数不好，能否把入参改为传入LocalDate

---

_**Assistant**_

好的，我来修改入参类型为`LocalDate`，并调整SQL查询语句。

```java:xydlfy-ph/src/main/java/space/lzhq/ph/mapper/HisSelfPayOrderMapper.java
package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.HisSelfPayOrder;

import java.math.BigDecimal;
import java.time.LocalDate;

@Repository
@DataSource(value = DataSourceType.HIS_ORDER_VIEW)
public interface HisSelfPayOrderMapper extends BaseMapper<HisSelfPayOrder> {
    
    /**
     * 查询指定日期的总支付金额
     * @param date 查询日期
     * @return 总支付金额
     */
    @Select("SELECT SUM(t.\"交易金额\") FROM dzpt.v_dzpt_dzmb t " +
            "WHERE t.\"交易时间\" >= TRUNC(#{date}) " +
            "AND t.\"交易时间\" < TRUNC(#{date} + 1) " +
            "AND t.\"交易类型\" = '支付'")
    BigDecimal getTotalPaymentAmountByDate(@Param("date") LocalDate date);
}
```

主要修改：

1. 添加了`java.time.LocalDate`的导入
2. 将两个String类型参数合并为一个`LocalDate`参数
3. 使用Oracle的`TRUNC`函数处理日期，确保查询整天的数据
4. 使用`date + 1`来表示第二天的开始时间，替代之前的23:59:59

使用示例：
```java
LocalDate queryDate = LocalDate.of(2024, 1, 1);
BigDecimal totalAmount = hisSelfPayOrderMapper.getTotalPaymentAmountByDate(queryDate);
```

这样的修改更加简洁且类型安全，不需要手动处理日期字符串格式化。

---

_**User**_

以2025-01-08为例，把`@Select`中的SQL语句组装好，我想到数据库中验证一下。

---

_**Assistant**_

我来帮您把SQL语句组装好。对于日期2025-01-08，SQL语句会是这样：

```sql
SELECT SUM(t."交易金额") 
FROM dzpt.v_dzpt_dzmb t 
WHERE t."交易时间" >= TRUNC(TO_DATE('2025-01-08', 'YYYY-MM-DD')) 
AND t."交易时间" < TRUNC(TO_DATE('2025-01-08', 'YYYY-MM-DD') + 1) 
AND t."交易类型" = '支付'
```

这条SQL语句会：
1. 查询2025-01-08 00:00:00到2025-01-09 00:00:00之间的所有交易
2. 只统计交易类型为"支付"的记录
3. 返回这个时间段内的交易金额总和

您可以直接复制这条SQL到数据库中执行验证。如果想测试其他日期，只需要修改`'2025-01-08'`这个日期值即可。

---

