<!-- Generated by SpecStory -->

# 微信支付与HIS账单对账分析 (2024-12-03 09:52:08)

_**User**_

`20241201_wxbill.csv` 是微信支付的账单文件，his_self_order.csv 是从HIS对账单视图中导出的（对应的实体类是 HisSelfPayOrder.java）。分析这两个文件，实现对账功能。

---

_**Assistant**_

我来帮您分析这两个文件并实现对账功能。

首先让我们分析两个文件的特点：

1. `20241201_wxbill.csv` (微信账单):
- 包含交易时间、订单号、交易金额、退款金额等信息
- 正向交易金额在"交易金额"列，退款在"交易金额"列为负数
- 订单号在"商户流水号"列

2. `his_self_order.csv` (HIS系统账单):
- 包含交易时间、订单号、交易金额、交易类型等信息
- 使用"交易类型"区分支付和退款
- 银行订单号对应微信账单的商户流水号

我建议创建一个新的服务类来处理对账逻辑：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/WxCheckServiceImpl.java
package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.service.IWxCheckService;

import java.io.BufferedReader;
import java.io.FileReader;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class WxCheckServiceImpl implements IWxCheckService {
    
    public class WxBillRecord {
        LocalDateTime tradeTime;
        String orderNo;
        BigDecimal amount;
        String bankOrderNo;
        
        public WxBillRecord(String[] fields) {
            this.tradeTime = LocalDateTime.parse(fields[0].replace("`",""), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            this.orderNo = fields[3].replace("`","");
            this.amount = new BigDecimal(fields[8].replace("`",""));
            this.bankOrderNo = fields[2].replace("`","");
        }
    }
    
    public class CheckResult {
        List<WxBillRecord> wxOnly = new ArrayList<>(); // 只在微信账单中存在
        List<HisSelfPayOrder> hisOnly = new ArrayList<>(); // 只在HIS账单中存在
        List<String> amountMismatch = new ArrayList<>(); // 金额不匹配
    }

    @Override
    public CheckResult checkBill(String wxBillPath, List<HisSelfPayOrder> hisOrders) {
        // 读取微信账单并解析
        Map<String, WxBillRecord> wxBillMap = new HashMap<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(wxBillPath))) {
            String line;
            reader.readLine(); // 跳过表头
            while ((line = reader.readLine()) != null) {
                String[] fields = line.split(",");
                WxBillRecord record = new WxBillRecord(fields);
                wxBillMap.put(record.orderNo, record);
            }
        } catch (Exception e) {
            throw new RuntimeException("读取微信账单失败", e);
        }

        // 将HIS订单转换为Map
        Map<String, HisSelfPayOrder> hisOrderMap = new HashMap<>();
        for (HisSelfPayOrder order : hisOrders) {
            hisOrderMap.put(order.getBankOrderNo(), order);
        }

        CheckResult result = new CheckResult();

        // 检查微信账单中的每条记录
        for (WxBillRecord wxRecord : wxBillMap.values()) {
            HisSelfPayOrder hisOrder = hisOrderMap.get(wxRecord.bankOrderNo);
            if (hisOrder == null) {
                result.wxOnly.add(wxRecord);
                continue;
            }

            // 检查金额是否匹配
            if (!wxRecord.amount.equals(hisOrder.getTradeAmount())) {
                result.amountMismatch.add(String.format(
                    "订单号: %s, 微信金额: %s, HIS金额: %s",
                    wxRecord.orderNo, wxRecord.amount, hisOrder.getTradeAmount()
                ));
            }
        }

        // 检查HIS账单中的每条记录
        for (HisSelfPayOrder hisOrder : hisOrders) {
            if (!wxBillMap.containsKey(hisOrder.getBankOrderNo())) {
                result.hisOnly.add(hisOrder);
            }
        }

        return result;
    }
}
```

然后创建对应的接口：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/service/IWxCheckService.java
package space.lzhq.ph.service;

import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.service.impl.WxCheckServiceImpl.CheckResult;

import java.util.List;

public interface IWxCheckService {
    CheckResult checkBill(String wxBillPath, List<HisSelfPayOrder> hisOrders);
}
```

这个实现：

1. 将微信账单和HIS账单分别读入内存并转换为Map结构，便于快速查找
2. 对每条微信账单记录：
   - 检查是否存在对应的HIS记录
   - 如果存在，检查金额是否匹配
3. 对每条HIS记录：
   - 检查是否存在对应的微信记录
4. 将检查结果分为三类：
   - 只在微信账单中存在的记录
   - 只在HIS账单中存在的记录
   - 金额不匹配的记录

您可以在Controller中这样使用：

```kotlin:xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxCheckAdminController.kt
@Controller
@RequestMapping("/ph/check")
class WxCheckAdminController {
    
    @Autowired
    private lateinit var wxCheckService: IWxCheckService
    
    @Autowired
    private lateinit var hisSelfPayOrderService: IHisSelfPayOrderService

    @GetMapping("/check")
    fun check(model: Model): String {
        val hisOrders = hisSelfPayOrderService.list()
        val wxBillPath = "path/to/20241201_wxbill.csv"
        
        val checkResult = wxCheckService.checkBill(wxBillPath, hisOrders)
        
        model.addAttribute("wxOnly", checkResult.wxOnly)
        model.addAttribute("hisOnly", checkResult.hisOnly)
        model.addAttribute("amountMismatch", checkResult.amountMismatch)
        
        return "ph/check/check"
    }
}
```

对应的视图模板：

```html:xydlfy-ph/src/main/resources/templates/ph/check/check.html
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>对账结果</title>
</head>
<body>
    <h2>对账结果</h2>
    
    <h3>只在微信账单中存在的记录</h3>
    <table>
        <tr>
            <th>交易时间</th>
            <th>订单号</th>
            <th>金额</th>
        </tr>
        <tr th:each="record : ${wxOnly}">
            <td th:text="${record.tradeTime}"></td>
            <td th:text="${record.orderNo}"></td>
            <td th:text="${record.amount}"></td>
        </tr>
    </table>

    <h3>只在HIS账单中存在的记录</h3>
    <table>
        <tr>
            <th>交易时间</th>
            <th>订单号</th>
            <th>金额</th>
            <th>病人姓名</th>
        </tr>
        <tr th:each="order : ${hisOnly}">
            <td th:text="${order.tradeTime}"></td>
            <td th:text="${order.bankOrderNo}"></td>
            <td th:text="${order.tradeAmount}"></td>
            <td th:text="${order.patientName}"></td>
        </tr>
    </table>

    <h3>金额不匹配的记录</h3>
    <ul>
        <li th:each="mismatch : ${amountMismatch}" th:text="${mismatch}"></li>
    </ul>
</body>
</html>
```

这个实现提供了基本的对账功能。您可以根据需要进一步扩展：

1. 添加日期范围过滤
2. 添加更多的对账维度（如交易时间匹配检查）
3. 添加统计信息（如总金额对比）
4. 添加导出功能
5. 优化性能（对于大量数据可以考虑使用流式处理）

需要注意的是，这个实现假设订单号是唯一的。如果存在重复的订单号，您需要添加额外的逻辑来处理。

---

_**User**_

`20241201_wxbill.csv` 是微信支付的账单文件，其中的数据可能使用 `CcbPayServices.downloadAndSaveBill` 获得。创建新文件实现对账功能，不要修改已有的文件。

---

_**Assistant**_

我理解需要创建新的对账功能，同时保留现有的对账系统。让我们创建新的文件来实现这个功能：

```java:xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/BillCheckServiceImpl.java
package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.service.IBillCheckService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class BillCheckServiceImpl implements IBillCheckService {

    public static class BillRecord {
        private LocalDateTime tradeTime;
        private String orderNo;
        private BigDecimal amount;
        private String bankOrderNo;
        private String tradeType; // "支付" 或 "退款"
        private String businessType; // "门诊" 或 "住院"
        private String patientName;
        private String patientId;
        
        public BillRecord(LocalDateTime tradeTime, String orderNo, BigDecimal amount, 
                         String bankOrderNo, String tradeType, String businessType) {
            this.tradeTime = tradeTime;
            this.orderNo = orderNo;
            this.amount = amount;
            this.bankOrderNo = bankOrderNo;
            this.tradeType = tradeType;
            this.businessType = businessType;
        }
        
        // Getters
    }

    public static class CheckResult {
        private List<BillRecord> wxOnlyRecords = new ArrayList<>();
        private List<BillRecord> hisOnlyRecords = new ArrayList<>();
        private List<BillRecord> amountMismatchRecords = new ArrayList<>();
        
        private BigDecimal totalWxPayAmount = BigDecimal.ZERO;
        private BigDecimal totalWxRefundAmount = BigDecimal.ZERO;
        private BigDecimal totalHisPayAmount = BigDecimal.ZERO;
        private BigDecimal totalHisRefundAmount = BigDecimal.ZERO;
        
        private BigDecimal totalWxMzPayAmount = BigDecimal.ZERO;
        private BigDecimal totalWxMzRefundAmount = BigDecimal.ZERO;
        private BigDecimal totalWxZyPayAmount = BigDecimal.ZERO;
        private BigDecimal totalWxZyRefundAmount = BigDecimal.ZERO;
        
        // Getters and summary methods
        public BigDecimal getWxNetAmount() {
            return totalWxPayAmount.subtract(totalWxRefundAmount);
        }
        
        public BigDecimal getHisNetAmount() {
            return totalHisPayAmount.subtract(totalHisRefundAmount);
        }
        
        public BigDecimal getDiffAmount() {
            return getWxNetAmount().subtract(getHisNetAmount());
        }
    }

    @Override
    public CheckResult checkBill(LocalDate billDate, List<HisSelfPayOrder> hisOrders) {
        CheckResult result = new CheckResult();
        
        // 获取微信账单
        WxPayBillResult wxBill = CcbPayServices.INSTANCE.downloadAndSaveBill(billDate);
        if (wxBill == null || wxBill.getBillInfoList() == null) {
            throw new IllegalStateException("无法获取微信账单");
        }

        // 将HIS订单转换为Map以便快速查找
        Map<String, BillRecord> hisRecordMap = new HashMap<>();
        for (HisSelfPayOrder hisOrder : hisOrders) {
            BillRecord record = new BillRecord(
                hisOrder.getTradeTime(),
                hisOrder.getBankOrderNo(),
                hisOrder.getTradeAmount(),
                hisOrder.getBankOrderNo(),
                hisOrder.isPayment() ? "支付" : "退款",
                hisOrder.isMenzhen() ? "门诊" : "住院"
            );
            record.patientName = hisOrder.getPatientName();
            record.patientId = hisOrder.getPatientId();
            
            hisRecordMap.put(record.bankOrderNo, record);
            
            // 累计HIS金额
            if (hisOrder.isPayment()) {
                result.totalHisPayAmount = result.totalHisPayAmount.add(hisOrder.getTradeAmount());
                if (hisOrder.isMenzhen()) {
                    result.totalWxMzPayAmount = result.totalWxMzPayAmount.add(hisOrder.getTradeAmount());
                } else {
                    result.totalWxZyPayAmount = result.totalWxZyPayAmount.add(hisOrder.getTradeAmount());
                }
            } else {
                result.totalHisRefundAmount = result.totalHisRefundAmount.add(hisOrder.getTradeAmount());
                if (hisOrder.isMenzhen()) {
                    result.totalWxMzRefundAmount = result.totalWxMzRefundAmount.add(hisOrder.getTradeAmount());
                } else {
                    result.totalWxZyRefundAmount = result.totalWxZyRefundAmount.add(hisOrder.getTradeAmount());
                }
            }
        }

        // 检查每笔微信交易
        for (WxPayBillInfo wxRecord : wxBill.getBillInfoList()) {
            String orderNo = wxRecord.getOutTradeNo();
            BigDecimal wxAmount = new BigDecimal(wxRecord.getTotalFee());
            
            BillRecord wxBillRecord = new BillRecord(
                LocalDateTime.parse(wxRecord.getTradeTime()),
                orderNo,
                wxAmount,
                wxRecord.getOutTradeNo(),
                wxRecord.isPay() ? "支付" : "退款",
                wxRecord.isMenzhen() ? "门诊" : "住院"
            );
            
            // 累计微信金额
            if (wxRecord.isPay()) {
                result.totalWxPayAmount = result.totalWxPayAmount.add(wxAmount);
            } else {
                result.totalWxRefundAmount = result.totalWxRefundAmount.add(wxAmount);
            }

            BillRecord hisRecord = hisRecordMap.get(orderNo);
            if (hisRecord == null) {
                result.wxOnlyRecords.add(wxBillRecord);
                continue;
            }

            // 检查金额是否匹配
            if (wxAmount.compareTo(hisRecord.amount) != 0) {
                result.amountMismatchRecords.add(wxBillRecord);
            }
            
            // 从HIS记录Map中移除已匹配的记录
            hisRecordMap.remove(orderNo);
        }

        // 剩余的HIS记录就是只在HIS系统中存在的记录
        result.hisOnlyRecords.addAll(hisRecordMap.values());

        return result;
    }
}
```

```java:xydlfy-ph/src/main/java/space/lzhq/ph/service/IBillCheckService.java
package space.lzhq.ph.service;

import space.lzhq.ph.domain.HisSelfPayOrder;
import space.lzhq.ph.service.impl.BillCheckServiceImpl.CheckResult;

import java.time.LocalDate;
import java.util.List;

public interface IBillCheckService {
    CheckResult checkBill(LocalDate billDate, List<HisSelfPayOrder> hisOrders);
}
```

```kotlin:xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/BillCheckController.kt
package space.lzhq.ph.controller

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import space.lzhq.ph.service.IBillCheckService
import space.lzhq.ph.service.IHisSelfPayOrderService
import java.time.LocalDate

@Controller
@RequestMapping("/ph/billcheck")
class BillCheckController {
    
    @Autowired
    private lateinit var billCheckService: IBillCheckService
    
    @Autowired
    private lateinit var hisSelfPayOrderService: IHisSelfPayOrderService

    @GetMapping("/check")
    fun check(billDate: LocalDate, model: Model): String {
        val hisOrders = hisSelfPayOrderService.listByTradeDateAndApplication(billDate, "掌医")
        val checkResult = billCheckService.checkBill(billDate, hisOrders)
        
        model.addAttribute("checkResult", checkResult)
        model.addAttribute("billDate", billDate)
        
        return "ph/billcheck/check"
    }
}
```

```html:xydlfy-ph/src/main/resources/templates/ph/billcheck/check.html
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('对账结果')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>对账汇总 - <span th:text="${billDate}"></span></h5>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="panel panel-primary">
                                <div class="panel-heading">微信支付</div>
                                <div class="panel-body">
                                    <p>总支付金额：<span th:text="${checkResult.totalWxPayAmount}"></span></p>
                                    <p>总退款金额：<span th:text="${checkResult.totalWxRefundAmount}"></span></p>
                                    <p>净收入：<span th:text="${checkResult.wxNetAmount}"></span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="panel panel-info">
                                <div class="panel-heading">HIS系统</div>
                                <div class="panel-body">
                                    <p>总支付金额：<span th:text="${checkResult.totalHisPayAmount}"></span></p>
                                    <p>总退款金额：<span th:text="${checkResult.totalHisRefundAmount}"></span></p>
                                    <p>净收入：<span th:text="${checkResult.hisNetAmount}"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="panel panel-danger">
                                <div class="panel-heading">差异</div>
                                <div class="panel-body">
                                    <p>差额：<span th:text="${checkResult.diffAmount}"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细对账结果 -->
            <div class="tabs-container">
                <ul class="nav nav-tabs">
                    <li class="active"><a data-toggle="tab" href="#tab-1">只在微信存在的记录</a></li>
                    <li><a data-toggle="tab" href="#tab-2">只在HIS存在的记录</a></li>
                    <li><a data-toggle="tab" href="#tab-3">金额不匹配的记录</a></li>
                </ul>
                <div class="tab-content">
                    <div id="tab-1" class="tab-pane active">
                        <div class="panel-body">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>交易时间</th>
                                        <th>订单号</th>
                                        <th>金额</th>
                                        <th>交易类型</th>
                                        <th>业务类型</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="record : ${checkResult.wxOnlyRecords}">
                                        <td th:text="${record.tradeTime}"></td>
                                        <td th:text="${record.orderNo}"></td>
                                        <td th:text="${record.amount}"></td>
                                        <td th:text="${record.tradeType}"></td>
                                        <td th:text="${record.businessType}"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- 其他tab页类似 -->
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
```

这个实现：

1. 创建了独立的对账服务，不影响现有系统
2. 提供了详细的对账结果，包括:
   - 总金额统计
   - 门诊/住院分类统计
   - 差异记录明细
3. 使用了友好的Web界面展示结果
4. 保持了与现有系统的一致性（使用相同的数据源和账单获取方式）

你可以通过访问 `/ph/billcheck/check?billDate=2024-12-01` 来查看对账结果。

这个实现还可以进一步优化：

1. 添加导出功能
2. 添加更多的统计维度
3. 添加对账结果的持久化
4. 添加定时任务自动对账
5. 添加更多的数据验证和错误处理

---

