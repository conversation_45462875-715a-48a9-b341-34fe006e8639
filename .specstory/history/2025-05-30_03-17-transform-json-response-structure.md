<!-- Generated by SpecStory -->

# Transform JSON response structure (2025-05-30 12:17:40)

_**User**_

Transform the following JSON response from `GET /api/chronic-disease-registration/enums`. The object currently nested under `data.enums` should be moved directly under the `data` key, removing the `enums` key.

```
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "enums": {
      "ChronicDiseaseRegistrationStatus": [
        {
          "code": "DRAFT",
          "description": "草稿（待提交）"
        },
        {
          "code": "SUBMITTED",
          "description": "已提交（待审核）"
        },
        {
          "code": "APPROVED",
          "description": "审核通过"
        },
        {
          "code": "REJECTED",
          "description": "审核驳回（待整改）"
        }
      ],
      "Relationship": [
        {
          "code": "FATHER",
          "description": "父亲"
        },
        {
          "code": "MOTHER",
          "description": "母亲"
        },
        {
          "code": "HUSBAND",
          "description": "丈夫"
        },
        {
          "code": "WIFE",
          "description": "妻子"
        },
        {
          "code": "SON",
          "description": "儿子"
        },
        {
          "code": "DAUGHTER",
          "description": "女儿"
        },
        {
          "code": "BROTHER",
          "description": "兄弟"
        },
        {
          "code": "SISTER",
          "description": "姐妹"
        },
        {
          "code": "FRIEND",
          "description": "朋友"
        },
        {
          "code": "COLLEAGUE",
          "description": "同事"
        },
        {
          "code": "CLASSMATE",
          "description": "同学"
        },
        {
          "code": "OTHER",
          "description": "其他"
        }
      ],
      "InsuranceType": [
        {
          "code": "CITY_WORKER",
          "description": "乌鲁木齐市职工医保"
        },
        {
          "code": "CITY_RESIDENT",
          "description": "乌鲁木齐市居民医保"
        },
        {
          "code": "DISTRICT",
          "description": "区医保（含铁路医保）"
        },
        {
          "code": "CORP",
          "description": "兵团医保（兵直、十一师、十二师）"
        }
      ],
      "ChronicDiseaseRegistrationAttachmentType": [
        {
          "code": "ID_CARD",
          "description": "身份证"
        },
        {
          "code": "INSURANCE_CARD",
          "description": "医保卡"
        },
        {
          "code": "DOCUMENT",
          "description": "认定资料"
        }
      ]
    }
  }
}
```

---

_**Assistant**_



---

我需要先查看相关的控制器代码来了解当前的实现，然后修改 DTO 类以实现你要求的响应结构。

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt

---

<details>
            <summary>Grep search for "enums.*GetMapping" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "GetMapping.*enums" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt` | L269 | `@GetMapping("/enums")` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt` | L269 | `@GetMapping("/enums")` |

</details>

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiController.kt

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/dto/response/EnumItemDto.java

---

现在我了解了当前的实现。要实现你要求的响应结构变化，我需要修改 `EnumDefinitionResponseDto` 类。有几种方法可以实现：

1. 使用 `@JsonUnwrapped` 注解
2. 让类实现 Map 接口
3. 使用自定义的序列化器

我推荐第一种方法，使用 `@JsonUnwrapped` 注解，这是最简洁的方式。

---

```diff
  package space.lzhq.ph.dto.response;
  
+ import com.fasterxml.jackson.annotation.JsonUnwrapped;
  import io.swagger.v3.oas.annotations.media.Schema;
  import lombok.Data;
```

```diff
  public class EnumDefinitionResponseDto {
  
+     @JsonUnwrapped
      @Schema(description = "枚举定义映射表，键为枚举简名，值为枚举项列表", 
              example = """
```

```diff
      private Map<String, List<EnumItemDto>> enums = new HashMap<>();
  
+     /**
+      * 添加枚举定义
+      *
+      * @param enumName 枚举类型简名
+      * @param items    枚举项列表
+      */
      public void addEnum(String enumName, List<EnumItemDto> items) {
          this.enums.put(enumName, items);
```

---

现在让我们创建一个测试来验证这个修改是否正确工作：

---

Read file: ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java

---

<details>
            <summary>Grep search for "enums.*test" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "test.*enums" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L1123 | `void testGetEnumsPublic_Success() throws Exception {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L1124 | `logTestExecution("testGetEnumsPublic_Success");` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L1123 | `void testGetEnumsPublic_Success() throws Exception {` |
| `file:///Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java` | L1124 | `logTestExecution("testGetEnumsPublic_Success");` |

</details>

---

Read file: ruoyi-admin/src/test/java/space/lzhq/ph/controller/ChronicDiseaseRegistrationApiControllerTest.java

---

现在我需要修改测试以验证新的响应结构（直接在 `data` 下而不是 `data.enums` 下）：

---

```diff
  
          // 验证响应状态和基本结构
-         result.andExpect(expectSuccessResponse(DataExpectation.EXISTS))
-                 .andExpect(jsonPath("$.data.enums").exists());
+         result.andExpected(expectSuccessResponse(DataExpectation.EXISTS))
+                 .andExpect(jsonPath("$.data.enums").doesNotExist()); // 确保enums键不存在
  
-         // 验证InsuranceType枚举
-         result.andExpect(jsonPath("$.data.enums.InsuranceType").exists())
-                 .andExpect(jsonPath("$.data.enums.InsuranceType").isArray())
-                 .andExpect(jsonPath("$.data.enums.InsuranceType[?(@.code=='CITY_WORKER')].description").value("乌鲁木齐市职工医保"))
-                 .andExpect(jsonPath("$.data.enums.InsuranceType[?(@.code=='CITY_RESIDENT')].description").value("乌鲁木齐市居民医保"));
+         // 验证InsuranceType枚举直接在data下
+         result.andExpect(jsonPath("$.data.InsuranceType").exists())
+                 .andExpect(jsonPath("$.data.InsuranceType").isArray())
+                 .andExpect(jsonPath("$.data.InsuranceType[?(@.code=='CITY_WORKER')].description").value("乌鲁木齐市职工医保"))
+                 .andExpect(jsonPath("$.data.InsuranceType[?(@.code=='CITY_RESIDENT')].description").value("乌鲁木齐市居民医保"));
  
-         // 验证Relationship枚举
-         result.andExpect(jsonPath("$.data.enums.Relationship").exists())
-                 .andExpect(jsonPath("$.data.enums.Relationship").isArray())
-                 .andExpect(jsonPath("$.data.enums.Relationship[?(@.code=='FATHER')].description").value("父亲"))
-                 .andExpect(jsonPath("$.data.enums.Relationship[?(@.code=='MOTHER')].description").value("母亲"));
+         // 验证Relationship枚举直接在data下
+         result.andExpect(jsonPath("$.data.Relationship").exists())
+                 .andExpect(jsonPath("$.data.Relationship").isArray())
+                 .andExpect(jsonPath("$.data.Relationship[?(@.code=='FATHER')].description").value("父亲"))
+                 .andExpect(jsonPath("$.data.Relationship[?(@.code=='MOTHER')].description").value("母亲"));
  
-         // 验证ChronicDiseaseRegistrationAttachmentType枚举
-         result.andExpect(jsonPath("$.data.enums.ChronicDiseaseRegistrationAttachmentType").exists())
-                 .andExpect(jsonPath("$.data.enums.ChronicDiseaseRegistrationAttachmentType").isArray())
-                 .andExpect(jsonPath("$.data.enums.ChronicDiseaseRegistrationAttachmentType[?(@.code=='ID_CARD')].description").value("身份证"));
+         // 验证ChronicDiseaseRegistrationAttachmentType枚举直接在data下
+         result.andExpect(jsonPath("$.data.ChronicDiseaseRegistrationAttachmentType").exists())
+                 .andExpect(jsonPath("$.data.ChronicDiseaseRegistrationAttachmentType").isArray())
+                 .andExpect(jsonPath("$.data.ChronicDiseaseRegistrationAttachmentType[?(@.code=='ID_CARD')].description").value("身份证"));
  
-         // 验证ChronicDiseaseRegistrationStatus枚举
-         result.andExpect(jsonPath("$.data.enums.ChronicDiseaseRegistrationStatus").exists())
-                 .andExpect(jsonPath("$.data.enums.ChronicDiseaseRegistrationStatus").isArray())
-                 .andExpect(jsonPath("$.data.enums.ChronicDiseaseRegistrationStatus[?(@.code=='DRAFT')].description").value("草稿（待提交）"));
+         // 验证ChronicDiseaseRegistrationStatus枚举直接在data下
+         result.andExpect(jsonPath("$.data.ChronicDiseaseRegistrationStatus").exists())
+                 .andExpect(jsonPath("$.data.ChronicDiseaseRegistrationStatus").isArray())
+                 .andExpect(jsonPath("$.data.ChronicDiseaseRegistrationStatus[?(@.code=='DRAFT')].description").value("草稿（待提交）"));
  
-         System.out.println("枚举定义测试通过 - 成功获取所有枚举信息");
+         System.out.println("枚举定义测试通过 - 成功获取所有枚举信息，响应结构已优化");
      }
  
```

---

我发现在编辑中有一个拼写错误，需要修复：

---

```diff
  
          // 验证响应状态和基本结构
-         result.andExpected(expectSuccessResponse(DataExpectation.EXISTS))
+         result.andExpect(expectSuccessResponse(DataExpectation.EXISTS))
                  .andExpect(jsonPath("$.data.enums").doesNotExist()); // 确保enums键不存在
  
```

---

现在让我们运行测试来验证修改是否正确工作：

---

```bash
cd /Users/<USER>/IdeaProjects/zhangyi-xyd6fy && mvn test -Dtest=ChronicDiseaseRegistrationApiControllerTest#testGetEnumsPublic_Success -pl ruoyi-admin
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------------< com.ruoyi:ruoyi-admin >------------------------
[INFO] Building ruoyi-admin 4.7.7
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.8.0:properties (properties) @ ruoyi-admin ---
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ ruoyi-admin ---
[INFO] Copying 451 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- closure-compiler:2.25.0:minify (minify-js) @ ruoyi-admin ---
[INFO] Starting JavaScript task:
[INFO] Skipping the merge step...
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [index.js].
[INFO] Uncompressed size: 24045 bytes.
[INFO] Compressed size: 14588 bytes minified (4034 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [common.js].
[INFO] Uncompressed size: 23954 bytes.
[INFO] Compressed size: 13785 bytes minified (4831 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [ry-ui.js].
[INFO] Uncompressed size: 86697 bytes.
[INFO] Compressed size: 34683 bytes minified (9849 bytes gzipped).
[INFO] Force is enabled, skipping check for changed files.
[INFO] Creating the minified file [login.js].
[INFO] Uncompressed size: 2754 bytes.
[INFO] Compressed size: 1512 bytes minified (860 bytes gzipped).
[INFO] Processed 4 output files
[INFO] Created 4 output files
[INFO] 
[INFO] --- compiler:3.13.0:compile (java-compile) @ ruoyi-admin ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ ruoyi-admin -
--
[INFO] Copying 1 resource from src/test/resources to target/test-classes
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (java-test-compile) @ ruoyi-admin ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 27 source files with javac [debug release 21] to target/test-cl
asses
[INFO] /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/s
pace/lzhq/ph/RequireSessionB
ehaviorTest.java: /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/
test/java/space/lzhq/ph/RequireSessionBehaviorTest.java uses or overrides a depr
ecated API.                                             [INFO] /Users/<USER>
/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/src/test/java/space/lzhq/ph/RequireSess
ionB
ehaviorTest.java: Recompile with -Xlint:deprecation for details.
                            [INFO] 
[INFO] --- surefire:3.2.5:test (default-test) @ ruoyi-admin ---
[INFO] Using auto detected provider org.apache.maven.surefire.junitplatform.JUni
tPlatformProvider
[INFO] 
[INFO] -------------------------------------------------------
[INFO]  T E S T S
[INFO] -------------------------------------------------------
[INFO] Running space.lzhq.ph.controller.ChronicDiseaseRegistrationApiControllerT
est
12:23:42,179 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is l
ogback-classic version 1.5.1
8
                            12:23:42,180 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - Here is a list of configura
tors discovered as a service, by rank:
                            12:23:42,180 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 -   org.springframework.boot.
logging.logback.RootLogLevelConfigurator
                            12:23:42,180 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - They will be invoked in ord
er until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
                            12:23:42,180 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - Constructed configurator of
 type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
                            12:23:42,183 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - org.springframework.boot.lo
gging.logback.RootLogLevelConfigurator.configure() call lasted 0 milliseconds. E
xecutionStatus=INVOKE_NEXT_IF_ANY
                                                        12:23:42,183 |-INFO in c
h.qos.logback.classic.util.ContextInitializer@2c532cd8 - Trying to configure wit
h ch
.qos.logback.classic.joran.SerializedModelConfigurator
                            12:23:42,183 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - Constructed configurator of
 type class ch.qos.logback.classic.joran.SerializedModelConfigurator
                            12:23:42,185 |-INFO in ch.qos.logback.classic.Logger
Context[default] - Could NOT find resource [logback-test
.scmo]
                            12:23:42,185 |-INFO in ch.qos.logback.classic.Logger
Context[default] - Could NOT find resource [logback.scmo
]
                            12:23:42,185 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - ch.qos.logback.classic.jora
n.SerializedModelConfigurator.configure() call lasted 2 milliseconds. ExecutionS
tatus=INVOKE_NEXT_IF_ANY    12:23:42,185 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - Trying to configure with ch
.qos.logback.classic.util.DefaultJoranConfigurator
                            12:23:42,185 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - Constructed configurator of
 type class ch.qos.logback.classic.util.DefaultJoranConfigurator
                            12:23:42,185 |-INFO in ch.qos.logback.classic.Logger
Context[default] - Could NOT find resource [logback-test
.xml]
                            12:23:42,187 |-INFO in ch.qos.logback.classic.Logger
Context[default] - Found resource [logback.xml] at [file
:/Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admin/target/classes/logbac
k.xml]                      12:23:42,229 |-WARN in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Appender named [console] n
ot referenced. Skipping further processing.
                            12:23:42,229 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[GELF]
                            12:23:42,229 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [de.siegmar.logbackgelf.GelfUdpAppender]
                            12:23:42,233 |-WARN in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Ignoring unknown property 
[useCompression] in [de.siegmar.logbackgelf.GelfUdpAppender]
                            12:23:42,266 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[file_info]
                            12:23:42,266 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,268 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-info.log" substituted for "${log.path}/sys-info.log"
                            12:23:42,269 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-info.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-info.%d{yyyy-MM-d
d}.log"                     12:23:42,270 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@1368862151 - No compression will be used
12:23:42,279 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1368862151 - Wi
ll use the pattern logs/sys-
info.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,291 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-info.%d{yyyy-MM-dd}.log'.
                            12:23:42,291 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,295 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.294Z
                            12:23:42,295 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,295 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,298 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_info] - Active log file name: lo
gs/sys-info.log
                            12:23:42,298 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_info] - File property is set to 
[logs/sys-info.log]
                            12:23:42,298 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[file_error]
                            12:23:42,298 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,298 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-error.log" substituted for "${log.path}/sys-error.log"
                            12:23:42,298 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-error.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-error.%d{yyyy-MM
-dd}.log"                   12:23:42,298 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@922807452 - No compression will be used
12:23:42,299 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@922807452 - Wil
l use the pattern logs/sys-e
rror.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,299 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-error.%d{yyyy-MM-dd}.log'.
                            12:23:42,299 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,299 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.299Z
                            12:23:42,299 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,299 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,299 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_error] - Active log file name: l
ogs/sys-error.log
                            12:23:42,299 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_error] - File property is set to
 [logs/sys-error.log]
                            12:23:42,299 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[file_debug]
                            12:23:42,299 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,299 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-debug.log" substituted for "${log.path}/sys-debug.log"
                            12:23:42,299 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-debug.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-debug.%d{yyyy-MM
-dd}.log"                   12:23:42,300 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@33563464 - No compression will be used
12:23:42,300 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@33563464 - Will
 use the pattern logs/sys-de
bug.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,300 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-debug.%d{yyyy-MM-dd}.log'.
                            12:23:42,300 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,300 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.300Z
                            12:23:42,300 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,300 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,301 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_debug] - Active log file name: l
ogs/sys-debug.log
                            12:23:42,301 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_debug] - File property is set to
 [logs/sys-debug.log]
                            12:23:42,301 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[sys-user]
                            12:23:42,301 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,301 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-user.log" substituted for "${log.path}/sys-user.log"
                            12:23:42,301 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "logs
/sys-user.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-user.%d{yyyy-MM-d
d}.log"                     12:23:42,301 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@1494346128 - No compression will be used
12:23:42,301 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1494346128 - Wi
ll use the pattern logs/sys-
user.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,301 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-user.%d{yyyy-MM-dd}.log'.
                            12:23:42,301 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,301 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.301Z
                            12:23:42,301 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,301 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@294e5088 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,302 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[sys-user] - Active log file name: log
s/sys-user.log
                            12:23:42,302 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[sys-user] - File property is set to [
logs/sys-user.log]
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
com.ruoyi] to INFO
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
org.springframework] to WARN
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
space.lzhq.ph] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
org.mospital] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sdk.biz.err] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sdk.biz.info] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sdk.comm.err] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
cn.yqtl] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.RootLoggerModelHandler - Setting level of ROOT
 logger to INFO
                            12:23:42,302 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [file_info] to Logger[ROOT]
                            12:23:42,302 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [file_debug] to Logger[ROOT]
                            12:23:42,302 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [file_error] to Logger[ROOT]
                            12:23:42,302 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [GELF] to Logger[ROOT]
                            12:23:42,302 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sys-user] to DEBUG
                            12:23:42,302 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [sys-user] to Logger[sys-user]
                            12:23:42,302 |-INFO in ch.qos.logback.core.model.pro
cessor.DefaultProcessor@31000e60 - End of configuration.
12:23:42,303 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@1d470d0 -
Registering current configur
ation as safe fallback point
                            12:23:42,303 |-INFO in ch.qos.logback.classic.util.C
ontextInitializer@2c532cd8 - ch.qos.logback.classic.util
.DefaultJoranConfigurator.configure() call lasted 118 milliseconds. ExecutionSta
tus=DO_NOT_INVOKE_NEXT_IF_ANY
                                                        
12:23:42,516 |-WARN in ch.qos.logback.core.model.processor.AppenderModelHandler
- Appender named [console] n
ot referenced. Skipping further processing.
                            12:23:42,516 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[GELF]
                            12:23:42,516 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [de.siegmar.logbackgelf.GelfUdpAppender]
                            12:23:42,516 |-WARN in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Ignoring unknown property 
[useCompression] in [de.siegmar.logbackgelf.GelfUdpAppender]
                            12:23:42,516 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[file_info]
                            12:23:42,516 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,516 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-info.log" substituted for "${log.path}/sys-info.log"
                            12:23:42,516 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-info.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-info.%d{yyyy-MM-d
d}.log"                     12:23:42,516 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@1673389762 - No compression will be used
12:23:42,516 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1673389762 - Wi
ll use the pattern logs/sys-
info.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,517 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-info.%d{yyyy-MM-dd}.log'.
                            12:23:42,517 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,517 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.298Z
                            12:23:42,517 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,517 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,519 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_info] - Active log file name: lo
gs/sys-info.log
                            12:23:42,519 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_info] - File property is set to 
[logs/sys-info.log]
                            12:23:42,519 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[file_error]
                            12:23:42,519 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,519 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-error.log" substituted for "${log.path}/sys-error.log"
                            12:23:42,519 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-error.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-error.%d{yyyy-MM
-dd}.log"                   12:23:42,519 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@1855997464 - No compression will be used
12:23:42,519 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1855997464 - Wi
ll use the pattern logs/sys-
error.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,519 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-error.%d{yyyy-MM-dd}.log'.
                            12:23:42,519 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,519 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.299Z
                            12:23:42,519 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,519 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,520 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_error] - Active log file name: l
ogs/sys-error.log
                            12:23:42,520 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_error] - File property is set to
 [logs/sys-error.log]
                            12:23:42,520 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[file_debug]
                            12:23:42,520 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,520 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-debug.log" substituted for "${log.path}/sys-debug.log"
                            12:23:42,520 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-debug.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-debug.%d{yyyy-MM
-dd}.log"                   12:23:42,520 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@1842881670 - No compression will be used
12:23:42,520 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1842881670 - Wi
ll use the pattern logs/sys-
debug.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,520 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-debug.%d{yyyy-MM-dd}.log'.
                            12:23:42,520 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,521 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.301Z
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,521 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_debug] - Active log file name: l
ogs/sys-debug.log
                            12:23:42,521 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[file_debug] - File property is set to
 [logs/sys-debug.log]
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - Processing appender named 
[sys-user]
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderModelHandler - About to instantiate appen
der of type [ch.qos.logback.core.rolling.RollingFileAppender]
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-user.log" substituted for "${log.path}/sys-user.log"
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "logs
/sys-user.%d{yyyy-MM-dd}.log" substituted for "${log.path}/sys-user.%d{yyyy-MM-d
d}.log"                     12:23:42,521 |-INFO in c.q.l.core.rolling.TimeBasedR
ollingPolicy@1443335402 - No compression will be used
12:23:42,521 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1443335402 - Wi
ll use the pattern logs/sys-
user.%d{yyyy-MM-dd}.log for the active file
                            12:23:42,521 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - The date pattern i
s 'yyyy-MM-dd' from file name pattern 'logs/sys-user.%d{yyyy-MM-dd}.log'.
                            12:23:42,521 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Roll-over at midni
ght.
                            12:23:42,521 |-INFO in c.q.l.core.rolling.DefaultTim
eBasedFileNamingAndTriggeringPolicy - Setting initial pe
riod to 2025-05-30T03:23:42.302Z
                            12:23:42,521 |-INFO in ch.qos.logback.core.model.pro
cessor.ImplicitModelHandler - Assuming default type [ch.
qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.ModelInterpretationContext@58ec7116 - value "%d{H
H:mm:ss.SSS} [%thread] %-5level %logger{50} - [%method,%line] - %msg%n" substitu
ted for "${log.pattern}"    12:23:42,522 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[sys-user] - Active log file name: log
s/sys-user.log
                            12:23:42,522 |-INFO in ch.qos.logback.core.rolling.R
ollingFileAppender[sys-user] - File property is set to [
logs/sys-user.log]
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
com.ruoyi] to INFO
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating INFO level on
 Logger[com.ruoyi] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
org.springframework] to WARN
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating WARN level on
 Logger[org.springframework] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
space.lzhq.ph] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[space.lzhq.ph] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
org.mospital] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[org.mospital] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sdk.biz.err] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[sdk.biz.err] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sdk.biz.info] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[sdk.biz.info] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sdk.comm.err] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[sdk.comm.err] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
cn.yqtl] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[cn.yqtl] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.RootLoggerModelHandler - Setting level of ROOT
 logger to INFO
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating INFO level on
 Logger[ROOT] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [file_info] to Logger[ROOT]
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [file_debug] to Logger[ROOT]
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [file_error] to Logger[ROOT]
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [GELF] to Logger[ROOT]
                            12:23:42,522 |-INFO in ch.qos.logback.classic.model.
processor.LoggerModelHandler - Setting level of logger [
sys-user] to DEBUG
                            12:23:42,522 |-INFO in ch.qos.logback.classic.jul.Le
velChangePropagator@5a00eb1e - Propagating DEBUG level o
n Logger[sys-user] onto the JUL framework
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.AppenderRefModelHandler - Attaching appender name
d [sys-user] to Logger[sys-user]
                            12:23:42,522 |-INFO in ch.qos.logback.core.model.pro
cessor.DefaultProcessor@36fcf6c0 - End of configuration.
12:23:42,523 |-INFO in org.springframework.boot.logging.logback.SpringBootJoranC
onfigurator@1aac188d - Regis
tering current configuration as safe fallback point
                            
Application Version: 4.7.7
Spring Boot Version: 3.4.5
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.8 
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader clas
ses because bootstrap classp
ath has been appended
                            执行集成测试: testGetEnumsPublic_Success

MockHttpServletRequest:
      HTTP Method = GET
      Request URI = /api/chronic-disease-registration/enums
       Parameters = {}
          Headers = [Content-Type:"application/json"]
             Body = <no character encoding set>
    Session Attrs = {}

Handler:
             Type = org.springframework.web.servlet.resource.ResourceHttpRequest
Handler

Async:
    Async started = false
     Async result = null

Resolved Exception:
             Type = org.springframework.web.servlet.resource.NoResourceFoundExce
ption

ModelAndView:
        View name = null
             View = null
            Model = null

FlashMap:
       Attributes = null

MockHttpServletResponse:
           Status = 200
    Error message = null
          Headers = [Vary:"Origin", "Access-Control-Request-Method", "Access-Con
trol-Request-Headers", Conte
nt-Type:"application/json"]
                                 Content type = application/json
             Body = {"msg":"系统繁忙，请稍后重试","traceId":"9700bf14d10a269a9700bf14d10a2
69a","code":500}
    Forwarded URL = null
   Redirected URL = null
          Cookies = []
[ERROR] Tests run: 1, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 35.73 s
<<< FAILURE! -- in space.lzh
q.ph.controller.ChronicDiseaseRegistrationApiControllerTest
                            [ERROR] space.lzhq.ph.controller.ChronicDiseaseRegis
trationApiControllerTest.testGetEnumsPublic_Success -- T
ime elapsed: 0.260 s <<< FAILURE!
                            java.lang.AssertionError: JSON path "$.code" expecte
d:<0> but was:<500>
        at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.ja
va:61)
        at org.springframework.test.util.AssertionErrors.assertEquals(AssertionE
rrors.java:128)
        at org.springframework.test.util.JsonPathExpectationsHelper.assertValue(
JsonPathExpectationsHelper.j
ava:172)
                                    at org.springframework.test.web.servlet.resu
lt.JsonPathResultMatchers.lambda$value$2(JsonPathResultM
atchers.java:112)
                                    at space.lzhq.ph.TestHelper.lambda$expectSuc
cessResponse$0(TestHelper.java:328)
        at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java
:214)
        at space.lzhq.ph.controller.ChronicDiseaseRegistrationApiControllerTest.
testGetEnumsPublic_Success(C
hronicDiseaseRegistrationApiControllerTest.java:1133)
                                    at java.base/java.lang.reflect.Method.invoke
(Method.java:580)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

[INFO] 
[INFO] Results:
[INFO] 
[ERROR] Failures: 
[ERROR]   ChronicDiseaseRegistrationApiControllerTest.testGetEnumsPublic_Success
:1133 JSON path "$.code" exp
ected:<0> but was:<500>
                            [INFO] 
[ERROR] Tests run: 1, Failures: 1, Errors: 0, Skipped: 0
[INFO] 
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  42.040 s
[INFO] Finished at: 2025-05-30T12:24:17+09:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-surefire-plugin:3.
2.5:test (default-test) on p
roject ruoyi-admin: There are test failures.
                            [ERROR] 
[ERROR] Please refer to /Users/<USER>/IdeaProjects/zhangyi-xyd6fy/ruoyi-admi
n/target/surefire-reports fo
r the individual test results.
                            [ERROR] Please refer to dump files (if any exist) [d
ate].dump, [date]-jvmRun[N].dump and [date].dumpstream.
[ERROR] -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e swit
ch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please rea
d the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureExc
eption

```

---

测试失败了，返回了500错误。看起来是因为 `@JsonUnwrapped` 可能没有按预期工作，或者可能需要检查 Jackson 的配置。让我们检查响应并调试这个问题：

---

```bash
mvn spring-boot:run -pl ruoyi-admin &
```

---

让我尝试另一种方法来解决这个问题。`@JsonUnwrapped` 可能在这种情况下不起作用。让我创建一个更简单的解决方案，直接让 DTO 类继承 `HashMap`：

---

```diff
  package space.lzhq.ph.dto.response;
  
- import com.fasterxml.jackson.annotation.JsonUnwrapped;
  import io.swagger.v3.oas.annotations.media.Schema;
- import lombok.Data;
- import lombok.NoArgsConstructor;
  
  import java.util.HashMap;
  import java.util.List;
- import java.util.Map;
  
  /**
   * 枚举定义响应DTO
   * 用于返回多个枚举类型的完整定义信息
   */
- @Data
- @NoArgsConstructor
  @Schema(description = "枚举定义响应")
- public class EnumDefinitionResponseDto {
- 
-     @JsonUnwrapped
-     @Schema(description = "枚举定义映射表，键为枚举简名，值为枚举项列表", 
-             example = """
-             {
-               "InsuranceType": [
-                 {"code": "CITY_WORKER", "description": "乌鲁木齐市职工医保"},
-                 {"code": "CITY_RESIDENT", "description": "乌鲁木齐市居民医保"}
-               ],
-               "Relationship": [
-                 {"code": "FATHER", "description": "父亲"},
-                 {"code": "MOTHER", "description": "母亲"}
-               ]
-             }
-             """)
-     private Map<String, List<EnumItemDto>> enums = new HashMap<>();
+ public class EnumDefinitionResponseDto extends HashMap<String, List<EnumItemDto>> {
  
      /**
```

```diff
       */
      public void addEnum(String enumName, List<EnumItemDto> items) {
-         this.enums.put(enumName, items);
+         this.put(enumName, items);
      }
  
```

---

现在让我们运行测试来验证修改是否正确：

---

```bash
mvn test -Dtest=ChronicDiseaseRegistrationApiControllerTest#testGetEnumsPublic_Success -pl ruoyi-admin
```

---

