package space.lzhq.ph.common

import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import java.math.BigDecimal

fun WxPayBillInfo.isMenzhen(): Boolean = outTradeNo.startsWith(ServiceType.MZ.name)
fun WxPayBillInfo.isZhuyuan(): Boolean = outTradeNo.startsWith(ServiceType.ZY.name)
fun WxPayBillInfo.isPay(): Boolean = refundId.isNullOrBlank()
fun WxPayBillInfo.isRefund(): Boolean = !isPay()
fun WxPayBillResult.setPayAmount(payAmount: BigDecimal) {
    this.totalAmount = payAmount.toString()
}

fun WxPayBillResult.getPayAmount(): BigDecimal = this.totalAmount.toBigDecimal()
fun WxPayBillResult.setRefundAmount(refundAmount: BigDecimal) {
    this.totalRefundFee = refundAmount.toString()
}

fun WxPayBillResult.getRefundAmount(): BigDecimal = this.totalRefundFee.toBigDecimal()
