package space.lzhq.ph.common

import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.math.Money
import org.dromara.hutool.core.text.StrUtil
import org.dromara.hutool.core.util.RandomUtil
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.atomic.AtomicInteger

object PaymentKit {
    // 微信支付统一下单接口的商户订单号，最大长度为32，参见：https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_1
    // 建设银行聚合支付统一下单接口的商户订单号，最大长度为30，参见：cn.doit.zhangyi.pay.ccb.UnifiedOrderRequest
    fun newOrderId(type: ServiceType): String =
        type.name + DateUtil.format(Date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomNumbers(4)

    // 微信支付申请退款接口的商户退款单号，最大长度为64，参见：https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_4
    fun newRefundNo(type: ServiceType, receiptNo: String): String =
        type.name +
                DateUtil.format(Date(), DatePattern.PURE_DATE_PATTERN) +
                RandomUtil.randomNumbers(2) +
                StrUtil.padAfter(receiptNo, 8, "0")

    fun fenToYuan(fen: Long): BigDecimal {
        return Money(BigDecimal.valueOf(fen / 100.0)).amount
    }

    fun yuanToFen(yuan: BigDecimal): Long {
        return Money(yuan.setScale(2)).cent
    }

    fun yuanToFen(yuan: Double): Long {
        return yuanToFen(yuan.toBigDecimal())
    }

    fun diffInFen(yuan1: BigDecimal, yuan2: BigDecimal): Long {
        return yuanToFen(yuan1) - yuanToFen(yuan2)
    }

    /**
     * HIS充值退款服务连续失败的次数
     */
    val CONTINUOUS_HIS_FAILURES: AtomicInteger = AtomicInteger(0)

    /**
     * 比较两个以逗号分隔的结算单号字符串是否包含相同的结算单号
     *
     * @param settlementIds1 第一个结算单号字符串，多个用逗号分隔
     * @param settlementIds2 第二个结算单号字符串，多个用逗号分隔
     * @return 如果两个字符串包含相同的结算单号集合则返回true，否则返回false
     */
    @JvmStatic
    fun containsSameSettlementIds(settlementIds1: String?, settlementIds2: String?): Boolean {
        // 如果两个字符串都为空，则认为相同
        if (settlementIds1.isNullOrBlank() && settlementIds2.isNullOrBlank()) {
            return true
        }

        // 如果只有一个为空，则认为不同
        if (settlementIds1.isNullOrBlank() || settlementIds2.isNullOrBlank()) {
            return false
        }

        // 将两个字符串转换为集合，并进行排序比较
        val set1 = settlementIds1.split(org.dromara.hutool.core.text.StrUtil.COMMA)
            .map { it.trim() }
            .filter { it.isNotBlank() }
            .toSet()

        val set2 = settlementIds2.split(org.dromara.hutool.core.text.StrUtil.COMMA)
            .map { it.trim() }
            .filter { it.isNotBlank() }
            .toSet()

        // 比较两个集合是否相同
        return set1 == set2
    }

}