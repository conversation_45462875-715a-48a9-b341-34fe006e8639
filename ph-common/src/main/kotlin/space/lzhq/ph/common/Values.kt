package space.lzhq.ph.common

import com.ruoyi.common.core.domain.AjaxResult
import org.dromara.hutool.json.JSONUtil

object Values {
    const val CLIENT_SESSION = "client_session"
    const val CLIENT_SESSION_LOADED = "client_session_loaded"
    const val CURRENT_PATIENT = "current_patient"
    const val CURRENT_PSC_NURSE = "current_psc_nurse"
    const val CURRENT_PSC_CARER = "current_psc_carer"
    var INDEXED_DEPARTMENTS: List<Map<String, Any>> = emptyList()

    val INVALID_CLIENT_SESSION_AJAX_RESULT: String = JSONUtil.toJsonStr(AjaxResult.error("会话无效，请重新登录"))
    val REQUIRE_WEIXIN_SESSION_AJAX_RESULT: String = JSONUtil.toJsonStr(AjaxResult.error("请在微信中打开"))
    val REQUIRE_ALIPAY_SESSION_AJAX_RESULT: String = JSONUtil.toJsonStr(AjaxResult.error("请在支付宝中打开"))
    val REQUIRE_ACTIVE_PATIENT_AJAX_RESULT: String = JSONUtil.toJsonStr(AjaxResult.error("获取默认就诊人失败"))
    val REQUIRE_ZHUYUAN_PATIENT_AJAX_RESULT: String = JSONUtil.toJsonStr(AjaxResult.error("请绑定住院号"))
    val INVALID_TOKEN_AJAX_RESULT: AjaxResult = AjaxResult.error("Token 无效")
}
