package space.lzhq.ph.common

import com.baomidou.mybatisplus.core.toolkit.support.SFunction
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper

/**
 * MyBatis Plus 查询条件扩展
 */

fun <T> LambdaQueryChainWrapper<T>.likeIfPresent(column: SFunction<T, *>, value: String?): LambdaQueryChainWrapper<T> {
    if (!value.isNullOrBlank()) {
        this.like(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.inIfPresent(
    column: SFunction<T, *>,
    values: Collection<*>?
): LambdaQueryChainWrapper<T> {
    if (!values.isNullOrEmpty()) {
        this.`in`(column, values)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.inIfPresent(
    column: SFunction<T, *>,
    vararg values: Any?
): LambdaQueryChainWrapper<T> {
    if (values.isNotEmpty() && values.all { it != null }) {
        this.`in`(column, *values)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.eqIfPresent(column: SFunction<T, *>, value: Any?): LambdaQueryChainWrapper<T> {
    if (value != null) {
        this.eq(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.neIfPresent(column: SFunction<T, *>, value: Any?): LambdaQueryChainWrapper<T> {
    if (value != null) {
        this.ne(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.gtIfPresent(column: SFunction<T, *>, value: Any?): LambdaQueryChainWrapper<T> {
    if (value != null) {
        this.gt(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.geIfPresent(column: SFunction<T, *>, value: Any?): LambdaQueryChainWrapper<T> {
    if (value != null) {
        this.ge(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.ltIfPresent(column: SFunction<T, *>, value: Any?): LambdaQueryChainWrapper<T> {
    if (value != null) {
        this.lt(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.leIfPresent(column: SFunction<T, *>, value: Any?): LambdaQueryChainWrapper<T> {
    if (value != null) {
        this.le(column, value)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.betweenIfPresent(
    column: SFunction<T, *>,
    value1: Any?,
    value2: Any?
): LambdaQueryChainWrapper<T> {
    when {
        value1 != null && value2 != null -> this.between(column, value1, value2)
        value1 != null -> this.ge(column, value1)
        value2 != null -> this.le(column, value2)
    }
    return this
}

fun <T> LambdaQueryChainWrapper<T>.betweenIfPresent(
    column: SFunction<T, *>,
    values: Array<*>?
): LambdaQueryChainWrapper<T> {
    if (values != null && values.size >= 2) {
        return betweenIfPresent(column, values[0], values[1])
    }
    return this
}
