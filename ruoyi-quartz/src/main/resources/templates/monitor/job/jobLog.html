<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('定时任务日志列表')}"/>
</head>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="jobLog-form">
                <div class="select-list">
                    <ul>
                        <li>
                            任务名称：<input type="text" name="jobName" th:value="${job!=null?job.jobName:''}"/>
                        </li>
                        <li>
                            任务分组：<select name="jobGroup" th:with="type=${@dict.getType('sys_job_group')}">
                            <option value="">所有</option>
                            <th:block th:if="${job==null}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </th:block>
                            <th:block th:if="${job!=null}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}" th:field="*{job.jobGroup}"></option>
                            </th:block>
                        </select>
                        </li>
                        <li>
                            执行状态：<select name="status" th:with="type=${@dict.getType('sys_common_status')}">
                            <option value="">所有</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                        </li>
                        <li class="select-time">
                            <label>执行时间： </label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endTime]"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="monitor:job:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-danger" onclick="$.operate.clean()" shiro:hasPermission="monitor:job:remove">
                <i class="fa fa-trash"></i> 清空
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="monitor:job:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-danger" onclick="closeItem()">
                <i class="fa fa-reply-all"></i> 关闭
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var detailFlag = [[${@permission.hasPermi('monitor:job:detail')}]];
    var statusDatas = [[${@dict.getType('sys_common_status')}]];
    var groupDatas = [[${@dict.getType('sys_job_group')}]];
    var prefix = ctx + "monitor/jobLog";

    $(function () {
        var options = {
            url: prefix + "/list",
            cleanUrl: prefix + "/clean",
            detailUrl: prefix + "/detail/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            sortName: "createTime",
            sortOrder: "desc",
            modalName: "调度日志",
            columns: [{
                checkbox: true
            },
                {
                    field: 'jobLogId',
                    title: '日志编号'
                },
                {
                    field: 'jobName',
                    title: '任务名称'
                },
                {
                    field: 'jobGroup',
                    title: '任务分组',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(groupDatas, value);
                    }
                },
                {
                    field: 'invokeTarget',
                    title: '调用目标字符串',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'jobMessage',
                    title: '日志信息'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-warning btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.jobLogId + '\')"><i class="fa fa-search"></i>详细</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>
