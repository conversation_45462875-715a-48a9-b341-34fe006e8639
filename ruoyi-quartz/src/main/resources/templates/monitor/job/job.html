<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('定时任务列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="job-form">
                <div class="select-list">
                    <ul>
                        <li>
                            任务名称：<input type="text" name="jobName"/>
                        </li>
                        <li>
                            任务分组：<select name="jobGroup" th:with="type=${@dict.getType('sys_job_group')}">
                            <option value="">所有</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                        </li>
                        <li>
                            任务状态：<select name="status" th:with="type=${@dict.getType('sys_job_status')}">
                            <option value="">所有</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="monitor:job:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasPermission="monitor:job:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="monitor:job:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="monitor:job:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary" onclick="javascript:cron()">
                <i class="fa fa-code"></i> 生成表达式
            </a>
            <a class="btn btn-info" onclick="javascript:jobLog()" shiro:hasPermission="monitor:job:detail">
                <i class="fa fa-list"></i> 日志
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var detailFlag = [[${@permission.hasPermi('monitor:job:detail')}]];
    var editFlag = [[${@permission.hasPermi('monitor:job:edit')}]];
    var removeFlag = [[${@permission.hasPermi('monitor:job:remove')}]];
    var statusFlag = [[${@permission.hasPermi('monitor:job:changeStatus')}]];
    var datas = [[${@dict.getType('sys_job_group')}]];
    var prefix = ctx + "monitor/job";

    $(function () {
        var options = {
            url: prefix + "/list",
            detailUrl: prefix + "/detail/{id}",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            sortName: "createTime",
            sortOrder: "desc",
            modalName: "任务",
            columns: [{
                checkbox: true
            },
                {
                    field: 'jobId',
                    title: '任务编号'
                },
                {
                    field: 'jobName',
                    title: '任务名称',
                },
                {
                    field: 'jobGroup',
                    title: '任务分组',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(datas, value);
                    }
                },
                {
                    field: 'invokeTarget',
                    title: '调用目标字符串',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'cronExpression',
                    title: '执行表达式'
                },
                {
                    visible: statusFlag == 'hidden' ? false : true,
                    title: '任务状态',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return statusTools(row);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="$.operate.edit(\'' + row.jobId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.jobId + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        var more = [];
                        more.push("<a class='btn btn-default btn-xs " + statusFlag + "' href='javascript:void(0)' onclick='run(" + row.jobId + ")'><i class='fa fa-play-circle-o'></i> 执行一次</a> ");
                        more.push("<a class='btn btn-default btn-xs " + detailFlag + "' href='javascript:void(0)' onclick='$.operate.detail(" + row.jobId + ")'><i class='fa fa-search'></i>任务详细</a> ");
                        more.push("<a class='btn btn-default btn-xs " + detailFlag + "' href='javascript:void(0)' onclick='jobLog(" + row.jobId + ")'><i class='fa fa-list'></i>调度日志</a>");
                        actions.push('<a class="btn btn-info btn-xs" role="button" data-container="body" data-placement="left" data-toggle="popover" data-html="true" data-trigger="hover" data-content="' + more.join('') + '"><i class="fa fa-chevron-circle-right"></i>更多操作</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    /* 调度任务状态显示 */
    function statusTools(row) {
        if (row.status == 1) {
            return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="start(\'' + row.jobId + '\', \'' + row.jobGroup + '\')"></i> ';
        } else {
            return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="stop(\'' + row.jobId + '\', \'' + row.jobGroup + '\')"></i> ';
        }
    }

    /* 立即执行一次 */
    function run(jobId) {
        $.modal.confirm("确认要立即执行一次任务吗？", function () {
            $.operate.post(prefix + "/run", {"jobId": jobId});
        })
    }

    /* 调度任务-停用 */
    function stop(jobId, jobGroup) {
        $.modal.confirm("确认要停用任务吗？", function () {
            $.operate.post(prefix + "/changeStatus", {"jobId": jobId, "jobGroup": jobGroup, "status": 1});
        })
    }

    /* 调度任务-启用 */
    function start(jobId, jobGroup) {
        $.modal.confirm("确认要启用任务吗？", function () {
            $.operate.post(prefix + "/changeStatus", {"jobId": jobId, "jobGroup": jobGroup, "status": 0});
        })
    }

    /* 调度日志查询 */
    function jobLog(jobId) {
        var url = ctx + 'monitor/jobLog';
        if ($.common.isNotEmpty(jobId)) {
            url += '?jobId=' + jobId;
        }
        $.modal.openTab("调度日志", url);
    }

    /* cron表达式生成 */
    function cron() {
        var url = prefix + '/cron';
        var height = $(window).height() - 50;
        top.layer.open({
            maxmin: true,
            title: "Cron表达式生成器",
            type: 2,
            area: ['800px', height + "px"], //宽高
            shadeClose: true,
            content: url
        });
    }
</script>
</body>
</html>
