create table ph_nat_sample
(
    id                int auto_increment comment 'ID'
        primary key,
    code              varchar(20)                            not null comment '混样编码',
    person_id         int                                    not null comment '样品人编号',
    person_department int                                    not null comment '样品人科室',
    person_name       varchar(20)                            not null comment '样品人姓名',
    person_id_card_no varchar(18)                            not null comment '样品人身份证号',
    person_mobile     varchar(11)                            not null comment '样品人手机号',
    person_nation     int          default 1                 not null comment '样品人民族',
    person_type       varchar(2)                             not null comment '样品人类型',
    person_days       int                                    not null comment '几天一检',
    person_address    varchar(100) default ''                not null comment '样品人现住址',
    collector_name    varchar(20)                            not null comment '采样人姓名',
    collector_mobile  varchar(11)                            not null comment '采样人手机号',
    collect_address   varchar(50)                            not null comment '采样地点',
    collect_time      datetime     default CURRENT_TIMESTAMP not null comment '采样时间',
    push_state        int          default 0                 not null comment '推送状态:0=未推送,1=推送成功,2=推送失败',
    push_time         datetime                               null comment '推送时间',
    push_msg          varchar(100) default ''                not null comment '推送状态描述',
    mix_type          int          default 0                 not null comment '混样类型',
    constraint ph_nat_sample_person_id_card_no_code_uindex
        unique (person_id_card_no, code)
)
    comment '样品';

create index ph_nat_sample_collect_time_index
    on ph_nat_sample (collect_time);

